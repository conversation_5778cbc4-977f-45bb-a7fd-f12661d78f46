import type { LocationQuery } from "vue-router";
import { useUserStore } from "@/stores/modules/user";
import { findNameByPath } from "@/routers";
import { getEnv } from "./index";

interface LoginParams {
  path: string;
}

export const getAuth = ({ path }: LoginParams) => {
  const appInfo = JSON.parse(localStorage.getItem("appInfo") || "{}");
  const { corpId, agentId, loginType, scope = "snsapi_privateinfo" } = appInfo;

  if (!loginType) {
    const env = getEnv();
    qwOauth2(path, corpId, agentId, scope, env);
  } else {
    // 统一身份认证
    authentication(path);
  }
};

const qwOauth2 = (path: string, corpId: string, agentId: string, scope: string, type: string) => {
  markAuth(true);
  const title = document.title ?? "";
  const { title: _title, code, ...rest } = getQueryParams(window.location.href);
  console.log(code);
  const paramsArray = [] as string[];
  Object.keys(rest).forEach(v => {
    paramsArray.push(`${v}=${rest[v]}`);
  });
  let paramsStr = paramsArray.join("&");
  paramsStr = _title ? paramsStr : paramsStr + `&title=${title}`;
  const pathName = import.meta.env.VITE_PATH_NAME;
  const url = `${window.location.origin}/${pathName}${path}?${paramsStr}`;
  const redirectUrl = encodeURIComponent(url); // 重定向url
  let openUrl = "";
  if (type === "wxwork") {
    openUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUrl}&response_type=code&scope=${scope}&state=STATE&agentid=${agentId}#wechat_redirect`;
  }
  if (type === "wechat") {
    openUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`;
  }
  window.location.replace(openUrl);
};

// 统一身份认证
export function authentication(path: string) {
  markAuth(true);
  const { code, ...rest } = getQueryParams(window.location.href);
  const localStorageAppInfo = JSON.parse(localStorage.getItem("appInfo") || "{}");
  const newAppInfo = Object.assign(localStorageAppInfo, rest);
  console.log(code);
  updateAppInfoBySearchParams(newAppInfo);
  const pathName = import.meta.env.VITE_PATH_NAME;
  const servicePath = `${pathName}/${String(findNameByPath(path))}`;
  const openUrl = `${window.location.origin}/auth/cas/sso?servicePath=${servicePath}`;
  window.location.replace(openUrl);
}

// 标记授权
export const markAuth = (flag: boolean) => {
  if (flag) window.sessionStorage.setItem("hasAuth", "1");
  else window.sessionStorage.removeItem("hasAuth");
};

export const removeQueryParam = (url: string, key: string) => {
  if (url.includes(key)) {
    // 使用URL对象处理URL
    const urlObj = new URL(url);
    urlObj.searchParams.delete(key);
    // 将URL对象转换回字符串
    const newUrl = urlObj.toString();
    return newUrl;
  } else {
    return url;
  }
};

export const updateAppInfoBySearchParams = (query: LocationQuery) => {
  // query:地址栏查询参数
  const localStorageAppInfo = JSON.parse(localStorage.getItem("appInfo") || "{}");
  const newAppInfo = Object.assign(localStorageAppInfo, { loginType: "" }, query);
  localStorage.setItem("appInfo", JSON.stringify(newAppInfo));
  // 存储当前登录的应用id和企业id 用于获取用户隐私信息和注册sdk
  const userStore = useUserStore();
  userStore.setWeChat(query);
  if (query.agentId && userStore.tokenInfo?.token) {
    userStore.listenSharing();
  }
};

export const getQueryParams = (fullPath: string) => {
  // 提取路径中的查询字符串
  const queryString = fullPath.split("?")[1];
  if (!queryString) {
    return {};
  }

  // 将查询字符串转换为对象
  return JSON.parse('{"' + decodeURIComponent(queryString).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
};
