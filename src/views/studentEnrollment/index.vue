<template>
  <div class="min-h-screen pb-8">
    <!-- 学院下拉 -->
    <div class="flex flex-row items-center">
      <div class="flex items-center py-[12px] px-[16px] gap-[4px]" @click="withPiker.show = true">
        <div>{{ currentSelected?.label || "-" }}</div>
        <img src="@/assets/images/eBike/icon_arrow_down.svg" alt="" />
      </div>
      <div class="flex-1"></div>
      <div v-if="classCode" class="flex items-center py-[12px] px-[16px] gap-[4px]" @click="jumpDetail()">
        <div class="text-[#4C94F3] text-[12px]">详情</div>
        <van-icon name="arrow" color="#4C94F3" />
      </div>
    </div>

    <!-- 指标卡片 -->
    <div class="flex flex-row items-center px-[15px] pb-[16px]">
      <div class="flex-1 flex flex-row items-end left">
        <div class="flex-1 flex flex-col px-[16px] py-[16px]">
          <div class="text-[#1D2023] text-[18px] font-semibold">{{ totalStudent }}</div>
          <span class="text-[#979DAB] text-[13px] mt-[6px]">新生总人数</span>
        </div>
        <img src="@/assets/images/studentEnrollment/icon_1.svg" alt="" />
      </div>
      <div class="flex-1 flex flex-row items-end right ml-[9px]">
        <div class="flex-1 flex flex-col px-[16px] py-[16px]">
          <div class="text-[#1D2023] text-[18px] font-semibold">{{ signedStudent }}</div>
          <span class="text-[#979DAB] text-[13px] mt-[6px]">已签到人数</span>
        </div>
        <img src="@/assets/images/studentEnrollment/icon_2.svg" alt="" />
      </div>
    </div>

    <div class="h-[8px] bg-[#F7F8FA]"></div>

    <!-- 当前报到率 -->
    <div class="chart-panel">
      <div class="flex items-center px-[12px] mt-[16px]">
        <div class="w-[3px] h-[15px] bg-[#4C94F3] mr-[4px]"></div>
        <div class="font-semibold text-[#1D2023] text-[16px]">当前报到率</div>
      </div>
      <div class="h-[180px] relative">
        <ECharts :option="pieOption" />
        <div class="center-info">
          <p class="text-[#5C6371] text-[12px]">总计</p>
          <p class="text-[#1D2023] text-[20px] font-semibold">{{ totalStudent ?? "" }}</p>
        </div>
      </div>
    </div>
    <!-- 报道率排行 -->
    <div v-if="barData.length > 0">
      <div class="h-[8px] bg-[#F7F8FA]"></div>
      <div class="flex items-center px-[12px] mt-[16px]">
        <div class="w-[3px] h-[15px] bg-[#4C94F3] mr-[4px]"></div>
        <div class="font-semibold text-[#1D2023] text-[16px]">报道率排行</div>
      </div>
      <div :style="{ height: barChartHeight + 'px' }">
        <ECharts :option="barOption" />
      </div>
    </div>
    <van-popup
      v-model:show="withPiker.show"
      class="overflow-hidden flex flex-col"
      :style="{
        height: '50%',
        '--van-picker-title-font-size': '18px',
        '--van-picker-action-font-size': '16px',
        '--van-picker-option-font-size': '22px'
      }"
      position="bottom"
      safe-area-inset-bottom
    >
      <div class="flex items-center justify-between">
        <div class="text-[#1D2023] text-[14px] px-[16px] py-[16px]" @click="withPiker.show = false">取消</div>
        <div class="text-[#4C94F3] text-[14px] px-[16px] py-[16px] font-semibold" @click="onNodeSelectConfirm">确定</div>
      </div>
      <div class="h-[1px] bg-[#F7F8FA]"></div>
      <div class="flex-1 overflow-auto">
        <fs-tree ref="treeRef" :data="treeData" @on-select-nodes="onNodeSelect"></fs-tree>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts" name="StudentEnrollmentIndex">
import FsTree from "./FsTree/FsTree.vue";
import { computed, onMounted, reactive, ref } from "vue";
import type { ECOption } from "@/components/ECharts/config";
import * as echarts from "echarts/core";
import { showLoadingToast } from "vant";
import { getCollegeListApi, getDashboardApi } from "@/api/modules/studentEnrollment";
import { ITreeItem } from "@/views/studentEnrollment/FsTree/types";
import { StudentEnrollment } from "@/api/modules/studentEnrollment/type";
import router from "@/routers";

const barChartHeight = ref(0);

const currentSelected = ref<{ key: string; label: string } | null>(null);
const withPiker = reactive<{
  show: boolean;
}>({
  show: false
});

const tempSelected = ref<{ key: string; label: string } | null>(null);
const onNodeSelect = data => {
  if (data && data.length > 0) {
    tempSelected.value = { label: data[0].label, key: data[0].key };
  }
};

const classCode = ref<string>("");
const onNodeSelectConfirm = () => {
  currentSelected.value = tempSelected.value;
  withPiker.show = false;
  const code = currentSelected?.value?.key ?? "-1";
  if (code.includes(":c")) {
    // 只有选择到班级层级才会有查看详情
    classCode.value = code.replace(":c", "");
  } else {
    classCode.value = "";
  }
  loadInfo();
};

const jumpDetail = () => {
  router.push({ name: "StudentEnrollmentClassDetail", query: { classId: classCode.value, className: currentSelected?.value?.label ?? "-" } });
};

const totalStudent = ref(0);
const signedStudent = ref(0);

// 饼图
const pieColor = ref<string[]>([]);
const pieData = ref<{ value: number; name: string }[]>([]);
const pieOption = computed<ECOption>(() => {
  return {
    series: [
      {
        silent: true,
        legendHoverLink: false,
        color: "#F7F8FA",
        type: "pie",
        radius: ["35%", "65%"],
        data: [100],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        selectedMode: false
      },
      {
        silent: true,
        legendHoverLink: false,
        color: pieColor.value,
        type: "pie",
        radius: ["40%", "60%"],
        data: pieData.value,
        labelLine: {
          length: 16,
          length2: 10,
          lineStyle: {
            color: "#E8E8E8"
          },
          smooth: true,
          smoothness: 0.6
        },
        label: {
          show: true,
          color: "#979DAB"
        }
      }
    ]
  };
});

// 水平条形图
const barData = ref<number[]>([]);
const barNames = ref<string[]>([]);
const barOption = computed<ECOption>(() => {
  return {
    grid: {
      top: 10,
      bottom: 10,
      left: 16,
      right: 56,
      containLabel: true
    },
    xAxis: {
      type: "value",
      max: 100,
      axisLabel: {
        formatter: "{value}%"
      },
      splitLine: {
        lineStyle: {
          color: "#EFEFF1"
        }
      },
      show: true
    },
    yAxis: {
      type: "category",
      inverse: true,
      data: barNames.value,
      axisTick: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: "#9CA3AF"
      }
    },
    series: [
      {
        type: "bar",
        data: barData.value,
        barWidth: "46%",
        itemStyle: {
          borderRadius: 6,
          color: new (echarts as any).graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#66B3FF" },
            { offset: 1, color: "#4B9CFF" }
          ])
        },
        showBackground: true,
        backgroundStyle: {
          borderRadius: 6,
          color: new (echarts as any).graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: "#F8F8F8" },
            { offset: 1, color: "#F8F8F8" }
          ])
        },
        label: {
          show: true,
          position: "right",
          formatter: "{c}%",
          color: "#6B7280"
        }
      }
    ]
  } as ECOption;
});

async function loadData() {
  await loadCollegeList();
  await loadInfo();
}

async function loadInfo() {
  const toast = showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    duration: 0
  });
  try {
    const param: { classCode?: string; majorCode?: string; collegeCode?: string } = {};
    const code = currentSelected?.value?.key ?? "-1";
    if (code !== "-1") {
      if (code.includes(":c")) {
        param.classCode = code.replace(":c", "");
      } else if (code.includes(":m")) {
        param.majorCode = code.replace(":m", "");
      } else {
        param.collegeCode = code;
      }
    }
    const { data } = await getDashboardApi(param);
    totalStudent.value = data?.totalStudents ?? 0;
    signedStudent.value = data?.checkedInCount ?? 0;
    pieData.value = [];
    const unreportedCount = (data.totalReportCount ?? 0) - (data.reportedCount ?? 0);
    if (unreportedCount > 0) {
      pieData.value.push({
        value: unreportedCount,
        name: `未报到${unreportedCount}`
      });
      pieColor.value.push("rgba(91, 143, 249, 0.85)");
    }
    if ((data.reportedCount ?? 0) > 0) {
      pieData.value.push({
        value: data.reportedCount ?? 0,
        name: `已报到${data.reportedCount ?? 0}`
      });
      pieColor.value.push("rgba(90, 216, 166, 0.85)");
    }
    barData.value = [];
    barNames.value = [];
    data.collegeReportRanks?.forEach(rank => {
      barData.value.push(rank.reportRate ?? 0);
      barNames.value.push(rank.collegeName ?? "-");
    });
    barChartHeight.value = barData.value.length * 40 + 30;
    toast.close();
  } catch (e) {
    //
  }
}

async function loadCollegeList() {
  try {
    const { data } = await getCollegeListApi();
    buildTreeData(data.colleges);
  } catch (e) {
    //
  }
}
const treeData = ref<ITreeItem[]>([]);

function buildTreeData(colleges: StudentEnrollment.ICollege[]) {
  const tree: ITreeItem[] = [];
  colleges.forEach(college => {
    const children: ITreeItem[] = [];
    const majors = college.majors;
    majors.forEach(major => {
      const classes: ITreeItem[] = major.classes.map(item => {
        return {
          key: item.classCode + ":c", // 加一个后缀，防止与专业/院校重复
          label: item.className,
          isLeaf: true
        };
      });
      children.push({
        key: major.majorCode + ":m", // 加一个后缀，防止与班级/院校重复
        label: major.majorName,
        children: classes
      });
    });

    tree.push({
      key: college.collegeCode,
      label: college.collegeName,
      children
    });
  });

  treeData.value.push({
    key: "-1",
    label: "沈阳职业学院",
    children: tree
  });
  currentSelected.value = { label: (treeData?.value[0]?.label as string) ?? "", key: (treeData?.value[0]?.key as string) ?? "-1" };
}

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.left {
  background: linear-gradient(314deg, #c7eaff 0%, #f1f9ff 100%);
  border-radius: 8px;
}
.right {
  background: linear-gradient(312deg, #c8fdf1 0%, #f1fffc 100%);
  border-radius: 8px;
}
.chart-panel {
  margin-top: 8px;
  background: #ffffff;
  border-radius: 8px;
  .center-info {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100px;
    margin: 0 auto;
    text-align: center;
  }
}
</style>
