<template>
  <div class="min-h-screen bg-[#F7F8FA] flex flex-col overflow-hidden">
    <!-- 班级标题 -->
    <div class="px-[15px] py-[8px] text-[15px] font-semibold text-[#1D2023]">{{ className }}</div>
    <div class="flex-1 flex flex-col overflow-auto px-[16px] gap-[8px]">
      <div v-for="student in students" :key="student.xh" class="flex flex-col bg-white rounded-[5px] px-[16px] py-[16px] gap-[6px]">
        <div class="flex items-start justify-between">
          <div class="flex items-center">
            <div class="font-semibold text-[15px] text-[#1D2023]">{{ student.name }}</div>
            <div class="text-[14px] text-[#979DAB] ml-[8px]">{{ student.xh }}</div>
          </div>
          <span class="text-[12px] px-[5px] py-[1px] rounded-[3px]" :class="student.checkInStatus ? 'bg-[#1BC91A14] text-[#1BC91A]' : 'bg-[#FA203A14] text-[#FA203A]'">
            {{ student.checkInStatus ? "已签到" : "未签到" }}
          </span>
        </div>
        <div class="text-[13px] text-[#5C6371]">
          企微状态：
          <span :class="student.wechatStatus ? 'text-[#1D2023]' : 'text-[#FA203A]'">
            {{ student.wechatStatus ? "已加入" : "未加入" }}
          </span>
        </div>
        <div class="text-[13px] text-[#5C6371]">
          基本信息：
          <span :class="student.basicInfoStatus ? 'text-[#1D2023]' : 'text-[#FA203A]'">
            {{ student.basicInfoStatus ? "已填写" : "未填写" }}
          </span>
        </div>
        <div class="text-[13px] text-[#5C6371]">
          交通信息：
          <span :class="student.trafficInfoStatus ? 'text-[#1D2023]' : 'text-[#FA203A]'">
            {{ student.trafficInfoStatus ? "已填写" : "未填写" }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { getStudentDetailApi } from "@/api/modules/studentEnrollment";
import { StudentEnrollment } from "@/api/modules/studentEnrollment/type";
import { showLoadingToast } from "vant";

const route = useRoute();
const className = route.query.className || "XX2601班";
const students = ref<StudentEnrollment.IStudentInfo[]>([]);
async function loadData() {
  const toast = showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    duration: 0
  });
  try {
    const params = { classCode: route.query.classId };
    const { data } = await getStudentDetailApi(params);
    students.value = data ?? [];
    toast.close();
  } catch (e) {
    //
  }
}

onMounted(() => {
  loadData();
});
</script>

<style scoped></style>
