<script setup lang="ts">
import { useRoute } from "vue-router";
import { getWxAuthorizationUrl } from "@/api/modules/login";
import { getQueryParams } from "@/utils/login";

const route = useRoute();
const params = getQueryParams(route.fullPath);

const { appid, redirect_uri: redirectUri, scope, state, agentid: agentId } = params;

const pathName = import.meta.env.VITE_PATH_NAME;

// 使用encodeURIComponent确保URL参数正确编码
const callbackUrl = `${window.location.origin}/${pathName}/ls-login?agentid=${agentId}&callbackUrl=${encodeURIComponent(redirectUri || "")}`;
const openUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(callbackUrl)}&response_type=code&scope=${scope}&state=${state}&agentid=${agentId}#wechat_redirect`;

async function getRedirectUrl() {
  try {
    const { data } = await getWxAuthorizationUrl({
      agentId,
      corpId: appid,
      redirectUri: callbackUrl,
      scope: scope || "snsapi_base",
      state
    });
    window.location.replace(data);
  } catch (error) {
    console.log({ error });
  }
}

if (appid) {
  window.location.replace(openUrl);
} else {
  getRedirectUrl();
}
</script>
