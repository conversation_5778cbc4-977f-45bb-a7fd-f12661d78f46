<script setup lang="ts">
import { getTicketByCode } from "@/api/modules/login";
import { onMounted } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const { callbackUrl, appid, agentid, code } = route.query;
async function sendTicket() {
  try {
    const { data } = await getTicketByCode({ code, agentId: agentid, corpId: appid });
    // 解码callbackUrl并构建最终URL
    const decodedCallbackUrl = decodeURIComponent(callbackUrl as string);
    const url = decodedCallbackUrl.includes("?") ? `${decodedCallbackUrl}&ticket=${data}` : `${decodedCallbackUrl}?ticket=${data}`;
    window.location.replace(url);
  } catch (error) {
    console.debug({ error });
  }
}

onMounted(sendTicket);
</script>
