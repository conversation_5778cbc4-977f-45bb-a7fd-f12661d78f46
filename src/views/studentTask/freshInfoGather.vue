<template>
  <div class="gatherContainer">
    <div class="questionArea">
      <van-form @submit="handleSubmit" :disabled="finishStatus === '2'">
        <div class="questionAreaTop">
          <div v-for="question in questions" :key="question.id" class="questionItem">
            <div class="questionTitle">
              <span :class="question.isRequred ? 'isRequired' : 'isNotRequired'">*</span>
              <span>{{ question.name }}</span>
              <span>{{ question.label }}</span>
            </div>
            <div class="questionField">
              <van-field
                class="inputClass"
                v-if="question.type === 'input'"
                v-model="answers[question.keyEn]"
                :placeholder="'请输入' + question.label + (question.plusLable ? question.plusLable : '')"
                required
                autocomplete="off"
                :disabled="question.disabled || finishStatus === '2'"
                :rules="question.rule ? question.rule : [{ required: true, message: '请填写' + `${question.label}` }]"
              />
              <van-field v-else-if="question.type === 'radio'" class="radioClass" :rules="[{ required: true, message: '请至少选择一项' }]">
                <template #input>
                  <van-radio-group :disabled="finishStatus === '2'" v-model="answers[question.keyEn]" class="radioGroupClass">
                    <van-radio v-for="opt in question.options" :key="opt.value" :name="opt.value">{{ opt.label }}</van-radio>
                  </van-radio-group>
                </template>
              </van-field>

              <van-field
                :disabled="finishStatus === '2'"
                v-else-if="question.type === 'focusSelect'"
                v-model="answers[question.keyEn]"
                autocomplete="off"
                :placeholder="'请选择' + question.label"
                required
                @click="handleFocusSelect(question)"
                :rules="[{ required: true, message: '请选择' + `${question.label}` }]"
                @focus="handleFocus"
                class="inputClass"
              />
              <van-field v-else-if="question.type === 'multi-select'" :disabled="finishStatus === '2'" :rules="[{ required: true, message: '请至少选择一项' }]">
                <template #input>
                  <van-checkbox-group
                    :disabled="finishStatus === '2'"
                    v-model="answers[question.keyEn]"
                    required
                    shape="square"
                    class="radioGroupClass"
                    @change="handleMultiSelectChange"
                    :rules="[{ required: true, message: '请填写' + `${question.label}` }]"
                  >
                    <van-checkbox :disabled="item.disabled" v-for="item in question.options" :key="item.value" :name="item.value">{{ item.name }}</van-checkbox>
                  </van-checkbox-group>
                </template>
              </van-field>
              <template v-if="question.type === 'listEdit'">
                <van-list>
                  <van-cell-group class="cellGroupClass">
                    <van-row class="table-row" justify="center">
                      <van-col span="6" class="table-cell">姓名</van-col>
                      <van-col span="6" class="table-cell">关系</van-col>
                      <van-col span="9" class="table-cell">手机号</van-col>
                      <van-col span="3" class="table-cell">操作</van-col>
                    </van-row>
                    <van-row v-for="(item, index) in question.options" :key="item.id" class="table-row" justify="center">
                      <van-col span="6" class="table-cell2">
                        <van-field autocomplete="off" v-model="item.name" required />
                      </van-col>
                      <van-col span="6" class="table-cell2">
                        <van-field autocomplete="off" v-model="item.relationCHN" required @click="handleFocusSelect(question, index)" @focus="handleFocus" right-icon="arrow-down" />
                      </van-col>
                      <van-col span="9" class="table-cell2">
                        <van-field autocomplete="off" v-model="item.phone" required />
                      </van-col>
                      <van-col span="3" class="table-cell2">
                        <span @click="handleDeleteRelation(index)"><van-icon name="close" color="#ee0a24" /></span>
                      </van-col>
                    </van-row>
                    <van-row class="table-row" justify="center">
                      <van-col style="color: #409eff" span="24" class="table-cell" @click="handleAddOneOption"><van-icon name="plus" />新增一行</van-col>
                    </van-row>
                    <van-row justify="start">
                      <van-col span="24">
                        <p class="warning" v-if="!question.options.length">请至少完成一项</p>
                      </van-col>
                    </van-row>
                  </van-cell-group>
                </van-list>
              </template>
            </div>
          </div>
        </div>
        <div class="questionAreaBottom">
          <van-button type="primary" round block native-type="submit" :disabled="finishStatus === '2'">提交</van-button>
        </div>
      </van-form>
    </div>

    <van-popup v-model:show="focusSelectBirthAddressVisible" position="bottom" :style="{ height: '40%' }">
      <van-picker :columns="focusSelectOptions" @confirm="onFocusSelectConfirm" :model-value="activeColumnValue" :columns-field-names="columnsFieldNames" @cancel="focusSelectBirthAddressVisible = false" />
    </van-popup>

    <van-popup v-model:show="focusSelectNationVisible" position="bottom" :style="{ height: '40%' }">
      <van-picker :columns="focusSelectOptions" @confirm="onFocusSelectConfirm" :model-value="activeColumnValue" :columns-field-names="columnsFieldNames" @cancel="focusSelectNationVisible = false" />
    </van-popup>
    <van-popup v-model:show="focusSelectDateVisible" position="bottom" :style="{ height: '40%' }">
      <van-date-picker title="选择出生日期" v-model="currentDate" @cancel="focusSelectDateVisible = false" @confirm="handleSelectDateConfirm" :min-date="minBirthday" />
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, toRaw } from "vue";
import { showToast, showSuccessToast, showFailToast, showConfirmDialog } from "vant";
import { getStudentInfoApiWithParams, saveStudentInfoApi, finishStuTaskApi, getByNewStudentDataId } from "@/api/modules/meeting";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/stores/modules/user";

// 类型定义
interface ParentContactInfo {
  id: number | string;
  name: string;
  relationship: number | string;
  relationCHN: string;
  phone: string;
}
interface Question {
  id: number;
  name: string;
  label: string;
  keyEn: string;
  value?: any;
  isRequred: boolean;
  type: string;
  options?: any[];
  plusLable?: string;
  rule?: any[];
  disabled?: boolean;
}
interface Answers {
  [key: string]: any;
  name: string;
  gender: string;
  politicalStatus: string;
  nationality: string;
  birthDate: string;
  idCardNumber: string;
  phone: string;
  permanentAddress: string;
  nativePlace: string;
  parentContactInfo: ParentContactInfo[];
  householdAddress: string;
  studentCategory: string;
  accidentInsuranceFlag: number | string;
  height: string;
  weight: string;
  shoeSize: string;
  dailyNecessities: number[];
}

const router = useRouter();
const routes = useRoute();

const finishStatus = ref(String(routes.query.finishStatus ?? "2")); // 统一为字符串
const minBirthday = ref(new Date(1960, 0, 1));
const currentDate = ref(["2000", "01", "01"]);
const activeColumnValue = ref<string[]>([]);
const focusSelectNationVisible = ref(false);
const focusSelectDateVisible = ref(false);
const focusSelectBirthAddressVisible = ref(false);
const focusSelectOptions = ref<any[]>([]);
const userInfo = useUserStore().userInfo;
const userId = ref(userInfo.userId);
const primaryKey = ref();
const columnsFieldNames = reactive({ text: "name", value: "value" });
const activeQuestionKeyEn = ref("");
const activeRelationOptionIndex = ref(0);

const genderOption = [
  { label: "男", value: "男" },
  { label: "女", value: "女" }
];
const hasBoughtAccidentEnsuranceOptions = [
  { label: "是", value: 1 },
  { label: "否", value: 0 }
];
const identityOption = [
  { label: "党员", value: "党员" },
  { label: "共青团员", value: "共青团员" },
  { label: "群众", value: "群众" }
];
// nationOptions, provinceOptions, dailyNecessityOptions 保持不变
const nationOptions = [
  { name: "汉族", value: 1 },
  { name: "壮族", value: 2 },
  { name: "维吾尔族", value: 3 },
  { name: "回族", value: 4 },
  { name: "苗族", value: 5 },
  { name: "满族", value: 6 },
  { name: "彝族", value: 7 },
  { name: "土家族", value: 8 },
  { name: "藏族", value: 9 },
  { name: "蒙古族", value: 10 },
  { name: "布依族", value: 11 },
  { name: "侗族", value: 12 },
  { name: "瑶族", value: 13 },
  { name: "白族", value: 14 },
  { name: "哈尼族", value: 15 },
  { name: "朝鲜族", value: 16 },
  { name: "黎族", value: 17 },
  { name: "哈萨克族", value: 18 },
  { name: "傣族", value: 19 },
  { name: "东乡族", value: 20 },
  { name: "傈僳族", value: 21 },
  { name: "畲族", value: 22 },
  { name: "仡佬族", value: 23 },
  { name: "拉祜族", value: 24 },
  { name: "水族", value: 25 },
  { name: "高山族", value: 26 },
  { name: "佤族", value: 27 },
  { name: "纳西族", value: 28 },
  { name: "羌族", value: 29 },
  { name: "土族", value: 30 },
  { name: "仫佬族", value: 31 },
  { name: "柯尔克孜族", value: 32 },
  { name: "锡伯族", value: 33 },
  { name: "撒拉族", value: 34 },
  { name: "景颇族", value: 35 },
  { name: "达斡尔族", value: 36 },
  { name: "布朗族", value: 37 },
  { name: "毛南族", value: 38 },
  { name: "塔吉克族", value: 39 },
  { name: "普米族", value: 40 },
  { name: "阿昌族", value: 41 },
  { name: "怒族", value: 42 },
  { name: "鄂温克族", value: 43 },
  { name: "京族", value: 44 },
  { name: "基诺族", value: 45 },
  { name: "保安族", value: 46 },
  { name: "德昂族", value: 47 },
  { name: "俄罗斯族", value: 48 },
  { name: "裕固族", value: 49 },
  { name: "乌兹别克族", value: 50 },
  { name: "门巴族", value: 51 },
  { name: "鄂伦春族", value: 52 },
  { name: "独龙族", value: 53 },
  { name: "赫哲族", value: 54 },
  { name: "珞巴族", value: 55 },
  { name: "塔塔尔族", value: 56 }
];
const provinceOptions = [
  { name: "北京市", value: 1 },
  { name: "天津市", value: 2 },
  { name: "河北省", value: 3 },
  { name: "山西省", value: 4 },
  { name: "内蒙古自治区", value: 5 },
  { name: "辽宁省", value: 6 },
  { name: "吉林省", value: 7 },
  { name: "黑龙江省", value: 8 },
  { name: "上海市", value: 9 },
  { name: "江苏省", value: 10 },
  { name: "浙江省", value: 11 },
  { name: "安徽省", value: 12 },
  { name: "福建省", value: 13 },
  { name: "江西省", value: 14 },
  { name: "山东省", value: 15 },
  { name: "河南省", value: 16 },
  { name: "湖北省", value: 17 },
  { name: "湖南省", value: 18 },
  { name: "广东省", value: 19 },
  { name: "广西壮族自治区", value: 20 },
  { name: "海南省", value: 21 },
  { name: "重庆市", value: 22 },
  { name: "四川省", value: 23 },
  { name: "贵州省", value: 24 },
  { name: "云南省", value: 25 },
  { name: "西藏自治区", value: 26 },
  { name: "陕西省", value: 27 },
  { name: "甘肃省", value: 28 },
  { name: "青海省", value: 29 },
  { name: "宁夏回族自治区", value: 30 },
  { name: "新疆维吾尔自治区", value: 31 },
  { name: "香港特别行政区", value: 32 },
  { name: "澳门特别行政区", value: 33 },
  { name: "台湾省", value: 34 }
];
const dailyNecessityOptions = [
  { name: "床上用品（9件套，包括:棉被、夏凉被、褥子、枕头、床单、被置、枕巾等） （380元）", value: 1 },
  { name: "暖瓶 （30元）", value: 2 },
  { name: "洗头膏 （26.5元）", value: 3 },
  { name: "毛巾 （15元）", value: 4 },
  { name: "排插 （42元）", value: 5 },
  { name: "挂框 （42元）", value: 6 },
  { name: "水杯 （15元）", value: 7 },
  { name: "不购买任何生活用品", value: 8 }
];

const stuTypes = reactive([
  { name: "单招", value: 1 },
  { name: "统招", value: 2 },
  { name: "3 + 2", value: 3 }
]);

const familyLinkWayOptions =
  finishStatus.value === "2"
    ? ref<ParentContactInfo[]>([])
    : ref<ParentContactInfo[]>([
        {
          id: 1,
          name: "",
          relationship: "",
          relationCHN: "",
          phone: ""
        }
      ]);
const linkOptions = [
  { name: "父子", id: 1, value: 1 },
  { name: "母子", id: 2, value: 2 },
  { name: "其他", id: 3, value: 3 }
];
const linkOptionsCP = ref([...linkOptions]);

const idCardRule = [
  { required: true, message: "请输入身份证号" },
  { pattern: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}([0-9Xx])$/, message: "输入的身份证有误" }
];
const phoneRule = [
  { required: true, message: "请输入电话号码" },
  { validator: (val: string) => val.length === 11, message: "输入的电话号码有误" }
];

const answers = ref<Answers>({
  name: "",
  gender: "",
  politicalStatus: "",
  nationality: "",
  birthDate: "",
  idCardNumber: "",
  phone: "",
  permanentAddress: "",
  nativePlace: "",
  parentContactInfo: [],
  householdAddress: "",
  studentCategory: "",
  accidentInsuranceFlag: "",
  height: "",
  weight: "",
  shoeSize: "",
  dailyNecessities: []
});

const questions = ref<Question[]>([
  { id: 1, name: "01", label: "姓名", keyEn: "name", isRequred: true, type: "input", disabled: true },
  { id: 2, name: "02", label: "性别", keyEn: "gender", isRequred: true, type: "radio", options: genderOption },
  { id: 3, name: "03", label: "政治面貌", keyEn: "politicalStatus", isRequred: true, type: "radio", options: identityOption },
  { id: 4, name: "04", label: "民族", keyEn: "nationality", isRequred: true, type: "focusSelect", options: nationOptions },
  { id: 5, name: "05", label: "出生日期", keyEn: "birthDate", isRequred: true, type: "focusSelect" },
  { id: 6, name: "06", label: "身份证号", keyEn: "idCardNumber", isRequred: true, type: "input", rule: idCardRule },
  { id: 7, name: "07", label: "联系电话", keyEn: "phone", plusLable: "（大陆电话）", isRequred: true, type: "input", rule: phoneRule },
  { id: 8, name: "08", label: "常住家庭地址", plusLable: "（精确到省份）", keyEn: "permanentAddress", isRequred: true, type: "input" },
  { id: 9, name: "09", label: "出生地", keyEn: "nativePlace", isRequred: true, type: "focusSelect", options: provinceOptions },
  { id: 10, name: "10", label: "家长联系方式", keyEn: "parentContactInfo", isRequred: true, type: "listEdit", options: familyLinkWayOptions.value },
  { id: 11, name: "11", label: "户口所在地", keyEn: "householdAddress", isRequred: true, type: "input" },
  { id: 12, name: "12", label: "学生类别", keyEn: "studentCategory", isRequred: true, type: "focusSelect", options: stuTypes },
  { id: 13, name: "13", label: "是否有意外保险（如未参保，建议入校前参保完毕）", keyEn: "accidentInsuranceFlag", isRequred: true, type: "radio", options: hasBoughtAccidentEnsuranceOptions },
  { id: 14, name: "14", label: "身高（cm）", keyEn: "height", isRequred: true, type: "input" },
  { id: 15, name: "15", label: "体重（kg）", keyEn: "weight", isRequred: true, type: "input" },
  { id: 16, name: "16", label: "鞋码（例：42码）", keyEn: "shoeSize", isRequred: true, type: "input" },
  { id: 17, name: "17", label: "[多选]是否购买生活用品", keyEn: "dailyNecessities", isRequred: true, type: "multi-select", options: dailyNecessityOptions }
]);

const handleAddOneOption = () => {
  if (finishStatus.value === "2") return;
  const options = questions.value[9].options as ParentContactInfo[];
  options.push({ id: options.length + 1, name: "", relationship: "", relationCHN: "", phone: "" });
};
const handleDeleteRelation = (index: number) => {
  if (finishStatus.value === "2") return;
  const options = questions.value[9].options as ParentContactInfo[];
  options.splice(index, 1);
  handleFilterLink();
};

const handleFocusSelect = (question: Question, index?: number) => {
  if (finishStatus.value === "2") {
    return;
  }
  activeQuestionKeyEn.value = question.keyEn;
  if (question.keyEn === "nationality") {
    focusSelectNationVisible.value = true;
    focusSelectOptions.value = nationOptions;
  } else if (question.keyEn === "nativePlace") {
    focusSelectBirthAddressVisible.value = true;
    focusSelectOptions.value = provinceOptions;
  } else if (question.keyEn === "birthDate") {
    focusSelectDateVisible.value = true;
  } else if (question.keyEn === "studentCategory") {
    focusSelectNationVisible.value = true;
    focusSelectOptions.value = stuTypes;
  } else if (question.keyEn === "parentContactInfo") {
    focusSelectOptions.value = linkOptionsCP.value;
    focusSelectNationVisible.value = true;
    if (typeof index === "number") {
      activeRelationOptionIndex.value = index;
    }
  }
};
const handleFilterLink = () => {
  const options = questions.value[9].options as ParentContactInfo[];
  let relationIds = new Set<number>();
  for (let i = 0; i < options.length; i++) {
    if (options[i].relationship !== 3) {
      relationIds.add(Number(options[i].relationship));
    }
  }
  linkOptionsCP.value = linkOptions.filter(item => !relationIds.has(item.id));
};

const onFocusSelectConfirm = ({ selectedOptions }: { selectedValues: any; selectedOptions: any[] }) => {
  if (finishStatus.value === "2") {
    return;
  }
  const selected = selectedOptions[0];
  if (activeQuestionKeyEn.value === "parentContactInfo") {
    const options = questions.value[9].options as ParentContactInfo[];
    options[activeRelationOptionIndex.value].relationCHN = selected.name;
    options[activeRelationOptionIndex.value].relationship = selected.id;
    answers.value[activeQuestionKeyEn.value][activeRelationOptionIndex.value] = {
      id: activeRelationOptionIndex.value,
      name: "",
      relationship: selected.id,
      relationCHN: selected.name,
      phone: ""
    };
    answers.value[activeQuestionKeyEn.value][activeRelationOptionIndex.value].relationship = selected.id;
  } else {
    answers.value[activeQuestionKeyEn.value] = selected.name;
  }
  handleFilterLink();
  focusSelectNationVisible.value = false;
  focusSelectBirthAddressVisible.value = false;
  console.log(answers);
};
const handleSelectDateConfirm = () => {
  answers.value[activeQuestionKeyEn.value] = currentDate.value.join("-");
  focusSelectDateVisible.value = false;
};

const handleMultiSelectChange = (values: number[]) => {
  let judge = true;
  if (values.includes(8)) {
    answers.value.dailyNecessities.length = 0;
    answers.value.dailyNecessities.push(8);
  } else {
    judge = false;
    answers.value.dailyNecessities = values;
  }
  for (let i in questions.value[16].options) {
    if (questions.value[16].options[i].value !== 8) {
      questions.value[16].options[i].disabled = judge;
    }
  }
};

const getStudentInfo = async () => {
  try {
    const { data, code } = await getStudentInfoApiWithParams({ userId: userId.value });
    if (Number(code) === 0) {
      const { name, id } = data;
      answers.value.name = name;
      primaryKey.value = id;
      if (finishStatus.value === "2") {
        getByNewStudentDataById();
      }
    }
  } catch {
    console.log("获取学员信息失败");
  }
};

const handleSubmit = () => {
  const options = questions.value[9].options as ParentContactInfo[];
  let valid = false;
  if (!options.length) {
    showToast(`请填写家庭关系`);
    return;
  } else {
    valid = true;
  }

  if (!valid) return;

  // 通过校验，继续提交
  showConfirmDialog({
    title: "提示",
    message: "填写完成后无法修改，请确认信息填写正确后提交"
  })
    .then(async () => {
      let parentContactInfo = JSON.parse(JSON.stringify(toRaw(options)));
      for (const key in parentContactInfo) {
        parentContactInfo[key].relationship = parentContactInfo[key].relationCHN;
        delete parentContactInfo[key].relationCHN;
        delete parentContactInfo[key].id;
      }
      let dailyNecessitiesSet = new Set([...answers.value.dailyNecessities]);
      let dailyNecessitiesCHN: string[] = [];
      for (let i in dailyNecessityOptions) {
        if (dailyNecessitiesSet.has(dailyNecessityOptions[i].value)) {
          dailyNecessitiesCHN.push(dailyNecessityOptions[i].name);
        }
      }
      let params = {
        ...answers.value,
        dailyNecessities: dailyNecessitiesCHN?.join(","),
        accidentInsuranceFlag: answers.value.accidentInsuranceFlag === 1,
        parentContactInfo,
        newStudentDataId: primaryKey.value
      };
      const { code, data } = await saveStudentInfoApi(params);
      if (data && Number(code) === 0) {
        const { code: code2, data: data2 } = await finishStuTaskApi(routes.query.taskId as string);
        if (data2 && Number(code2) === 0) {
          showSuccessToast("操作成功，完成任务");
          router.go(-1);
        }
      } else {
        showFailToast("操作失败");
      }
    })
    .catch(() => {
      // on cancel
    });
};
const getByNewStudentDataById = async () => {
  let { data } = await getByNewStudentDataId({ newStudentDataId: primaryKey.value });
  let dailyNecessitiesCP = data?.dailyNecessities.split(",");
  let dailyNecessities: number[] = [];
  for (let i in dailyNecessitiesCP) {
    for (let j in dailyNecessityOptions) {
      if (dailyNecessitiesCP[i] === dailyNecessityOptions[j].name) {
        dailyNecessities.push(dailyNecessityOptions[j].value);
      }
    }
  }

  let parentContactInfo = data.parentContactInfo;
  const options = questions.value[9].options as ParentContactInfo[];
  for (const key in parentContactInfo) {
    options.push({
      ...parentContactInfo[key],
      id: key,
      relationCHN: parentContactInfo[key].relationship
    });
    parentContactInfo[key].relationship = parentContactInfo[key].relationCHN;
    delete parentContactInfo[key].relationCHN;
    delete parentContactInfo[key].id;
  }

  answers.value = {
    ...data,
    accidentInsuranceFlag: data.accidentInsuranceFlag ? 1 : 0,
    dailyNecessities,
    parentContactInfo: options
  };
};

const handleFocus = () => {
  const activeElement = document.activeElement as HTMLElement;
  if (activeElement) {
    activeElement.blur();
  }
};

onMounted(() => {
  getStudentInfo();
});
</script>
<style lang="scss" scoped>
.gatherContainer {
  display: flex;
  flex-direction: column;

  //   padding: 12px;
  gap: 12px;
  min-height: 100vh;
  overflow: auto;
  background: #ffffff;
  .questionArea {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 12px;
    background: #f5f6fa;
    border-radius: 4px;
    .van-form {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 12px;
      overflow: auto;
      .questionAreaTop {
        display: flex;
        flex: 1 1 0;
        flex-direction: column;
        gap: 12px;
        overflow-y: auto;
      }
      .questionAreaBottom {
        flex: 0 0 40px;
      }
    }
    .questionItem {
      padding: 8px;
      background: #ffffff;
      border-radius: 4px;
      .questionTitle {
        padding-bottom: 4px;
        .isRequired {
          color: red;
        }
        .isNotRequired {
          display: none;
        }
        span {
          &:nth-of-type(2),
          &:nth-of-type(3) {
            font-size: 15px;
            font-weight: bold;
          }
          &:nth-of-type(3) {
            padding-left: 8px;
          }
        }
      }
      .questionField {
        font-size: 14px;
        .inputClass {
          // border: 1px solid #cccccc;
          // border-radius: 4px;
          padding: 0;
          :deep(.van-cell__value) {
            position: relative;
            .van-field__body {
              padding: 12px;
              border: 1px solid #cccccc;
              border-radius: 4px;
            }
          }
        }
        .radioClass {
          padding: 5px;
        }
        .radioGroupClass {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
        :deep(.van-list) {
          display: flex;
          justify-content: center;
          width: 100%;
          .table-cell {
            padding: 4px 0;
            text-align: center;
          }
          .cellGroupClass {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
          }
          .table-row .table-cell2 {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            .van-field__control {
              text-align: center;
            }
            .van-field__right-icon {
              position: relative;
              padding: 0;
              .van-icon {
                position: absolute;
                top: -10px;
                left: -10px;
                font-size: 14px;
              }
            }
          }
        }
        .warning {
          padding: 4px;
          font-size: 12px;
          color: #ee0a24;
        }
      }
    }
  }
}
.table-container {
  width: 80%;
  overflow-x: auto;
}

// .table-header {
//   background-color: #f2f2f2;
//   font-weight: bold;
// }
.table-row {
  // border-bottom: 1px solid #dddddd;
}

// .table-cell {
//   padding: 8px;
//   text-align: center;
// }
</style>
