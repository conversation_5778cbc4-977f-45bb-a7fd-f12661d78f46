<template>
  <div class="gather-page">
    <div class="user-info-box">
      <van-form @submit="onSubmit" ref="formRef" :disabled="isTaskCompleted">
        <div class="questionAreaTop">
          <div class="user-info">
            <div class="questionTitle"><span style="color: red">*</span>01.姓名</div>
            <van-field class="inputClass" v-model="name" required autocomplete="off" disabled />
          </div>
          <div class="user-info">
            <div class="questionTitle"><span style="color: red">*</span>02.预计报道时间</div>
            <van-field
              class="inputClass"
              label-width="115px"
              v-model="expectedArrivalDate"
              name="datePicker"
              placeholder="点击选择时间"
              @click="handleDatePickerClick"
              :disabled="isTaskCompleted"
              readonly
              :rules="[{ required: true, message: '请选择预计报道时间' }]"
            />
          </div>

          <div class="user-info">
            <div class="questionTitle"><span style="color: red">*</span>03.交通方式</div>
            <van-field class="time" label-width="115px" name="transportMethod" :disabled="isTaskCompleted">
              <template #input>
                <van-radio-group v-model="transportationType" class="radio-group" :class="{ 'radio-group--readonly': isTaskCompleted }">
                  <van-radio name="火车">火车</van-radio>
                  <van-radio name="飞机">飞机</van-radio>
                  <van-radio name="大巴">大巴</van-radio>
                  <van-radio name="驾车">驾车</van-radio>
                  <van-radio name="其它">其它</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </div>

          <div v-if="transportationType !== '大巴' && transportationType !== '驾车' && transportationType !== '其它'">
            <div class="user-info">
              <div class="questionTitle"><span style="color: red">*</span>04.车次/航班号</div>
              <van-field class="inputClass" label-width="115px" v-model="vehicleNumber" name="number" placeholder="请输入" :disabled="isTaskCompleted" :rules="numberRules" />
            </div>
          </div>

          <div v-if="transportationType !== '大巴' && transportationType !== '驾车' && transportationType !== '其它'">
            <div class="user-info">
              <div class="questionTitle"><span style="color: red">*</span>05.到达车站</div>
              <van-field class="time" label-width="115px" name="arrivalStation" :disabled="isTaskCompleted">
                <template #input>
                  <van-radio-group v-model="arrivalStation" class="radio-group" :class="{ 'radio-group--readonly': isTaskCompleted }">
                    <van-radio name="沈阳站">沈阳站</van-radio>
                    <van-radio name="沈阳北站">沈阳北站</van-radio>
                    <van-radio name="其它">其它</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field v-if="arrivalStation === '其它'" class="inputClass otherInputClass" label="其它车站地址" v-model="otherStation" name="otherStation" placeholder="请输入其它车站名称" :disabled="isTaskCompleted" :rules="otherStationRules" />
            </div>
          </div>

          <div class="user-info">
            <div class="questionTitle"><span style="color: red">*</span>{{ transportationType === "大巴" || transportationType === "驾车" || transportationType === "其它" ? "04.随行人员人数" : "06.随行人员人数" }}</div>
            <van-field class="inputClass" label-width="115px" v-model="personNumber" name="personNumber" placeholder="请输入随行人员人数" type="digit" :disabled="isTaskCompleted" :rules="personNumberRules" />
          </div>
        </div>
        <div class="questionAreaBottom">
          <van-button type="primary" round block :disabled="isTaskCompleted" :loading="submitLoading" @click="onSubmit"> 提交 </van-button>
        </div>
      </van-form>

      <van-popup v-model:show="showPicker" destroy-on-close position="bottom">
        <van-date-picker :model-value="pickerValue" :min-date="new Date()" @confirm="onConfirm" @cancel="showPicker = false" />
      </van-popup>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { showToast, showConfirmDialog, showSuccessToast } from "vant";
import { finishStuTaskApi, getStudentInfoApi, submitStuTaskFormApi, getMethodByNewStudentDataId } from "@/api/modules/studentEnrollment";
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();
const taskId = ref<string>("");
const name = ref<string>("");
const newStudentDataId = ref<string>("");

// 信息是否已经提交【已经提交的话，该页面仅显示，不能编辑和再次提交】
const isTaskCompleted = ref<boolean>(false);

onMounted(() => {
  taskId.value = route.query.taskId as string;
  getStudentInfo();
});

// 获取用户信息
const getStudentInfo = async () => {
  try {
    const { data, code } = await getStudentInfoApi();
    if (+code === 0) {
      name.value = data.name;
      newStudentDataId.value = String(data.id);
      await getTransportationInfo();
    }
  } catch (error) {
    console.log("error-------", error);
    console.log("获取学员信息失败1");
  }
};

// 获取采集到的交通信息
const getTransportationInfo = async () => {
  try {
    const { data, code } = await getMethodByNewStudentDataId({ newStudentDataId: newStudentDataId.value });
    if (+code === 0) {
      isTaskCompleted.value = true;
      expectedArrivalDate.value = data.expectedArrivalDate ? data.expectedArrivalDate : "";
      transportationType.value = data.transportationType ? data.transportationType : "";
      vehicleNumber.value = data.vehicleNumber ? data.vehicleNumber : "";
      arrivalStation.value = data.arrivalStation ? data.arrivalStation : "";
      otherStation.value = data.arrivalStationOther ? data.arrivalStationOther : "";
      personNumber.value = data.accompanyingPersonsCount ? data.accompanyingPersonsCount : "";
    } else {
      isTaskCompleted.value = false;
    }
  } catch (error) {
    isTaskCompleted.value = false;
    console.log("error-------", error);
    console.log("获取学员信息失败2");
  }
};

// 表单引用
const formRef = ref();

// 提交loading状态
const submitLoading = ref(false);

// 预计报道时间
const expectedArrivalDate = ref("");
const showPicker = ref(false);
const pickerValue = ref([]);
const onConfirm = ({ selectedValues }) => {
  expectedArrivalDate.value = selectedValues.join("-");
  pickerValue.value = selectedValues;
  showPicker.value = false;
};

// 处理日期选择器点击事件
const handleDatePickerClick = () => {
  if (!isTaskCompleted.value) {
    showPicker.value = true;
  }
};

// 交通方式
const transportationType = ref("火车");

// 车次/航班号
const vehicleNumber = ref("");

// 到达车站
const arrivalStation = ref("沈阳站");

// 其它车站地址
const otherStation = ref("");

// 随行人员人数
const personNumber = ref("");

const numberRules = computed(() => {
  if (transportationType.value === "驾车" || transportationType.value === "其它") {
    return [];
  }
  return [
    { required: true, message: "请输入车次/航班号" },
    {
      validator: (value: string) => {
        if (!value) return false;
        return value.trim().length > 0;
      },
      message: "车次/航班号不能为空格"
    }
  ];
});

const otherStationRules = computed(() => {
  if (arrivalStation.value !== "其它") {
    return [];
  }
  return [
    { required: true, message: "请输入其它车站名称" },
    {
      validator: (value: string) => {
        if (!value) return false;
        return value.trim().length > 0;
      },
      message: "车站名称不能为空格"
    }
  ];
});

const personNumberRules = [
  { required: true, message: "请输入随行人员人数" },
  {
    validator: (value: string) => {
      if (!value) return false;
      const num = parseInt(value);
      return !isNaN(num) && num >= 0 && num <= 20;
    },
    message: "随行人员人数必须是0-20之间的数字"
  }
];

// 提交表单
const onSubmit = async () => {
  try {
    submitLoading.value = true;

    // 验证表单
    await formRef.value?.validate();

    // 构建提交数据
    const submitData = {
      newStudentDataId: newStudentDataId.value,
      name: name.value,
      expectedArrivalDate: expectedArrivalDate.value,
      transportationType: transportationType.value,
      ...(transportationType.value !== "驾车" &&
        transportationType.value !== "其它" && {
          vehicleNumber: vehicleNumber.value,
          arrivalStation: arrivalStation.value,
          ...(arrivalStation.value === "其它" && { arrivalStationOther: otherStation.value })
        }),
      accompanyingPersonsCount: parseInt(personNumber.value)
    };

    // 通过校验，继续提交
    showConfirmDialog({
      title: "提示",
      message: "填写完成后无法修改，请确认信息填写正确后提交"
    })
      .then(async () => {
        // 这里可以调用API提交数据
        await submitStuTaskFormApi(submitData);
        await finishTask();

        showSuccessToast("操作成功，完成任务");
      })
      .catch(() => {
        // on cancel
      });
  } catch (error) {
    console.error("表单验证失败:", error);
    if (error instanceof Error) {
      showToast({
        type: "fail",
        message: error.message || "请检查填写的信息"
      });
    }
  } finally {
    submitLoading.value = false;
  }
};

// 完成任务
async function finishTask() {
  try {
    const { code } = await finishStuTaskApi(taskId.value);
    if (+code === 0) {
      console.log("完成任务");
      router.back();
    }
  } catch (error) {
    console.log("error-------", error);
  }
}
</script>

<style scoped lang="scss">
.gather-page {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 100vh;
  overflow: auto;
  background: #ffffff;
  .user-info-box {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 12px;
    background: #f5f6fa;
    border-radius: 4px;
    .questionAreaTop {
      display: flex;
      flex: 1 1 0;
      flex-direction: column;
      gap: 12px;
      overflow-y: auto;
      .inputClass {
        // border: 1px solid #cccccc;
        // border-radius: 4px;
        padding: 0;
        :deep(.van-cell__value) {
          position: relative;
          .van-field__body {
            padding: 12px;
            border: 1px solid #cccccc;
            border-radius: 4px;
          }
        }
      }
      .otherInputClass {
        :deep(.van-cell__title) {
          display: flex;
          align-items: center;
        }
        :deep(.van-field__body) {
          padding: 12px;
          border: 0 !important;
          border-bottom: 1px solid #cccccc !important;
          border-radius: 0 !important;
        }
      }
      .radioClass {
        padding: 5px;
      }
    }
    .questionAreaBottom {
      flex: 0 0 40px;
    }
    .van-form {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 12px;
      overflow: auto;
    }
    .form-name {
      padding: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background-color: #ffffff;
      border-radius: 8px;
    }
    .user-info {
      padding: 12px;
      font-size: 15px;
      font-weight: 500;
      color: #333333;
      background-color: #ffffff;
      border-radius: 4px;
      .questionTitle {
        padding-bottom: 4px;
        font-size: 15px;
        font-weight: bold;
      }
      .time {
        &::after {
          content: none;
        }
      }
    }
    .time {
      padding: 12px;
      margin-top: 12px;
      font-size: 15px;
      font-weight: 500;
      color: #333333;
      background-color: #ffffff;
      border-radius: 8px;

      // 禁用状态下的样式覆盖
      &.van-field--disabled {
        background-color: #f7f8fa;

        // 保持label颜色清晰
        :deep(.van-field__label) {
          color: #333333 !important; // 保持原有的深色
        }

        // 输入框内容颜色稍微淡一些但仍然可读
        :deep(.van-field__control) {
          color: #666666 !important;
          background-color: transparent;
        }

        // 禁用状态的输入框样式
        :deep(.van-field__control[disabled]) {
          color: #666666 !important;
          -webkit-text-fill-color: #666666 !important;
        }
      }
    }
    .radio-group {
      display: flex;
      flex-direction: column;
      gap: 12px; // 竖向间距12px

      // 只读状态下禁用点击但保持原有样式
      &.radio-group--readonly {
        pointer-events: none;
      }
    }
  }

  // 固定在底部的提交按钮样式
  .submit-button-container {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    padding: 16px 24px;
    background-color: #ffffff;
    border-top: 1px solid #ebedf0;
    box-shadow: 0 -2px 8px rgb(0 0 0 / 10%);
  }
}
</style>
