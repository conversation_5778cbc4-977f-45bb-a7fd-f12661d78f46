<template>
  <div class="page-container">
    <div class="Background-image" v-if="myServes[0]?.url">
      <van-image width="100%" :src="myServes[0]?.url" fit="cover" position="top" />
    </div>
    <div class="card">
      <div class="task-title">我的服务</div>
      <div class="service-wrapper">
        <div v-for="(item, index) in filteredServes" :key="index" class="service-item" @click="navigateToDetail(item)">
          <div class="service-img-box">
            <img class="service-img" :src="item.localImgUrl" alt="" />
            <div class="cardtitle" :style="{ color: getCardTitleColor(item.name) }">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="task-card">
      <div class="task-title">新生指引</div>
      <div class="task-tip">温馨提示：完成信息采集后才可以进行到校签到</div>
      <div v-for="(item, index) in taskList" :key="index" class="task-row">
        <div class="task-left">
          <img class="task-icon" :src="item?.iconUrl" alt="" />
          <span class="task-label">{{ item?.name }}</span>
        </div>
        <van-button
          v-if="shouldUseOriginalLogic(item)"
          :type="item?.finishStatus === 2 ? 'default' : 'primary'"
          size="small"
          class="task-btn"
          :style="item?.finishStatus === 2 ? { backgroundColor: '#E8E8E8', color: '#999999', border: 'none' } : {}"
          @click="handleTaskClick(item)"
        >
          {{ item?.finishStatus === 2 ? "已完成" : "去完成" }}
        </van-button>
        <van-button v-else :type="getTaskButtonType(item)" size="small" class="task-btn" :style="getTaskButtonStyle(item)" :disabled="isTaskDisabled(item)" @click="handleTaskClick(item)">
          {{ getTaskButtonText(item) }}
        </van-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getSysConfigVoByKeyApi, stuTaskPageApi, getFinishStuTaskApi, finishStuTaskApi } from "@/api/modules/studentEnrollment";
import { getSystemConfigApi } from "@/api/modules/customSetting";
import { onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { ButtonType } from "vant";

const router = useRouter();
const myServes = ref<any[]>([]);
const taskList = ref<any[]>([]);
const canDoStepFour = ref(false);
const projectName = ref<string>("");

// 类型定义
interface TaskItem {
  id: number;
  name: string;
  taskType: string;
  taskUrl: string;
  taskStatus: boolean;
  sort: number;
  finishStatus: number;
  iconUrl?: string;
}

// 获取系统配置
const getSystemConfig = async () => {
  try {
    const { data } = await getSystemConfigApi();
    projectName.value = data?.projectName || "";
    console.log("获取到的projectName:", projectName.value);
  } catch (error) {
    console.error("获取系统配置失败:", error);
    projectName.value = "";
  }
};

// 封装刷新数据的方法
const refreshData = () => {
  getMyServes();
  getTaskList();
};

onMounted(async () => {
  await getSystemConfig();
  refreshData();
});

// 动态导入图片
const importImage = (imageName: string) => {
  return new URL(`/src/assets/images/studentTask/${imageName}`, import.meta.url).href;
};
const bgImgPaths = ref([importImage("first_img.png"), importImage("second_img.png"), importImage("third_img.png"), importImage("four_img.png")]);
// 过滤已开启的服务项目
const filteredServes = computed(() => {
  return myServes.value.slice(1).filter(item => item.status === 1);
});

// 获取卡片标题颜色
const getCardTitleColor = (name: string): string => {
  const colorMap: Record<string, string> = {
    入学指南: "#073059",
    常见问题: "#734000",
    校园生活指引: "#006941",
    校园地图: "#103050"
  };
  return colorMap[name] || "#333333";
};

// 判断是否应该使用原有逻辑
const shouldUseOriginalLogic = (item: any): boolean => {
  if (!item) return true;

  // 任务1、2、3始终使用原有逻辑
  if ("123".includes(String(item.id))) {
    return true;
  }

  // 任务4：只有当projectName为vtcsy时才使用新逻辑，其他情况使用原有逻辑
  if (Number(item.id) === 4) {
    return projectName.value !== "vtcsy";
  }

  // 其他任务使用原有逻辑
  return true;
};

// 判断任务是否被禁用
const isTaskDisabled = (item: any): boolean => {
  if (!item) return false;

  // 只有当projectName为vtcsy且任务id为4时，才需要特殊处理
  if (projectName.value === "vtcsy" && Number(item.id) === 4) {
    return !canDoStepFour.value && item.finishStatus !== 2;
  }
  return false;
};

// 获取任务按钮类型
const getTaskButtonType = (item: any): ButtonType => {
  if (!item) return "primary";

  if (item?.finishStatus === 2) {
    return "default";
  }

  // 只有当projectName为vtcsy且任务id为4时，才需要特殊处理
  if (projectName.value === "vtcsy" && Number(item.id) === 4) {
    return canDoStepFour.value ? "primary" : "default";
  }

  return "primary";
};

// 获取任务按钮样式
const getTaskButtonStyle = (item: any): Record<string, string> => {
  if (!item) return {};

  if (item?.finishStatus === 2) {
    return { backgroundColor: "#E8E8E8", color: "#999999", border: "none" };
  }

  // 只有当projectName为vtcsy且任务id为4时，才需要特殊处理
  if (projectName.value === "vtcsy" && Number(item.id) === 4 && !canDoStepFour.value) {
    return { backgroundColor: "#E8E8E8", color: "#999999", border: "none" };
  }

  return {};
};

// 获取任务按钮文本
const getTaskButtonText = (item: any): string => {
  if (!item) return "去完成";
  return item?.finishStatus === 2 ? "已完成" : "去完成";
};

// 获取我的服务
const getMyServes = async () => {
  const { data } = (await getSysConfigVoByKeyApi("user.bind.newStu.guide")) as { data: any };
  const { configValue } = data ?? {};
  const configObj = JSON.parse(configValue);
  const newArr = configObj.map(item => {
    return {
      ...item,
      localImgUrl: item.name === "入学指南" ? bgImgPaths.value[0] : item.name === "常见问题" ? bgImgPaths.value[1] : item.name === "校园生活指引" ? bgImgPaths.value[2] : bgImgPaths.value[3]
    };
  });
  console.log(newArr, "newArr");
  myServes.value = newArr; // 使用添加了本地图片的新数组
};
function navigateToDetail(item: any) {
  // 跳转外链
  if (item.type === 1) {
    window.open(item.url, "_blank");
  } else {
    // 打开新页面显示富文本内容
    router.push({ path: "/richText", query: { content: item.content } });
  }
}

// 获取任务列表
const getTaskList = async () => {
  const param = {
    page: 1,
    size: 1000
  };

  try {
    // 并行请求任务列表和已完成任务
    const [taskResponse, finishResponse] = await Promise.all([stuTaskPageApi(param), getFinishStuTaskApi()]);

    if (Number(taskResponse.code) !== 0 || Number(finishResponse.code) !== 0) {
      console.error("获取任务数据失败:", taskResponse, finishResponse);
      return;
    }

    // 获取已完成任务ID集合
    const finishTaskIds = new Set(finishResponse.data);

    // 处理任务数据
    const openedTasks: TaskItem[] = taskResponse.data.records
      .filter((item: any) => item.taskStatus) // 过滤已开放的任务
      .sort((a: any, b: any) => (a.sort || 0) - (b.sort || 0)) // 按sort字段升序排序
      .map((item: any) => ({
        ...item,
        finishStatus: finishTaskIds.has(item.id) ? 2 : 1 // 标记任务是否完成
      }));

    // 检查任务2和任务3的完成状态
    const task2Finished = openedTasks.some(task => task.id === 2 && task.finishStatus === 2);
    const task3Finished = openedTasks.some(task => task.id === 3 && task.finishStatus === 2);

    // 设置任务4的可执行状态
    // 当且仅当projectName为vtcsy时，任务4需要依赖任务2和3的完成状态
    if (projectName.value === "vtcsy") {
      canDoStepFour.value = task2Finished && task3Finished;
    } else {
      // 其他项目保持原有逻辑
      canDoStepFour.value = task2Finished && task3Finished;
    }

    taskList.value = openedTasks;
  } catch (error) {
    console.error("获取任务列表失败:", error);
  }
};

// 处理任务点击
async function handleTaskClick(item: any) {
  console.log(item);

  // 检查任务是否被禁用
  if (isTaskDisabled(item)) {
    console.log("任务被禁用，无法点击");
    return;
  }

  // 保持原有的任务4逻辑（兼容性）
  if (Number(item.id) === 4 && !canDoStepFour.value) return;
  console.log(item, "item");
  // 跳转外部任务
  if (item.taskType === "1") {
    // 先打开新窗口，避免异步操作后被浏览器阻止
    window.open(item.taskUrl, "_blank");
    // 异步调用完成任务接口
    try {
      await finishStuTaskApi(item.id);
    } catch (error) {
      console.error("完成任务失败:", error);
      // 如果接口调用失败，窗口已经打开，不影响用户体验
    }
  } else {
    router.push({ path: item?.taskUrl, query: { taskId: item.id, finishStatus: item.finishStatus } });
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f6fa;
}
.Background-image {
  position: fixed;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 180px;
  text-align: center;
}
.Background-image img {
  width: 100%;
  height: 180px;
  object-fit: fill;
}
.bottom-content {
  z-index: 10;
  height: 100%;
  margin: 140px 20px 0;
  margin-top: 150px; /* 给出足够的上边距，避免被固定定位的背景图覆盖 */
  background-color: #ffffff;
  border-radius: 8px 8px 0 0; /* 添加圆角效果，增强重叠视觉 */
}
.card {
  z-index: 10;
  padding: 10px;
  margin: 200px 20px 0;
  background-color: #ffffff;
  border-radius: 8px; /* 添加圆角效果，增强重叠视觉 */
}
.service-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}
.service-item {
  box-sizing: border-box;
  width: 48%;
  text-align: center;
  cursor: pointer;
  border-radius: 8px;
}
.service-img-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
}
.service-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #ffffff;
  border-radius: 8px;
}
.cardtitle {
  position: absolute;
  top: 5px;
  bottom: 6px;
  left: 5px;
  padding: 2px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  text-align: center;
  pointer-events: none;
  border-radius: 0 0 8px 8px;
}
.task-card {
  padding: 10px;
  margin: 10px 20px 20px;
  background-color: #ffffff;
  border-radius: 8px;
}
.task-title {
  position: relative;
  padding-left: 12px;
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: bold;
}
.task-title::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 16px;
  content: "";
  background-color: #1989fa;
  border-radius: 2px;
  transform: translateY(-50%);
}
.task-tip {
  position: relative;
  padding: 6px 6px 6px 25px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #303133;
  background-color: #fffbe6;
  border-radius: 4px;
}
.task-tip::before {
  position: absolute;
  top: 50%;
  left: 5px;
  width: 16px;
  height: 16px;
  content: "";
  background-image: url("src/assets/images/studentTask/sound_msg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateY(-50%);
}
.task-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 6px;
}
.task-left {
  display: flex;
  align-items: center;
}
.task-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}
.task-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  margin-right: 8px;
  font-size: 14px;
  font-weight: bold;
  color: #1989fa;
  border: 1.5px solid #1989fa;
  border-radius: 50%;
}
.task-label {
  font-size: 14px;
  font-weight: 500;
  color: #1d2023;
}
.task-btn {
  width: 52px;
  height: 22px;
  font-size: 12px;
}
:deep(.van-button--small) {
  padding: 0;
}
</style>
