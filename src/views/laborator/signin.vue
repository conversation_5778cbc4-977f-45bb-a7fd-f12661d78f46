<template>
  <LsLayout>
    <LsCard class="field-card m-[8px]">
      <div class="user-name text-[20px] font-semibold">{{ userInfo.name }}</div>
      <div class="user-id mt-4 text-[16px] text-[#979dab]">工号&nbsp;:&emsp;{{ userInfo.userId }}</div>
    </LsCard>
    <LsCard class="field-card m-[8px]">
      <div class="text-[16px] font-semibold">{{ info.projectName }}</div>
      <div class="user-id mt-4 text-[13px] text-[#979dab]">创建人：{{ info.applicantName }}</div>
      <div class="user-id mt-2 text-[13px] text-[#979dab]">开始时间：{{ info.beginDate }}</div>
      <div class="user-id mt-2 text-[13px] text-[#979dab]">签到方式：{{ signInfo.signType }}</div>
      <div class="user-id mt-2 text-[13px] text-[#979dab] pb-12" style="border-bottom: #eeeeee 1px solid">签到地点：{{ signInfo.signAddressName }} {{ signInfo.signRealm }}米</div>
      <div class="text-[15px] mt-12 font-semibold">请选择签到节次：</div>
      <div class="jc-container-c">
        <div class="jc-container">
          <div v-for="l in signInfo.lessonInfo" :key="l.lesson" @click="selecteLesson(l)" class="jc-item">
            <div class="jc-tag" :class="{ sign: l.sign, active: l.selected, conflict: l.sign === 0 && !l.selected && l.conflict == 2 }">第{{ l.lesson }}节</div>
            <div @click.stop="showConflict" v-if="l.conflict" class="sign-warning"><van-icon name="warning-o" />存在冲突</div>
          </div>
        </div>
      </div>
      <div v-if="isClock" @click="doSignIn" class="sign-btn active">签到</div>
      <div v-else class="sign-btn">签到</div>
      <div v-if="isClock" class="jrqd">
        <div class="flex" style="align-items: center">
          <img src="@/assets/images/clockIn/students_success.png" class="w-[20px] h-[20px]" alt="" />
          <span class="text-[#5C6371] ml-[5px]">已进入签到范围：</span>
        </div>
        <span class="text-[#1677FF] omit" @click="goPosition">
          {{ signInfo.signAddressName }}
        </span>
      </div>
      <div v-else class="jrqd">
        <div class="flex" style="align-items: center">
          <img src="@/assets/images/clockIn/students_warn.png" class="w-[20px] h-[20px]" alt="" />
          <span class="text-[#5C6371] ml-[5px]">您当前不在签到范围：</span>
        </div>
        <span class="text-[#1677FF] omit" @click="goPosition">
          {{ signInfo.signAddressName }}
        </span>
      </div>
      <div v-if="signInfo.lessonInfo && signInfo.lessonInfo.findIndex(l => l.sign) > -1" class="yqdjc">
        已签到节次：{{
          signInfo.lessonInfo
            .filter(l => l.sign)
            .map(l => l.lesson)
            .toString()
        }}
      </div>
    </LsCard>

    <template #footer>
      <div class="router-link-btn">
        <div @click="scan" class="apply-btn"><van-icon name="qr" size="26" /> 重新扫码</div>
      </div>
    </template>
  </LsLayout>
</template>

<script setup lang="ts" name="LaboratorSignin">
import { useUserStore } from "@/stores/modules/user";
import { LsLayout, LsCard } from "ls-desgin";
import { computed, ref, onMounted } from "vue";
import { getLabApplySignAddressAndLessonApi, getLabInfoApi, userSignApi, getLocation } from "@/api/modules/laborator";
import { showDialog, showSuccessToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import { useLsMap } from "@/hooks/useLsMap";
import { scanQRCode } from "@/hooks/WecomSDK";

const userInfo = computed(() => useUserStore().userInfo || {});
const { query } = useRoute();
const info = ref<any>({});
const signInfo = ref<any>({ lessonInfo: [] });
const id = ref("");
onMounted(() => {
  id.value = query.id as string;
  getLabInfoApi(id.value).then((res: any) => {
    info.value = res.data.labInfo || {};
  });
  getData();
});

const getData = () => {
  getLabApplySignAddressAndLessonApi(id.value).then((res: any) => {
    signInfo.value = res.data || {};
    beforeFn(res.data.signAddress.split(","), res.data.signRealm * 1);
  });
};

const isClock = ref(false);
const beforeFn = async (point: any, signInRange: any) => {
  const { isJudgeRange } = useLsMap(undefined, { lat: point[0], lng: point[1], radius: signInRange });
  getLocation((path: { lat: number; lng: number; radius: number }) => {
    try {
      isClock.value = isJudgeRange({ lat: point[0], lng: point[1], radius: signInRange as number }, path);
    } catch (error) {
      console.error(error, "判断范围失败");
    }
  });
};

const selecteLesson = (l: any) => {
  if (l.sign === 0 && l.conflict !== 2) {
    l.selected = !l.selected;
  }
};

const doSignIn = () => {
  if (signInfo.value.lessonInfo.findIndex(l => l.selected) > -1) {
    userSignApi({
      labApplyId: id.value,
      signInLesson: signInfo.value.lessonInfo
        .filter(l => l.selected || l.sign)
        .map(l => l.lesson)
        .toString(),
      userType: "0"
    }).then(() => {
      showSuccessToast("签到成功");
      getData();
    });
  } else {
    showToast("请选择签到节次");
  }
};

const showConflict = () => {
  showDialog({
    title: "冲突提示",
    width: "280px",
    closeOnClickOverlay: true,
    className: "dialog-class",
    confirmButtonText: "我知道了",
    message: "同时间存在您报名的其他项目需要签到，同一节只能签到一个项目，请根据实际情况进行签到"
  });
};
const router = useRouter();
const goPosition = () => {
  router.push({ name: "LaboratorPosition", query: { point: signInfo.value.signAddress, radius: signInfo.value.signRealm } });
};

const scan = () => {
  scanQRCode((res: { resultStr: string }) => {
    const regex = /[?&]labCode=(\d+)/;
    const regex1 = /[?&]labRoomInfoId=(\d+)/;
    const match = res.resultStr.match(regex);
    const match1 = res.resultStr.match(regex1);
    if (!match || !match[1] || !match1 || !match1[1]) {
      const regex2 = /[?&]id=(\d+)/;
      const match = res.resultStr.match(regex2);
      if (!match || !match[1] || res.resultStr.indexOf("/laborator/labInfo") === -1) {
        showToast("非法二维码");
      } else {
        router.replace({ name: "LaboratorLabInfo", query: { id: match[1] } });
      }
    } else {
      router.replace({ name: "LaboratorStudent", query: { labCode: match[1], labRoomInfoId: match1[1] } });
    }
  });
};
</script>
<style scoped lang="scss">
.jrqd {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  text-align: center;
}
.sign-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 150px;
  margin: 16px auto;
  font-size: 20px;
  color: #ffffff;
  cursor: pointer;
  background: #aaaaaa;
  border: #eeeeee 8px solid;
  border-radius: 150px;
}
.sign-btn.active {
  background: #1677ff;
}
.jc-container-c {
  width: 100%;
  overflow: hidden;
}
.jc-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 8px;
  .jc-item {
    flex: 0 0 auto;
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    .jc-tag {
      padding: 6px 18px;
      font-size: 13px;
      line-height: 13px;
      color: #666666;
      background: #eeeeee;
      border-radius: 4px;
    }
    .sign {
      color: #07c160;
      background: #f0fff0;
    }
    .active {
      color: #ffffff;
      background: #1677ff;
    }
    .sign-warning {
      width: 100%;
      font-size: 12px;
      color: #ff976a;
      text-align: center;
    }
    .conflict {
      color: #cccccc;
      background: #f4f4f4;
    }
  }
}
.yqdjc {
  padding-top: 8px;
  margin-top: 12px;
  font-size: 13px;
  text-align: center;
  border-top: #eeeeee 1px solid;
}
.router-link-btn {
  width: 100%;
  padding: 16px;
  background: #ffffff;
  .apply-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 54px;
    font-size: 18px;
    color: #ffffff;
    cursor: pointer;
    background: #1677ff;
    border-radius: 8px;
  }
}
</style>
