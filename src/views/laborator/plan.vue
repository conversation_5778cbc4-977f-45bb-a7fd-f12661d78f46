<template>
  <LsLayout>
    <template #header>
      <div class="plan-header">
        <div @click="openShowInfo" class="plan-header-left"><van-icon name="description-o" color="#1677ff" /> 参考信息 <van-icon name="arrow" /></div>
        <div v-if="!showInfo" class="plan-header-right">
          <van-button @click="openSetRepetition" type="primary" plain size="small"><van-icon name="edit" /> 设置重复</van-button>
        </div>
      </div>
    </template>
    <div class="week-list-container">
      <div v-if="currentWeekPlanText" @click="showWurrentWeekPlan = true" class="yxzc-xx">
        <div class="yxzc-info van-ellipsis">
          <span class="color-black">第{{ currentWeekIndex }}周</span>
          <span class="color-plan">时间：</span>
          {{ currentWeekPlanText }}
        </div>
        <van-icon name="arrow-down" color="#999999" size="18" style="margin-top: 2px" />
      </div>
      <div v-else class="yxzc-xx">
        <div class="yxzc-info van-ellipsis">
          <span style="color: #999999">当前周暂无排课</span>
        </div>
        <van-icon name="arrow-down" color="#999999" size="18" style="margin-top: 2px" />
      </div>
    </div>
    <div class="week-list-container">
      <div id="scroll-container" class="week-list">
        <div ref="weekRef" @click="setCurrentWeek(item)" v-for="item in schoolCalendar.length" :key="item" class="week-item" :class="{ current: currentWeekIndex === item, active: checkWeekClass(item), disabled: item < minWeekIndex }">{{ item }}</div>
      </div>
    </div>
    <div class="xl-container">
      <div class="xl-item" style="width: 13%">
        <div class="xl-date">{{ currentM }}</div>
        <div v-for="item in 12" :key="item" class="xl-number">{{ item }}</div>
      </div>
      <div v-for="(week, index) in currentWeek" :key="index" class="xl-item">
        <div class="xl-date">
          <div>
            <div class="xl-date-name">{{ week.name }}</div>
            <div class="xl-date-abbr">{{ week.abbr }}</div>
          </div>
        </div>
        <div v-for="(course, i) in week.courses" :key="i" class="xl-course" :class="{ 'disabled-date': week.date < today }">
          <div @click="selecteCourse(week, i + 1)" v-if="week.date" class="courses-block" :class="blockClass(week, i + 1)">
            <van-icon v-if="checkConflict(week, i + 1)" name="fail" size="20" color="#ffffff" />
            <!-- <div v-if="week.date >= today && checkShare(week, index) && sharePeople(week, i + 1)" class="kgx">可共享<br />{{ sharePeople(week, i + 1) }}人</div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="pk-sm">
      <div class="pk-sm-item">
        <div class="pk-disable"></div>
        该时间段被占用（不可共享）
      </div>
      <div class="pk-sm-item">
        <div class="pk-disable pk-disable-gx"></div>
        该时间段被占用（可共享）
      </div>
      <div class="pk-sm-item">
        <div class="pk-disable pk-active"></div>
        该时间段已安排（无共享）
      </div>
      <div class="pk-sm-item">
        <div class="pk-disable pk-active-gx"></div>
        该时间段已安排（有共享）
      </div>
    </div>
    <template v-if="!showInfo && !showSetRepetition" #footer>
      <div v-if="disableEdit" class="w-full flex-shrink-0 flex p-12">
        <van-button @click="router.back()" class="flex-1" type="primary" block>返回</van-button>
      </div>
      <div v-else class="w-full flex-shrink-0 flex">
        <div @click="doDel" class="w-60 del-btn">
          <div>
            <van-icon name="brush-o" size="22" />
            <div>清空</div>
          </div>
        </div>
        <LsButtonBar @button-click="handleSubmit" main-text="保存" secondary-text="取消" type="doubleEqual" secondary-plain />
      </div>
    </template>
    <van-action-sheet v-model:show="showSetRepetition" title="设置重复">
      <div class="set-repetition-content">
        <div>当前周已排课：</div>
        <div class="ypk">
          <div class="ypk-item" v-for="item in labApplySchedulingList.filter(s => s.teachWeek === currentWeekIndex)" :key="item.date">星期{{ xqDist[item.dayOfWeek] }}：第{{ item.lesson.toString() }}节</div>
        </div>
        <div class="funtion-btn-c">
          <div @click="setRepetitionType(1)" class="funtion-btn" :class="{ active: repetitionType === 1 }">重复至全部周</div>
          <div @click="setRepetitionType(2)" class="funtion-btn" :class="{ active: repetitionType === 2 }">重复至单周</div>
          <div @click="setRepetitionType(3)" class="funtion-btn" :class="{ active: repetitionType === 3 }">重复至双周</div>
          <div @click="setRepetitionType(4)" class="funtion-btn" :class="{ active: repetitionType === 4 }">自定义重复</div>
        </div>
        <div>自定义重复</div>
        <div class="set-repetition-weeks">
          <div @click="setRepetitionWeek(item)" v-for="item in schoolCalendar.length" :key="item" class="week-item" :class="{ current: currentWeekIndex === item, active: repetitioList.findIndex(r => r === item) > -1, disabled: item < minWeekIndex }">
            {{ item }}
          </div>
        </div>
        <van-button class="apply-btn" :loading="inCheck" type="primary" block @click="savetRepetition">保存</van-button>
      </div>
    </van-action-sheet>
    <van-popup v-model:show="showInfo" position="top" :style="{ height: '100%' }" closeable>
      <div class="show-info-c">
        <div class="show-info-title" style="background: #f4f5f6">参考信息</div>
        <div class="show-info-body">
          <van-form ref="vanFormRef" input-align="right" error-message-align="right" validate-trigger="onChange" label-width="120" style="margin-bottom: 16px">
            <van-field :disabled="disableEdit" v-model="form.realClassHour" placeholder="修改开放时长" :rules="[{ validator, required: false, message: '开放时长必须为正整数' }]">
              <template #label>
                申请开放时长 <span class="expect-text">({{ info.classHour }})</span>
              </template>
              <template #right-icon>课时</template>
            </van-field>
            <van-field
              v-if="info.applyStatus === '1' || info.applyStatus === '2'"
              v-model="form.realWeekClassHour"
              placeholder="修改每周课时"
              :rules="[
                { validator: validator, required: false, message: '每周课时必须为正整数' },
                { validator: val => val < 61, required: false, message: '每周课时必须小于60' }
              ]"
            >
              >
              <template #label>
                预期每周课时 <span class="expect-text-2">({{ info.expectWeekClassHour }})</span>
              </template>
              <template #right-icon>课时</template>
            </van-field>
            <van-field
              v-if="info.applyStatus === '1' || info.applyStatus === '2'"
              v-model="form.realDayClassHour"
              placeholder="修改每天课时"
              :rules="[
                { validator: validator, required: false, message: '每天课时必须为正整数' },
                { validator: val => val < 13, required: false, message: '每天课时必须小于12' }
              ]"
            >
              >
              <template #label>
                预期每天课时 <span class="expect-text-2">({{ info.expectDayClassHour }})</span>
              </template>
              <template #right-icon>课时</template>
            </van-field>
            <van-field
              :disabled="disableEdit"
              v-model="form.realPeople"
              placeholder="修改人数"
              :rules="[
                { validator: validator, required: false, message: '人数必须为正整数' },
                { validator: val => val < 1001, required: false, message: '人数必须小于1000' },
                { validator: validator2, required: false, message: '请填写预期人数' }
              ]"
            >
              >
              <template #label>
                预期人数 <span class="expect-text">({{ info.expectPeople }})</span>
              </template>
              <template #right-icon>人</template>
            </van-field>
            <van-field>
              <template #label>预期实验室</template>
              <template #input>{{ info.labName }}</template>
            </van-field>
            <van-field>
              <template #label>预期房间</template>
              <template #input>{{ info.roomName }}</template>
            </van-field>
            <van-field v-if="info.applyStatus === '1' || info.applyStatus === '2'">
              <template #label>
                优先周次<br /><span style="color: #999999">({{ info.priorityWeek }})</span>
              </template>
              <template #input>
                <div v-if="!form.realWeek" @click="openShowZc" style="color: #999999; cursor: pointer">修改周次<van-icon name="arrow" /></div>
                <div v-else @click="openShowZc" style="color: #333333; cursor: pointer">{{ form.realWeek }}<van-icon name="arrow" color="#999999" /></div>
              </template>
            </van-field>
            <van-field v-if="info.applyStatus === '1' || info.applyStatus === '2'">
              <template #label>
                优先节次<br /><span style="color: #999999">({{ info.prioritySection }})</span>
              </template>
              <template #input>
                <div v-if="!form.realLesson" @click="openShowJc" style="color: #999999; cursor: pointer">修改节次<van-icon name="arrow" /></div>
                <div v-else @click="openShowJc" style="color: #333333; cursor: pointer">{{ form.realLesson }}<van-icon name="arrow" color="#999999" /></div>
              </template>
            </van-field>
            <van-field label="共享实验室" placeholder="">
              <template #input><van-switch v-model="form.shareFlag" :disabled="disableEdit" /></template>
            </van-field>
          </van-form>
        </div>
        <div class="show-info-bottom">
          <div v-if="disableEdit" class="apply-btn apply-btn-disable">保存</div>
          <div v-else @click="savetExpect" class="apply-btn">保存</div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model:show="showZc" position="bottom" closeable>
      <div class="show-info-cc">
        <div class="show-info-title">修改周次</div>
        <div class="show-info-body">
          <div class="week-item" v-for="item in schoolCalendar.length" :key="item" :class="{ active: priorityWeek.findIndex(p => p === item + '') > -1 }" @click="priorityWeekToggle(item + '')">{{ item }}</div>
        </div>
        <div class="show-info-bottom">
          <div @click="savetZc" class="apply-btn">保存</div>
        </div>
      </div>
    </van-popup>

    <van-popup v-model:show="showJc" position="bottom" closeable>
      <div class="show-info-cc">
        <div class="show-info-title">修改节次</div>
        <div class="show-info-body">
          <div class="week-item" v-for="item in 12" :key="item" :class="{ active: prioritySection.findIndex(p => p === item + '') > -1 }" @click="prioritySectionToggle(item + '')">{{ item }}</div>
        </div>
        <div class="show-info-bottom">
          <div @click="saveJc" class="apply-btn">保存</div>
        </div>
      </div>
    </van-popup>

    <van-dialog v-model:show="showWurrentWeekPlan" :title="`第${currentWeekIndex}周排课`" teleport="body" class-name="query-exam-detail-dialog" confirm-button-text="我知道了">
      <div v-for="s in labApplySchedulingList.filter(s => s.teachWeek === currentWeekIndex && s.lesson.length)" :key="s.id" style="padding-left: 16px">星期{{ xqDist[s.dayOfWeek] }} 第{{ s.lesson.toString() }}节</div>
    </van-dialog>
  </LsLayout>
</template>

<script setup lang="ts" name="LaboratorPlan">
import { LsLayout, LsButtonBar } from "ls-desgin";
import { ref, onMounted, computed, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getApplyInfoApi, savePlanCourseApi, getLabApplySchedulingByLabApplyIdApi, getCurrentSchoolCalendarApi, getPlanCourseDateListApi } from "@/api/modules/laborator";
import { showDialog, showSuccessToast, showToast } from "vant";
import dayjs from "dayjs";
import type { FormInstance } from "vant";

const xqDist = ["日", "一", "二", "三", "四", "五", "六"];
const today = dayjs().format("YYYY-MM-DD");
const vanFormRef = ref<FormInstance>();
const form = ref<any>({});
const router = useRouter();
const { query } = useRoute();
const id = ref("");
const role = ref("");
const currentWeekIndex = ref();
const currentWeek = ref<any>([]);
const schoolCalendar = ref<any>([]);
const labApplySchedulingList = ref<any>([]);
const labApplyRealScheduleId = ref("");
const info = ref<any>({});
const disableData = ref<any>([]);
const labApplySchedulingData = ref<any>([]);
const showInfo = ref(false);
const realSchedule = ref<any>({});
const validator = (val: string) => !val || /^[1-9]\d*$/.test(val);
const minWeekIndex = ref(1);
const weekRef = ref();
const showWurrentWeekPlan = ref(false);
const currentWeekPlanText = computed(() => {
  return labApplySchedulingList.value
    .filter(s => s.teachWeek === currentWeekIndex.value && s.lesson.length)
    .map(w => {
      return `星期${xqDist[w.dayOfWeek]}第${w.lesson.toString()}节 `;
    })
    .join(" ");
});
const disableEdit = ref(false);
onMounted(() => {
  id.value = query.id as string;
  role.value = query.role as string;
  getApplyInfoApi(id.value).then((res: any) => {
    info.value = res.data?.labInfo || {};
    realSchedule.value = res.data?.realSchedule || {};
    if (role.value === "2" && ["3"].findIndex(s => s === info.value.applyStatus) > -1) {
      disableEdit.value = true;
    }
    if (info.value.applyStatus === "8" || info.value.applyStatus === "9") {
      disableEdit.value = true;
    }
    form.value = {
      realClassHour: realSchedule.value.realClassHour,
      realDayClassHour: realSchedule.value.realDayClassHour,
      realPeople: realSchedule.value.realPeople,
      realWeekClassHour: realSchedule.value.realWeekClassHour,
      realWeek: realSchedule.value.realWeek,
      realLesson: realSchedule.value.realLesson,
      shareFlag: realSchedule.value.shareFlag
    };
    getCurrentSchoolCalendarApi().then(res => {
      schoolCalendar.value = res.weekList;
      minWeekIndex.value = schoolCalendar.value.findIndex(s => s[6].date >= today) + 1;
      setCurrentWeek(minWeekIndex.value);
      nextTick(() => {
        weekRef.value[minWeekIndex.value - 1].scrollIntoView({ behavior: "smooth", block: "nearest", inline: "start" });
      });
    });
  });
  getLabApplySchedulingByLabApplyIdApi(query.id as string).then((res: any) => {
    if (res.data.labApplyRealScheduleId) {
      labApplyRealScheduleId.value = res.data.labApplyRealScheduleId;
      (res.data.scheduleList || []).forEach((week: any) => {
        const lesson: any = [];
        week.lesson.split(",").forEach((l: string) => lesson.push(parseInt(l)));
        labApplySchedulingList.value.push({
          date: week.date,
          lesson,
          dayOfWeek: week.dayOfWeek,
          teachWeek: parseInt(week.teachWeek)
        });
      });
    }
  });
});

const openShowInfo = () => {
  form.value = {
    realClassHour: realSchedule.value.realClassHour,
    realDayClassHour: realSchedule.value.realDayClassHour,
    realPeople: realSchedule.value.realPeople,
    realWeekClassHour: realSchedule.value.realWeekClassHour,
    realWeek: realSchedule.value.realWeek,
    realLesson: realSchedule.value.realLesson,
    shareFlag: realSchedule.value.shareFlag
  };
  showInfo.value = true;
};
const showZc = ref(false);
const priorityWeek = ref<string[]>([]);
const openShowZc = () => {
  priorityWeek.value = form.value.realWeek ? form.value.realWeek.split(",") : [];
  showZc.value = true;
};
const priorityWeekToggle = (val: string) => {
  if (priorityWeek.value.findIndex(p => p === val) > -1) {
    priorityWeek.value = priorityWeek.value.filter(p => p !== val);
  } else {
    priorityWeek.value.push(val);
  }
};
const savetZc = () => {
  form.value.realWeek = priorityWeek.value.sort((a, b) => parseInt(a) - parseInt(b)).toString();
  showZc.value = false;
};

const showJc = ref(false);
const prioritySection = ref<string[]>([]);
const openShowJc = () => {
  prioritySection.value = form.value.realLesson ? form.value.realLesson.split(",") : [];
  showJc.value = true;
};
const prioritySectionToggle = (val: string) => {
  if (prioritySection.value.findIndex(p => p === val) > -1) {
    prioritySection.value = prioritySection.value.filter(p => p !== val);
  } else {
    prioritySection.value.push(val);
  }
};
const saveJc = () => {
  form.value.realLesson = prioritySection.value.sort((a, b) => parseInt(a) - parseInt(b)).toString();
  showJc.value = false;
};

const setCurrentWeek = (index: number) => {
  if (index < minWeekIndex.value) {
    return;
  }
  getPlanCourseDateListApi(info.value.labRoomInfoId, index).then((res: any) => {
    disableData.value = [];
    (res.data.disableData || []).forEach((week: any) => {
      const lesson: any = [];
      week.lesson.split(",").forEach((l: string) => lesson.push(parseInt(l)));
      week.lesson = lesson;
      disableData.value.push(week);
    });
    labApplySchedulingData.value = [];
    (res.data.labApplySchedulingData || [])
      .filter(d => d.labApplyId !== info.value.id)
      .forEach((week: any) => {
        const lesson: any = [];
        week.lesson.split(",").forEach((l: string) => lesson.push(parseInt(l)));
        week.lesson = lesson;
        labApplySchedulingData.value.push(week);
      });
  });
  currentWeekIndex.value = index;
  currentWeek.value = schoolCalendar.value[index - 1];
  currentWeek.value.forEach(c => {
    c.courses = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}];
  });
};

const currentM = computed(() => {
  if (currentWeek.value.findIndex(w => w.date) > -1) {
    return dayjs(currentWeek.value.find(w => w.date).date).format("M月");
  } else {
    return "";
  }
});
const selecteCourse = (week, index) => {
  if ((realSchedule.value.realPeople || info.value.capacity) > info.value.capacity) {
    return showToast(`人数超过房间容量${info.value.capacity}`);
  }
  if (!realSchedule.value.realPeople && !info.value.capacity) {
    return showToast("请先设置人数");
  }
  if (disableEdit.value) {
    return;
  }
  if (checkDisabled(week, index) && !checkSelected(week, index)) {
    return;
  }
  if (checkShare(week, index) && !realSchedule.value.shareFlag && !checkSelected(week, index)) {
    return;
  }
  if (checkShare(week, index) && realPeople.value * 1 > sharePeople(week, index) && !checkSelected(week, index)) {
    return;
  }
  if (week.date < today && !checkSelected(week, index)) {
    return;
  }
  if (labApplySchedulingList.value.findIndex(l => l.date === week.date) > -1) {
    const w = labApplySchedulingList.value.find(l => l.date === week.date);
    if (w.lesson.findIndex(l => l === index) > -1) {
      w.lesson = w.lesson.filter(l => l !== index);
    } else {
      w.lesson.push(index);
    }
    w.lesson = w.lesson.sort((a, b) => a - b);
  } else {
    labApplySchedulingList.value.push({
      date: week.date,
      lesson: [index],
      dayOfWeek: week.dayOfWeek,
      teachWeek: currentWeekIndex.value
    });
  }
};

const realPeople = computed(() => {
  return realSchedule.value.realPeople || info.value.capacity;
});

// 实验室已选择
const checkSelected = (week, index) => {
  return (labApplySchedulingList.value.find(l => l.date === week.date) || { lesson: [] }).lesson.findIndex(l => l === index) > -1;
};
// 实验室是否可用
const checkUnavailable = (week, index) => {
  return (disableData.value.find(l => dayjs(l.date).format("YYYY-MM-DD") === week.date) || { lesson: [] }).lesson.findIndex(l => l === index) > -1;
};
// 实验室是否被占用
const checkOccupy = (week, index) => {
  return labApplySchedulingData.value.findIndex(l => dayjs(l.date).format("YYYY-MM-DD") === week.date && !l.shareFlag && l.lesson.findIndex(les => les === index) > -1) > -1;
};
// 实验室是否不可选
const checkDisabled = (week, index) => {
  return checkUnavailable(week, index) || checkOccupy(week, index);
};
// 实验室是否可共享
const checkShare = (week, index) => {
  return labApplySchedulingData.value.findIndex(l => dayjs(l.date).format("YYYY-MM-DD") === week.date && l.shareFlag && l.lesson.findIndex(les => les === index) > -1) > -1;
};
// 实验室可共享人数
const sharePeople = (week, index) => {
  let count = 0;
  labApplySchedulingData.value
    .filter(l => dayjs(l.date).format("YYYY-MM-DD") === week.date && l.shareFlag && l.lesson.findIndex(les => les === index) > -1)
    .forEach((s: any) => {
      count += s.realPeople;
    });
  return count > info.value.capacity ? 0 : info.value.capacity - count;
};
// 排课是否冲突
const checkConflict = (week, index) => {
  return checkDisabled(week, index) && checkSelected(week, index);
};
const blockClass = computed(() => (week, index) => {
  if (checkSelected(week, index) && checkShare(week, index)) {
    return "share-active";
  } else if (checkSelected(week, index)) {
    return "active";
  } else if (checkDisabled(week, index) || (realSchedule.value.realPeople || info.value.capacity) > info.value.capacity) {
    return "disabled";
  } else if (checkShare(week, index)) {
    if (realSchedule.value.shareFlag && realPeople.value * 1 <= sharePeople(week, index)) {
      return "share";
    } else {
      return "disabled";
    }
  } else {
    return "";
  }
});

const checkWeekClass = computed(() => (item: string) => {
  return labApplySchedulingList.value.findIndex(l => (l.teachWeek === item || l.teachWeek === item + "") && l.lesson.length) > -1;
});

const handleSubmit = (type: string) => {
  if (type === "main") {
    if (labApplySchedulingList.value.findIndex(l => l.lesson.length) > -1) {
      const params: any = {
        labApplyId: query.id,
        creatorUserType: role.value,
        labApplySchedulingList: [],
        realClassHour: realSchedule.value.realClassHour,
        realDayClassHour: realSchedule.value.realDayClassHour,
        realPeople: realSchedule.value.realPeople || info.value.capacity,
        realWeekClassHour: realSchedule.value.realWeekClassHour,
        realWeek: realSchedule.value.realWeek,
        realLesson: realSchedule.value.realLesson,
        shareFlag: realSchedule.value.shareFlag
      };
      labApplySchedulingList.value.forEach(l => {
        if (l.lesson.length) {
          params.labApplySchedulingList.push({
            date: l.date,
            lesson: l.lesson.toString(),
            dayOfWeek: l.dayOfWeek,
            teachWeek: l.teachWeek
          });
        }
      });
      if (labApplyRealScheduleId.value) {
        params.labApplyRealScheduleId = labApplyRealScheduleId.value;
      }
      savePlanCourseApi(params).then(() => {
        showSuccessToast("排课成功");
        router.back();
      });
    } else {
      return showToast("请选择方案");
    }
  } else {
    router.back();
  }
};
const doDel = () => {
  if (labApplySchedulingList.value.length === 0) {
    return;
  }
  showDialog({
    title: "清空",
    message: "确定清空现有方案，重新排课?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(() => {
    labApplySchedulingList.value = [];
  });
};

const inCheck = ref(false);
const openSetRepetition = () => {
  if (labApplySchedulingList.value.findIndex(s => s.teachWeek === currentWeekIndex.value && s.lesson.length) === -1) {
    return showToast("当前周没有排课！");
  }
  showSetRepetition.value = true;
  repetitionType.value = 4;
  repetitioList.value = [];
  inCheck.value = false;
};

const showSetRepetition = ref(false);
const repetitionType = ref(4);
const repetitioList = ref<number[]>([]);
const setRepetitionType = (type: number) => {
  if (inCheck.value) return;
  repetitionType.value = type;
  repetitioList.value = [];
  if (type !== 4) {
    for (let i = 1; i <= schoolCalendar.value.length; i++) {
      if (i % 2 === 0 && (type === 1 || type === 3) && i !== currentWeekIndex.value) {
        if (i >= minWeekIndex.value) {
          repetitioList.value.push(i);
        }
      } else if (i % 2 === 1 && (type === 1 || type === 2) && i !== currentWeekIndex.value) {
        if (i >= minWeekIndex.value) {
          repetitioList.value.push(i);
        }
      }
    }
  }
};
const setRepetitionWeek = (week: any) => {
  if (inCheck.value) return;
  if (week < minWeekIndex.value) {
    return;
  }
  if (repetitioList.value.findIndex(r => r === week) > -1) {
    repetitioList.value = repetitioList.value.filter(r => r !== week);
  } else {
    repetitioList.value.push(week);
  }
};
const savetRepetition = async () => {
  if (inCheck.value) return;
  if (repetitioList.value.length === 0) {
    return showToast("请选择重复周次！");
  }
  inCheck.value = true;
  const conflictWeek: any = [];
  try {
    for (let i in repetitioList.value as number[]) {
      const res = await getPlanCourseDateListApi(info.value.labRoomInfoId, repetitioList.value[i]);
      disableData.value = [];
      (res.data.disableData || []).forEach((week: any) => {
        const lesson: any = [];
        week.lesson.split(",").forEach((l: string) => lesson.push(parseInt(l)));
        week.lesson = lesson;
        disableData.value.push(week);
      });
      labApplySchedulingData.value = [];
      (res.data.labApplySchedulingData || [])
        .filter(d => d.labApplyId !== info.value.id)
        .forEach((week: any) => {
          const lesson: any = [];
          week.lesson.split(",").forEach((l: string) => lesson.push(parseInt(l)));
          week.lesson = lesson;
          labApplySchedulingData.value.push(week);
        });
      let isConflict = false;
      labApplySchedulingList.value
        .filter(l => l.teachWeek === currentWeekIndex.value)
        .forEach(s => {
          const week = schoolCalendar.value[repetitioList.value[i] - 1].find(c => parseInt(c.dayOfWeek) === parseInt(s.dayOfWeek));
          if (week) {
            s.lesson.forEach(les => {
              if (blockClass.value(week, les) === "disabled") {
                isConflict = true;
              }
            });
          }
        });
      if (isConflict) {
        conflictWeek.push(repetitioList.value[i]);
      }
    }
  } catch (error) {
    setCurrentWeek(currentWeekIndex.value);
    inCheck.value = false;
  }
  setCurrentWeek(currentWeekIndex.value);
  inCheck.value = false;
  if (conflictWeek.length) {
    return showToast(`第${conflictWeek.toString()}周排课存在冲突！`);
  }

  labApplySchedulingList.value
    .filter(l => l.teachWeek === currentWeekIndex.value)
    .forEach(s => {
      repetitioList.value.forEach(r => {
        if (schoolCalendar.value[r - 1].findIndex(c => parseInt(c.dayOfWeek) === parseInt(s.dayOfWeek)) > -1) {
          const d = schoolCalendar.value[r - 1].find(c => parseInt(c.dayOfWeek) === parseInt(s.dayOfWeek)).date;
          const index = labApplySchedulingList.value.findIndex(sl => sl.date === d);
          if (index === -1) {
            labApplySchedulingList.value.push({
              date: d,
              lesson: s.lesson,
              dayOfWeek: parseInt(s.dayOfWeek),
              teachWeek: r
            });
          } else {
            labApplySchedulingList.value[index].lesson = s.lesson;
          }
        }
      });
    });
  showSetRepetition.value = false;
};

const validator2 = (val: string) => {
  return !form.value.shareFlag || !!val;
};
const savetExpect = async () => {
  await vanFormRef.value?.validate();
  if (form.value.shareFlag && !form.value.realPeople) {
    return showToast("请填写预期人数！");
  }
  realSchedule.value = {
    realClassHour: form.value.realClassHour,
    realDayClassHour: form.value.realDayClassHour,
    realPeople: form.value.realPeople,
    realWeekClassHour: form.value.realWeekClassHour,
    realWeek: form.value.realWeek,
    realLesson: form.value.realLesson,
    shareFlag: form.value.shareFlag
  };
  showInfo.value = false;
  labApplySchedulingList.value = [];
};
</script>
<style scoped lang="scss">
.pk-sm {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 12px;
  background: #ffffff;
  .pk-sm-item {
    display: flex;
    align-items: center;
    width: 50%;
    margin-bottom: 4px;
    font-size: 12px;
    color: #999999;
    .pk-disable {
      width: 14px;
      height: 14px;
      margin-right: 4px;
      background: #eeeeee;
      border-radius: 12px;
    }
    .pk-disable-gx {
      background: #aef8d2;
    }
    .pk-active {
      background: #56a7ff;
    }
    .pk-active-gx {
      background: #07c160;
    }
  }
}
.xl-container {
  display: flex;
  background: #ffffff;
  .xl-item {
    width: 20%;
    .xl-number {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      color: #000000;
      background: #f5f5f5;
    }
    .xl-course {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      border-bottom: #dddddd 1px solid;
      .courses-block {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 85%;
        height: 85%;
        background: #ffffff;
        border-radius: 4px;
        .kgx {
          position: absolute;
          right: 2px;
          bottom: 2px;
          font-size: 8px;
          line-height: 10px;
          color: #999999;
          text-align: right;
        }
      }
      .courses-block.disabled {
        background: #f5f5f5;
      }
      .courses-block.active {
        background: #56a7ff;
      }
      .courses-block.share {
        background: #aef8d2;
      }
      .courses-block.share-active {
        background: #07c160;
        .kgx {
          color: #ffffff;
        }
      }
    }
    .xl-date {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 46px;
      font-size: 12px;
      text-align: center;
      border-bottom: #dddddd 1px solid;
      .xl-date-name {
        color: #333333;
      }
      .xl-date-abbr {
        color: #666666;
      }
    }
    .disabled-date {
      background: #eeeeee;
      .courses-block {
        background: #eeeeee !important;
      }
    }
  }
}
.plan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f4f5f6 !important;
  .plan-header-left {
    display: flex;
    align-items: center;
    font-size: 14px;
  }
}
.week-list-container {
  width: 100%;
  padding: 0 12px 12px 4px;
  overflow: auto hidden;
  background: #f4f5f6 !important;
  .yxzc-xx {
    display: flex;
    padding: 8px 12px;
    margin-left: 8px;
    cursor: pointer;
    background: #ffffff;
    border-radius: 4px;
    .yxzc-info {
      flex: 1 1 auto;
      font-size: 14px;
      color: #333333 !important;
    }
  }
  .week-list {
    display: flex;
    margin-left: 8px;
    .week-item {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      margin-right: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #666666;
      cursor: pointer;
      background: #ffffff;
      border: #dddddd 2px solid;
      border-radius: 32px;
    }
    .active {
      color: #ffffff;
      background: #79b9f9;
      border: #79b9f9 2px solid;
    }
    .current {
      border: #1677ff 3px solid;
    }
    .disabled {
      color: #999999;
      background: #eeeeee;
    }
  }
}
.del-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
  font-size: 12px;
  color: #1677ff;
  text-align: center;
  cursor: pointer;
  .van-icon {
    color: #1677ff;
  }
}
.set-repetition-content {
  padding: 0 12px 12px;
  .funtion-btn-c {
    display: flex;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 12px;
    .funtion-btn {
      width: 25%;
      height: 32px;
      margin-right: 8px;
      font-size: 12px;
      line-height: 32px;
      color: #444444;
      text-align: center;
      cursor: pointer;
      background: #f4f5f6;
      border-radius: 4px;
    }
    .funtion-btn:last-child {
      margin-right: 0;
    }
    .funtion-btn.active {
      color: #1677ff;
      background: #ddeeff;
    }
  }
  .set-repetition-weeks {
    display: flex;
    flex-wrap: wrap;
    .week-item {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      margin-top: 8px;
      margin-right: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #666666;
      cursor: pointer;
      background: #ffffff;
      border: #dddddd 2px solid;
      border-radius: 32px;
    }
    .active {
      color: #ffffff;
      background: #1677ff;
      border: #1677ff 2px solid;
    }
    .current {
      color: #ffffff;
      background: #79b9f9;
      border: #79b9f9 2px solid;
    }
    .disabled {
      color: #999999;
      background: #eeeeee;
    }
  }
  .apply-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 42px;
    margin-top: 16px;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    background: #1677ff;
    border-radius: 8px;
  }
}
.ypk {
  display: flex;
  flex-wrap: wrap;
  .ypk-item {
    padding: 4px 12px;
    margin-top: 4px;
    margin-right: 8px;
    font-size: 12px;
    line-height: 12px;
    background: #f4f5f6;
    border-radius: 4px;
  }
}
.show-info-c {
  display: flex;
  flex-direction: column;
  width: 100% !important;
  height: 100vh;
  .show-info-title {
    flex: 0 0 auto;
    padding: 16px;
    font-size: 16px;
    border-bottom: #dddddd 1px solid;
  }
  .show-info-body {
    flex: 1 1 auto;
    overflow: hidden auto;
  }
  .show-info-bottom {
    flex: 0 0 auto;
    padding: 12px;
    .apply-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 42px;
      margin-top: 16px;
      font-size: 16px;
      color: #ffffff;
      cursor: pointer;
      background: #1677ff;
      border-radius: 8px;
    }
    .apply-btn-disable {
      background: #cccccc;
    }
  }
}
.show-info-cc {
  display: flex;
  flex-direction: column;
  width: 100% !important;
  .show-info-title {
    flex: 0 0 auto;
    padding: 16px;
    font-size: 16px;
    border-bottom: #dddddd 1px solid;
  }
  .show-info-body {
    display: flex;
    flex: 1 1 auto;
    flex-wrap: wrap;
    padding: 12px 0 0 12px;
    overflow: hidden auto;
    .week-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      padding: 10px;
      margin-right: 8px;
      margin-bottom: 8px;
      font-size: 15px;
      line-height: 15px;
      text-align: center;
      cursor: pointer;
      border: #dddddd 1px solid;
      border-radius: 20px;
    }
    .week-item.active {
      color: #ffffff;
      background: #1677ff;
      border: #1677ff 1px solid;
    }
  }
  .show-info-bottom {
    flex: 0 0 auto;
    padding: 12px;
    .apply-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 42px;
      margin-top: 16px;
      font-size: 16px;
      color: #ffffff;
      cursor: pointer;
      background: #1677ff;
      border-radius: 8px;
    }
  }
}
.expect-text {
  color: #1677ff;
}
.expect-text-2 {
  color: #999999;
}
.color-black {
  color: #000000;
}
.color-plan {
  padding: 0 4px 0 16px;
  color: #999999;
}
</style>
