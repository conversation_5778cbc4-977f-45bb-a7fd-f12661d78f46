<template>
  <div class="w-full instructor-container">
    <div class="instructor-title">指导教师</div>
    <div class="text-[12px] text-[#999]">指导教师可以审核此项目报名人员，并可以查看签到情况</div>
    <div class="instructor-item">
      <div class="text-[15px] text-[#222]">第一指导教师</div>
      <div class="flex mt-8">
        <div v-if="info.firstInstructorUserid" class="flex instructor-card">
          <div class="instructor-icon flex-0">{{ getSubName(info.firstInstructor.name) }}</div>
          <div class="flex-1">
            <div class="text-[16px] text-[#222]">{{ info.firstInstructor.name }}</div>
            <div class="text-[12px] text-[#999] bm-ellipsis">{{ showPath(info.firstInstructor.path) }}</div>
          </div>
        </div>
        <div v-if="canEdit">
          <div class="edit-instructor" @click="addInstructor('1')"><van-icon name="edit" />编辑</div>
          <div v-if="info.firstInstructorUserid" class="del-instructor" @click="delInstructor('1')"><van-icon name="delete-o" />删除</div>
        </div>
      </div>
    </div>
    <div class="instructor-item">
      <div class="text-[15px] text-[#222]">第二指导教师</div>
      <div class="flex mt-8">
        <div v-if="info.secondInstructorUserid" class="flex instructor-card">
          <div class="instructor-icon flex-0">{{ getSubName(info.secondInstructor.name) }}</div>
          <div class="flex-1">
            <div class="text-[16px] text-[#222]">{{ info.secondInstructor.name }}</div>
            <div class="text-[12px] text-[#999] bm-ellipsis">{{ showPath(info.secondInstructor.path) }}</div>
          </div>
        </div>
        <div v-if="canEdit">
          <div class="edit-instructor" @click="addInstructor('2')"><van-icon name="edit" />编辑</div>
          <div v-if="info.secondInstructorUserid" class="del-instructor" @click="delInstructor('2')"><van-icon name="delete-o" />删除</div>
        </div>
      </div>
    </div>
    <div class="instructor-item">
      <div class="text-[15px] text-[#222]">实验协助人员</div>
      <div class="flex mt-8">
        <div v-if="info.labAssistUserid" class="flex instructor-card">
          <div class="instructor-icon flex-0">{{ getSubName(info.labAssist.name) }}</div>
          <div class="flex-1">
            <div class="text-[16px] text-[#222]">{{ info.labAssist.name }}</div>
            <div class="text-[12px] text-[#999] bm-ellipsis">{{ showPath(info.labAssist.path) }}</div>
          </div>
        </div>
        <div v-if="canEdit">
          <div class="edit-instructor" @click="addInstructor('3')"><van-icon name="edit" />编辑</div>
          <div v-if="info.labAssistUserid" class="del-instructor" @click="delInstructor('3')"><van-icon name="delete-o" />删除</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, computed } from "vue";
import { addLabInstructorApi } from "@/api/modules/laborator";
import { showDialog, showSuccessToast } from "vant";
import { useUserStore } from "@/stores/modules/user";

const emit = defineEmits(["refresh", "add"]);

const props = withDefaults(
  defineProps<{
    info: any;
    id: string;
    applyStatus: string;
    role: string;
    applicantUserid: string;
  }>(),
  {
    info: () => {
      return {};
    },
    id: "",
    applyStatus: "",
    role: "",
    applicantUserid: ""
  }
);
const canEdit = computed(() => {
  return ["5", "7"].findIndex(s => s === props.applyStatus) > -1 && (props.role !== "1" || useUserStore().userInfo.userId === props.applicantUserid);
});

onMounted(() => {});

const getSubName = (name: string) => {
  if (name && name.length < 3) {
    return name;
  } else {
    return name.substring(1, 3);
  }
};
const typeDist = {
  "1": "第一指导教师",
  "2": "第二指导教师",
  "3": "实验协助人员"
};
const addInstructor = (labUserType: string) => {
  // addLabInstructorApi({
  //   labUserType,
  //   labApplyId: props.id,
  //   userid: "15527366190"
  // });
  // showSuccessToast(`编辑${typeDist[labUserType]}成功`);
  emit("add", labUserType);
};
const delInstructor = (labUserType: string) => {
  showDialog({
    title: "删除",
    message: `确删除${typeDist[labUserType]}?`,
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await addLabInstructorApi({
      labUserType,
      labApplyId: props.id,
      userid: ""
    });
    showSuccessToast(`删除${typeDist[labUserType]}成功`);
    emit("refresh");
  });
};
const showPath = (path: string) => {
  const paths = path.split("/").filter(p => p) || [];
  if (paths.length > 1) {
    return paths[paths.length - 2] + "/" + paths[paths.length - 1];
  } else if (paths.length === 1) {
    return paths[0];
  } else {
    return "";
  }
};
</script>

<style scoped lang="scss">
.instructor-container {
  padding: 16px;
  margin-bottom: 12px;
  background: #ffffff;
  .instructor-title {
    padding-left: 8px;
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 15px;
    border-left: #1677ff 3px solid;
  }
  .instructor-item {
    margin-top: 12px;
    .instructor-card {
      align-items: center;
      width: 70%;
      padding: 12px;
      margin-right: 8px;
      background: #f4f5f6;
      border-radius: 4px;
      .instructor-icon {
        display: flex;
        flex: 0 0 auto;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin-right: 8px;
        font-size: 14px;
        line-height: 14px;
        color: #ffffff;
        background: #1677ff;
        border-radius: 40px;
      }
      .bm-ellipsis {
        width: 255px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .edit-instructor,
    .del-instructor {
      padding: 6px 12px;
      font-size: 16px;
      line-height: 16px;
      border: #eeeeee 1px solid;
      border-radius: 2px;
      .van-icon {
        margin-right: 4px;
        color: #1677ff;
      }
    }
    .del-instructor {
      margin-top: 8px;
      .van-icon {
        color: #ee0a24;
      }
    }
  }
}
</style>
