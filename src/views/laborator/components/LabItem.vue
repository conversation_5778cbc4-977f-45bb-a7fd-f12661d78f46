<template>
  <div class="block mb-8">
    <ls-card @click="go" inset>
      <div class="flex">
        <div class="w-42 flex-shrink-0">
          <img src="@/assets/images/laborator/lab.png" class="w-[34px] h-[32px]" alt="" />
        </div>
        <div class="flex-1">
          <div class="top w-full flex">
            <div class="name font-medium text-[17px] text-[#1D2023] flex-1 van-multi-ellipsis--l2">
              <template v-if="info.labUserTypeChinese">
                <LsTag v-for="(item, i) in info.labUserTypeChinese.split(',')" :key="i" type="primary" class="ls-tag" plain :style="{ 'padding-left': i === 0 ? '0' : '4px' }">{{ item }}</LsTag>
              </template>
              {{ info.projectName }}
            </div>
            <div><StatusTag :status="info.applyStatus" :role="role"></StatusTag></div>
          </div>
          <div class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
            实验类型：<span class="text-[#222]">{{ info.labTypeName }}</span>
          </div>
          <template v-if="info.applyStatus !== '0'">
            <div v-if="!info.beginDate" class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
              提交时间：<span class="text-[#222]">{{ info.applyTime }}</span>
            </div>
            <div v-if="info.beginDate" class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
              开始时间：<span class="text-[#222]">{{ info.beginDate }}</span>
            </div>
          </template>
        </div>
      </div>
    </ls-card>
  </div>
</template>
<script lang="ts" setup>
import { LsCard, LsTag } from "ls-desgin";
import { useRouter } from "vue-router";
import StatusTag from "./StatusTag.vue";

const props = withDefaults(
  defineProps<{
    info: any;
    role: any;
  }>(),
  {
    info: () => {
      return {};
    },
    role: "1"
  }
);

const router = useRouter();

const go = () => {
  if (props.role === "0") {
    router.push({ name: "LaboratorLabInfo", query: { id: props.info.id } });
  } else {
    router.push({ name: "LaboratorDetail", query: { id: props.info.id, role: props.role } });
  }
};
</script>
