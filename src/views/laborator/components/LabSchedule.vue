<template>
  <div class="w-full lab-schedule-container">
    <div class="lab-schedule-title">实验室安排</div>
    <van-button v-if="['1', '2'].findIndex(s => s === applyStatus) > -1 && role !== '1' && !(data.scheduleList && data.scheduleList.length)" type="primary" size="small" @click="goPlan" style="margin-bottom: 12px">配置方案</van-button>
    <div v-if="data.scheduleList && data.scheduleList.length && !(role === '1' && ['0', '1', '2', '3', '4', '6'].findIndex(s => s === applyStatus) > -1)" class="schedule-container">
      <div class="flex items-center" style="justify-content: space-between">
        <div class="flex items-center">
          <img src="@/assets/images/laborator/lab.png" class="w-[20px] h-[20px] mr-[6px]" alt="" />
          <div class="text-[15px]">{{ data.roomName }}</div>
          <div class="capacity-tag">容量：{{ data.capacity }}人</div>
        </div>
        <van-button v-if="['1', '2', '3', '5', '7'].findIndex(s => s === applyStatus) > -1 && role !== '1'" plain size="small" @click="goPlan" style="flex: 0 0 auto"> <van-icon name="edit" color="#1677ff" /> 编辑 </van-button>
        <van-button v-if="['8', '9'].findIndex(s => s === applyStatus) > -1 && role !== '1'" plain size="small" @click="goPlan" style="flex: 0 0 auto"> <van-icon name="eye-o" color="#1677ff" /> 查看 </van-button>
      </div>
      <div v-for="s in scheduleList" :key="s.id" class="schedule-item">
        <div class="schedule-item-info">
          周次：<span>第{{ s.teachWeeks.toString() }}(周)</span>
        </div>
        <div class="schedule-item-info">
          时间：<span>星期{{ dayDist[s.dayOfWeek] }} 第{{ s.lesson }}节</span>
        </div>
      </div>
      <div v-if="role !== '0'" class="schedule-info">最后编辑人：{{ data.lastUpdateName }}</div>
      <div v-if="role !== '0'" class="schedule-info">最后编辑时间：{{ data.lastUpdateTime }}</div>
    </div>
    <van-steps direction="vertical" :active="-1" active-color="#07c160">
      <van-step v-for="record of approvalRecord" :key="record.id">
        <template #inactive-icon>
          <van-icon v-if="record.approvalStatus === 0" name="more" size="20" />
          <van-icon v-if="record.approvalStatus === 1" name="checked" color="#07c160" size="20" />
          <van-icon v-if="record.approvalStatus === 2" name="clear" color="#ee0a24" size="20" />
        </template>
        <div class="approval-step">
          <div class="approval-step-info">
            <div class="text-[16px] text-[#222]">
              {{ record.name }}<span v-if="record.operatorUserType === '1'">（申请人）</span><span v-if="record.operatorUserType === '2'">（实验室管理员）</span><span v-if="record.operatorUserType === '3'">（实验室主任）</span>
            </div>
            <div class="text-[14px] text-[#999] approval-time">{{ record.approvalTime }}</div>
          </div>
          <div class="approval-step-info">
            <div class="text-[14px] text-[#999]">{{ record.operatorUserid }}</div>
            <div v-if="record.approvalStatus === 0" class="text-[16px] text-[#1677ff]">审批中</div>
            <div v-if="record.approvalStatus === 1" class="text-[16px] text-[#07c160]">已同意</div>
            <div v-if="record.approvalStatus === 2" class="text-[16px] text-[#ee0a24]">未通过</div>
          </div>
        </div>
      </van-step>
    </van-steps>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { getLabApplySchedulingByLabApplyIdApi } from "@/api/modules/laborator";
import { useRoute, useRouter } from "vue-router";
// import { showDialog, showSuccessToast } from "vant";

const router = useRouter();
const props = withDefaults(
  defineProps<{
    approvalRecord: any[];
    id: string;
    role: string;
    applyStatus: string;
  }>(),
  {
    approvalRecord: () => {
      return [];
    },
    id: "",
    role: "",
    applyStatus: ""
  }
);
const { query } = useRoute();
const data = ref<any>({ scheduleList: [] });
const dayDist = ["日", "一", "二", "三", "四", "五", "六"];
onMounted(() => {
  getData();
});

const getData = () => {
  getLabApplySchedulingByLabApplyIdApi(query.id as string).then(res => {
    data.value = res.data;
  });
};

const goPlan = () => {
  router.push({ name: "LaboratorPlan", query: { id: props.id, role: props.role } });
};

const scheduleList = computed(() => {
  const list: any = [];
  data.value.scheduleList.forEach((d: any) => {
    const index = list.findIndex(l => l.lesson === d.lesson && l.dayOfWeek === d.dayOfWeek);
    if (index === -1) {
      d.teachWeeks = [d.teachWeek];
      list.push(d);
    } else {
      list[index].teachWeeks.push(d.teachWeek);
    }
  });
  return list;
});

// const delPlan = () => {
//   showDialog({
//     title: "删除",
//     message: "确定删除?",
//     showCancelButton: true,
//     showConfirmButton: true,
//     confirmButtonColor: "var(--van-primary-color)"
//   }).then(async () => {
//     await savePlanCourseApi({
//       labApplyId: props.id,
//       creatorUserType: props.role,
//       labApplySchedulingList: []
//     });
//     showSuccessToast("删除成功");
//     getData();
//   });
// };
</script>

<style scoped lang="scss">
.approval-time {
  flex: 0 0 auto;
}
.approval-step {
  .approval-step-info {
    display: flex;
    justify-content: space-between;
    padding-bottom: 4px;
  }
}
.lab-schedule-container {
  padding: 16px;
  margin-bottom: 12px;
  background: #ffffff;
  .lab-schedule-title {
    padding-left: 8px;
    margin-bottom: 12px;
    font-size: 16px;
    line-height: 15px;
    border-left: #1677ff 3px solid;
  }
  .schedule-container {
    width: 100%;
    padding: 12px;
    margin-bottom: 12px;
    border: #dddddd 1px solid;
    border-radius: 8px;
    .capacity-tag {
      flex: 0 0 auto;
      padding: 4px 8px;
      margin-right: 8px;
      margin-left: 8px;
      font-size: 12px;
      line-height: 12px;
      color: #666666;
      background: #f4f4f4;
      border-radius: 2px;
    }
    .schedule-item {
      padding: 8px 12px;
      margin-top: 8px;
      background: #eef5f6;
      border-radius: 4px;
      .schedule-item-info {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #777777;
        span {
          color: #222222;
        }
      }
    }
    .schedule-info {
      margin-top: 8px;
      font-size: 12px;
      color: #666666;
    }
    .schedule-info:last-child {
      margin-top: 4px;
    }
  }
  .van-step::after {
    border: none !important;
  }
}
</style>
