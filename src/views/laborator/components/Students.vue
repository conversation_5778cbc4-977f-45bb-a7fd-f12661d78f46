<template>
  <div class="w-full lab-students-container">
    <div v-if="type === '0'" class="lab-students-title-c">
      <div class="lab-students-title">审批申请（{{ records.length }}人）</div>
      <van-button v-if="records.length > 0" @click="doApproveAll" type="primary" plain size="small"><van-icon name="success" /> 全部通过</van-button>
    </div>
    <div v-else class="lab-students-title-c">
      <div class="lab-students-title">参与人（{{ records.length }}人）</div>
      <van-button @click="showTeacherCheckInRecords" type="primary" plain size="small">教师签到记录</van-button>
    </div>
    <van-notice-bar v-if="type === '0'" left-icon="info-o">审核超过168小时，将自动不通过</van-notice-bar>
    <empty v-if="records.length === 0" class="!pt-0" :text="`暂无${type === '0' ? '申请人' : '参与人'}`"></empty>
    <div v-for="record in records" :key="record.id" class="student-c">
      <div class="student-c-t">
        <div class="student-name">
          {{ record.name }}<span>{{ record.userId }}</span>
        </div>
        <div class="student-btn">
          <van-button v-if="type === '0'" @click="doApprove(record, '1')" plain size="small"><van-icon name="success" color="#1677ff" /> 通过</van-button>
          <van-button v-if="type === '0'" @click="doApprove(record, '2')" plain size="small" style="margin-left: 8px; color: red">不通过</van-button>
          <van-button v-if="type === '1'" @click="showCheckInRecords(record)" plain size="small">查看</van-button>
          <van-button v-if="type === '1'" @click="doRemove(record)" plain size="small" style="margin-left: 8px; color: red">移除</van-button>
        </div>
      </div>
      <div class="student-info">
        所在部门：<span>{{ record.path }}</span>
      </div>
      <div v-if="type === '0'" class="student-info">
        提交时间：<span>{{ record.approvalTime }}</span>
      </div>
    </div>
    <van-dialog v-model:show="showTeacher" title="教师签到记录" teleport="body" class-name="query-exam-detail-dialog" confirm-button-text="我知道了">
      <div class="sign-detail-container">
        <template v-for="s in signInRecord" :key="s.id">
          <div class="sign-detail-date">{{ s.title }}</div>
          <div class="sign-detail-name">
            {{ s.name }}<span class="text-[12px]">（{{ s.labUserType }}）</span>
          </div>
          <div class="sign-detail-time">
            {{ s.signInTime }}
            <div style="color: #07c160">已签到</div>
          </div>
        </template>
      </div>
    </van-dialog>
    <van-dialog v-model:show="showStudent" title="签到记录" teleport="body" class-name="query-exam-detail-dialog" confirm-button-text="我知道了">
      <div class="sign-detail-container">
        <template v-for="s in signInRecord2" :key="s.id">
          <div class="sign-detail-date">{{ s.title }} 第{{ s.signInLesson }}节</div>
          <div class="sign-detail-time">
            {{ s.signInTime }}
            <div style="color: #07c160">已签到</div>
          </div>
        </template>
      </div>
    </van-dialog>
  </div>
</template>
<script lang="ts" setup>
import { examineAndApproveApi, removeParticipantApi, teacherCheckInRecordsApi, viewCheckInRecordApi, getCurrentSchoolCalendarApi } from "@/api/modules/laborator";
import { showDialog, showSuccessToast } from "vant";
import { ref, onMounted } from "vue";
import dayjs from "dayjs";

const props = withDefaults(
  defineProps<{
    info?: any;
    records: any[];
    id: string;
    type?: string;
  }>(),
  {
    info: () => {
      return {};
    },
    records: () => {
      return [];
    },
    id: "",
    type: "0"
  }
);
const emit = defineEmits(["refresh"]);

const schoolCalendar = ref<any>({});
onMounted(() => {
  if (props.type === "1") {
    setTimeout(() => {
      getCurrentSchoolCalendarApi().then(res => {
        (res.weekList || []).forEach((week, index) => {
          week.forEach(w => {
            if (w.date) {
              schoolCalendar.value[w.date] = `第${index + 1}周 ${w.name.replace("周", "星期")}`;
            }
          });
        });
      });
    }, 100);
  }
});
const doApprove = (record: any, joinStatus: string) => {
  const action = joinStatus === "1" ? "通过" : "不通过";
  showDialog({
    title: action,
    message: `确定${action}${record.name}的申请?`,
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await examineAndApproveApi({
      labApplyId: props.id,
      joinStatus,
      applyJoinUserId: [record.id]
    });
    showSuccessToast("操作成功");
    emit("refresh");
  });
};

const doApproveAll = () => {
  showDialog({
    title: "全部通过",
    message: "确定全部通过?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await examineAndApproveApi({
      labApplyId: props.id,
      joinStatus: "1",
      applyJoinUserId: props.records.map(r => r.id)
    });
    showSuccessToast("全部通过成功");
    emit("refresh");
  });
};

const doRemove = (record: any) => {
  showDialog({
    title: "移除",
    message: `确定移除${record.name}?`,
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await removeParticipantApi({ labApplyId: props.id, userids: [record.userId] });
    showSuccessToast("操作成功");
    emit("refresh");
  });
};

const showTeacher = ref(false);
const signInRecord = ref<any>([]);
const showTeacherCheckInRecords = () => {
  teacherCheckInRecordsApi(props.id).then(res => {
    if (res.data.signInRecord && res.data.signInRecord.length) {
      signInRecord.value = [];
      res.data.signInRecord.forEach(s => {
        s.date = dayjs(s.signInTime).format("YYYY-MM-DD");
        s.title = schoolCalendar.value[s.date];
        signInRecord.value.push(s);
      });
    }
    showTeacher.value = true;
  });
};
const showStudent = ref(false);
const signInRecord2 = ref<any>([]);
const showCheckInRecords = (record: any) => {
  viewCheckInRecordApi(props.id, record.userId).then(res => {
    signInRecord2.value = res.data;
    if (res.data && res.data.length) {
      signInRecord2.value = [];
      res.data.forEach(s => {
        s.date = dayjs(s.signInTime).format("YYYY-MM-DD");
        s.title = schoolCalendar.value[s.date];
        signInRecord2.value.push(s);
      });
    }
    showStudent.value = true;
  });
};
</script>

<style scoped lang="scss">
.sign-detail-container {
  max-height: 60vh;
  padding: 4px 16px;
  overflow: hidden auto;
  .sign-detail-date {
    margin-top: 12px;
  }
  .sign-detail-name {
    font-size: 14px;
    color: #666666;
  }
  .sign-detail-time {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #999999;
  }
}
.approval-step {
  .approval-step-info {
    display: flex;
    justify-content: space-between;
    padding-bottom: 4px;
  }
}
.lab-students-container {
  padding: 0 16px 16px;
  margin-bottom: 12px;
  background: #ffffff;
  .lab-students-title-c {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    .lab-students-title {
      height: 15px;
      padding-left: 8px;
      font-size: 16px;
      line-height: 15px;
      border-left: #1677ff 3px solid;
    }
  }
}
.student-c {
  padding: 12px 0;
  border-bottom: #eeeeee 1px solid;
  .student-c-t {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .student-name {
      font-size: 16px;
      color: #222222;
      span {
        padding-left: 6px;
        font-size: 14px;
        color: #666666;
      }
    }
  }
  .student-info {
    margin-top: 4px;
    font-size: 14px;
    line-height: 14px;
    color: #aaaaaa;
    span {
      color: #444444;
    }
  }
}
.student-c:last-child {
  border-bottom: none;
}
</style>
