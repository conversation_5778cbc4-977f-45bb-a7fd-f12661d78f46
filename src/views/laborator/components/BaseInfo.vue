<template>
  <div v-if="role === '0'" class="w-full base-info-container">
    <div class="w-full flex">
      <div class="name font-medium text-[16px] text-[#1D2023] flex-1">{{ info.projectName }}</div>
      <div class="status-tag">
        <StatusTag :status="props.info.applyStatus" :role="role"></StatusTag>
      </div>
    </div>
    <div v-if="['0', '1', '2', '3'].findIndex(s => s === applyStatus) > -1" class="name text-[13px] text-[#999] flex-1">{{ info.applyTime }}</div>
    <div v-else class="name text-[13px] text-[#999] flex-1">{{ info.beginDate }}</div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验内容:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labContent }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">项目简介:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.projectProfile }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验类型:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labTypeName }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">归属学院:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.collegeName }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">开放时长:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.classHour }}课时</div>
    </div>
    <div v-if="info.firstInstructor && info.firstInstructor.name" class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">第一指导<br />教师:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.firstInstructor?.name }}</div>
    </div>
    <div v-if="info.secondInstructor && info.secondInstructor.name" class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">第二指导<br />教师:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.secondInstructor?.name }}</div>
    </div>
    <div v-if="info.labAssist && info.labAssist.name" class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验协助<br />人员:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labAssist?.name }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验室:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labName }}</div>
    </div>
  </div>
  <div v-else class="w-full base-info-container">
    <div class="w-full flex">
      <div class="name font-medium text-[16px] text-[#1D2023] flex-1">{{ info.projectName }}</div>
      <div class="status-tag">
        <StatusTag :status="props.info.applyStatus" :role="role"></StatusTag>
      </div>
    </div>
    <div v-if="['0', '1', '2', '3'].findIndex(s => s === applyStatus) > -1" class="name text-[13px] text-[#999] flex-1">{{ info.applyTime }}</div>
    <div v-else class="name text-[13px] text-[#999] flex-1">{{ info.beginDate }}</div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">申请人:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.applicantName }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验内容:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labContent }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div v-if="['0', '1', '2', '3'].findIndex(s => s === applyStatus) > -1" class="name text-[15px] text-[#777] flex-0 w-80">申请开放<br />时长:</div>
      <div v-else class="name text-[15px] text-[#777] flex-0 w-80">开放时长:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.classHour }}课时</div>
    </div>
    <div v-if="['0', '1', '2', '3'].findIndex(s => s === applyStatus) > -1" class="yqkc-container">
      <div class="flex">
        <div class="yqkc-item">
          预计每周课时:<span>{{ info.expectWeekClassHour }}课时</span>
        </div>
        <div class="yqkc-item">
          预计每天课时:<span>{{ info.expectDayClassHour }}课时</span>
        </div>
      </div>
      <div class="yqkc-item">
        优先周次:<span>{{ info.priorityWeek }}</span>
      </div>
      <div class="yqkc-item">
        优先节次:<span>{{ info.prioritySection }}</span>
      </div>
    </div>
    <div v-if="['0', '1', '2', '3'].findIndex(s => s === applyStatus) > -1" class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">预期人数:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.expectPeople }}人</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验类型:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labTypeName }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">归属学院:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.collegeName }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">实验室:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.labName }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">项目简介:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.projectProfile }}</div>
    </div>
    <div class="w-full flex mt-6">
      <div class="name text-[15px] text-[#777] flex-0 w-80">详情:</div>
      <div class="name text-[15px] text-[#222] flex-1">{{ info.projectDetails }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import StatusTag from "./StatusTag.vue";

const props = withDefaults(
  defineProps<{
    info: any;
    role: string;
    applyStatus: string;
  }>(),
  {
    info: () => {
      return {};
    },
    role: "",
    applyStatus: ""
  }
);
</script>

<style scoped lang="scss">
.base-info-container {
  padding: 16px;
  margin-bottom: 12px;
  background: #ffffff;
}
.yqkc-container {
  width: 100%;
  padding: 8px 12px 4px;
  margin-top: 6px;
  background: #f4f5f6;
  border: #eeeeee 1px solid;
  border-radius: 4px;
  .yqkc-item {
    min-width: 50%;
    margin-bottom: 4px;
    color: #999999;
    span {
      color: #333333;
    }
  }
}
</style>
