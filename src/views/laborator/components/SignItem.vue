<template>
  <ls-card class="field-card m-[8px]">
    <div class="flex">
      <div class="w-42 flex-shrink-0">
        <img src="@/assets/images/laborator/lab.png" class="w-[34px] h-[32px]" alt="" />
      </div>
      <div class="flex-1">
        <div class="top w-full flex">
          <div class="name font-medium text-[17px] text-[#1D2023] flex-1">
            <LsTag v-if="useUserStore().userInfo.userId === info.firstInstructorUserid || useUserStore().userInfo.userId === info.secondInstructorUserid" type="primary" class="ls-tag" plain>指导</LsTag>
            <LsTag v-if="useUserStore().userInfo.userId === info.labAssistUserid" type="primary" class="ls-tag" plain>协助</LsTag>
            {{ props.info.projectName }}
          </div>
          <div>
            <LsTag v-if="signIn === 0" type="primary" ghost>待签</LsTag>
            <LsTag v-else type="success" ghost>已签</LsTag>
          </div>
        </div>
        <div class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
          创建人：<span class="text-[#222]">{{ info.applicantName }}</span>
        </div>
        <div class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
          项目开始时间：<span class="text-[#222]">{{ info.beginDate }}</span>
        </div>
        <div class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
          签到方式：<span class="text-[#222]">{{ info.signType }}</span>
        </div>
        <div class="bottom mt-4 text-[14px] leading-18px font-normal text-[#979DAB]">
          实验类型：<span class="text-[#222]">{{ info.labTypeName }}</span>
        </div>
      </div>
    </div>
  </ls-card>
</template>
<script lang="ts" setup>
import { LsTag, LsCard } from "ls-desgin";
import { useUserStore } from "@/stores/modules/user";

const props = withDefaults(
  defineProps<{
    info: any;
    signIn: number;
  }>(),
  {
    info: () => {
      return {};
    }
  }
);
</script>
