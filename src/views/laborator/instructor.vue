<template>
  <LsLayout>
    <LsCard class="field-card m-[8px]">
      <div class="user-name text-[20px] font-semibold">{{ userInfo.name }}</div>
      <div class="user-id mt-4 text-[16px] text-[#979dab]">工号&nbsp;:&emsp;{{ userInfo.userId }}</div>
    </LsCard>
    <LsCard v-if="labApplySchedulingList.length" class="field-card m-[8px]">
      <div class="jrqd">今日签到（{{ today }}）</div>
      <div v-if="signIn !== 0" class="sign-btn active">已签到</div>
      <div v-else-if="isClock1 || isClock2" @click="doSignIn" class="sign-btn active">签到</div>
      <div v-else class="sign-btn">签到</div>
      <div v-if="isClock1" class="jrqd">
        <div class="flex" style="align-items: center">
          <img src="@/assets/images/clockIn/students_success.png" class="w-[20px] h-[20px]" alt="" />
          <span class="text-[#5C6371] ml-[5px]">
            已进入签到范围：
            <span class="text-[#1677FF] omit" @click="goPosition(address1)">
              {{ address1.campusName }}
            </span>
          </span>
        </div>
      </div>
      <div v-else-if="isClock2" class="jrqd">
        <div class="flex" style="align-items: center">
          <img src="@/assets/images/clockIn/students_success.png" class="w-[20px] h-[20px]" alt="" />
          <span class="text-[#5C6371] ml-[5px]">
            已进入签到范围：
            <span class="text-[#1677FF] omit" @click="goPosition(address2)">
              {{ address2.campusName }}
            </span>
          </span>
        </div>
      </div>
      <div v-else class="jrqd">
        <div class="flex" style="align-items: center">
          <img src="@/assets/images/clockIn/students_warn.png" class="w-[20px] h-[20px]" alt="" />
          <span class="text-[#5C6371] ml-[5px]">您当前不在签到范围：</span>
        </div>
        <div class="text-[#1677FF] omit">
          <span @click="goPosition(address1)">{{ address1.campusName }}</span>
          /
          <span @click="goPosition(address2)">{{ address2.campusName }}</span>
        </div>
      </div>
    </LsCard>
    <LsCard v-if="labApplySchedulingList.length === 0" class="field-card m-[8px]">
      <Empty class="!pt-0" text="暂无数据"></Empty>
    </LsCard>
    <SignItem v-for="info in labApplySchedulingList" :key="info.id" :info="info" :sign-in="signIn"></SignItem>
  </LsLayout>
</template>

<script setup lang="ts" name="LaboratorInstructor">
import { useUserStore } from "@/stores/modules/user";
import { LsLayout, LsCard } from "ls-desgin";
import { computed, ref, onMounted } from "vue";
import { getSignAddressAndLessonApi, teacherSignInApi, getLocation } from "@/api/modules/laborator";
import dayjs from "dayjs";
import Empty from "@/components/empty.vue";
import SignItem from "./components/SignItem.vue";
import { showSuccessToast } from "vant";
import { useLsMap } from "@/hooks/useLsMap";
import { useRouter } from "vue-router";

const userInfo = computed(() => useUserStore().userInfo || {});
const today = dayjs().format("YYYY-MM-DD");
const signIn = ref(0);
const labApplySchedulingList = ref<any>([]);
const address1 = ref<any>({});
const address2 = ref<any>({});
const isClock1 = ref(false);
const isClock2 = ref(false);
onMounted(() => {
  getData();
});

const getData = () => {
  getSignAddressAndLessonApi().then((res: any) => {
    signIn.value = res.data.signIn;
    address1.value = JSON.parse(res.data.address1 || "{}");
    address2.value = JSON.parse(res.data.address2 || "{}");
    beforeFn1(address1.value.point.split(","), address1.value.realm * 1);
    labApplySchedulingList.value = res.data.labApplySchedulingList;
    setTimeout(() => {
      if (!isClock1.value && !isClock2.value) {
        beforeFn1(address1.value.point.split(","), address1.value.realm * 1);
      }
    }, 2000);
  });
};

const beforeFn1 = async (point: any, signInRange: any) => {
  const { isJudgeRange } = useLsMap(undefined, { lat: point[0], lng: point[1], radius: signInRange });
  getLocation((path: { lat: number; lng: number; radius: number }) => {
    try {
      isClock1.value = isClock1.value || isJudgeRange({ lat: point[0], lng: point[1], radius: signInRange as number }, path);
    } catch (error) {
      console.error(error, "判断范围失败");
    }
    setTimeout(() => {
      beforeFn2(address2.value.point.split(","), address2.value.realm * 1);
    }, 200);
  });
};

const beforeFn2 = async (point: any, signInRange: any) => {
  const { isJudgeRange } = useLsMap(undefined, { lat: point[0], lng: point[1], radius: signInRange });
  getLocation((path: { lat: number; lng: number; radius: number }) => {
    try {
      isClock2.value = isClock2.value || isJudgeRange({ lat: point[0], lng: point[1], radius: signInRange as number }, path);
    } catch (error) {
      console.error(error, "判断范围失败");
    }
  });
};

const doSignIn = () => {
  teacherSignInApi({
    userType: 1,
    labApplyIds: labApplySchedulingList.value.map(l => l.id)
  }).then(() => {
    showSuccessToast("签到成功");
    getData();
  });
};
const router = useRouter();
const goPosition = (address: any) => {
  router.push({ name: "LaboratorPosition", query: { point: address.point, radius: address.realm } });
};
</script>
<style scoped lang="scss">
.jrqd {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  text-align: center;
}
.sign-btn {
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 150px;
  margin: 12px auto;
  font-size: 20px;
  color: #ffffff;
  cursor: pointer;
  background: #aaaaaa;
  border: #eeeeee 8px solid;
  border-radius: 150px;
}
.sign-btn.active {
  background: #1677ff;
}
</style>
