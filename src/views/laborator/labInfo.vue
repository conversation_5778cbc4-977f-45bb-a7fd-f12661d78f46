<template>
  <LsLayout>
    <BaseInfo :info="info" role="0"></BaseInfo>
    <LabSchedule :approval-record="[]" :id="id" role="0"></LabSchedule>
    <SignInfo :id="id"></SignInfo>
    <template #footer>
      <div v-if="useUserStore().userInfo.userType !== 1 && info.applyStatus !== '8' && info.applyStatus !== '9'" class="w-full p-12 flex-shrink-0 footer-shadow">
        <div v-if="labApplyJoinCurrent?.joinStatus === 2" class="mb-[8px]" style="color: #ee0a24; text-align: center">
          申请未通过，请重新申请 <span style="color: #aaaaaa">{{ approvalTime }}</span>
        </div>
        <van-button v-if="!labApplyJoinCurrent.id || labApplyJoinCurrent.joinStatus === 0 || labApplyJoinCurrent.joinStatus === 2" class="flex-1" type="primary" block @click="doApply">
          {{ labApplyJoinCurrent && labApplyJoinCurrent.joinStatus === 0 ? "已报名" : "报名" }}
        </van-button>
        <van-button v-if="labApplyJoinCurrent?.joinStatus === 1 && info.applyStatus === '5'" class="flex-1" type="primary" block color="#aaaaaa">等待项目开始</van-button>
        <van-button @click="goSign" v-if="labApplyJoinCurrent?.joinStatus === 1 && info.applyStatus === '7'" class="flex-1" type="primary" block>签到</van-button>
      </div>
    </template>
  </LsLayout>
</template>

<script setup lang="ts" name="LaboratorLabInfo">
import { LsLayout } from "ls-desgin";
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getLabInfoApi, applyForLabApi, getLabApplySignAddressAndLessonApi } from "@/api/modules/laborator";
import BaseInfo from "./components/BaseInfo.vue";
import LabSchedule from "./components/LabSchedule.vue";
import SignInfo from "./components/SignInfo.vue";
import { showDialog, showSuccessToast, showToast } from "vant";
import { useUserStore } from "@/stores/modules/user";
import dayjs from "dayjs";

const router = useRouter();
const { query } = useRoute();
const id = ref("");
const info = ref<any>({});
const labApplyJoinCurrent = ref<any>({});
onMounted(() => {
  id.value = query.id as string;
  getDetail();
});

const approvalTime = computed(() => {
  return dayjs(labApplyJoinCurrent.value.approvalTime).format("YYYY-MM-DD HH:mm");
});

const getDetail = () => {
  getLabInfoApi(id.value).then((res: any) => {
    info.value = res.data?.labInfo || {};
    labApplyJoinCurrent.value = res.data?.labApplyJoinCurrent || {};
  });
  getLabApplySignAddressAndLessonApi(id.value);
};
const goSign = () => {
  router.push({ name: "LaboratorSignin", query: { id: id.value } });
};

const doApply = () => {
  if (labApplyJoinCurrent.value && labApplyJoinCurrent.value.joinStatus === 0) {
    showToast("您已经报名，请耐心等待管理员审核！");
    return;
  }
  showDialog({
    title: "报名",
    message: "确定报名?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await applyForLabApi(id.value);
    showSuccessToast("报名成功");
    getDetail();
  });
};
</script>
<style scoped lang="scss">
.footer-shadow {
  box-shadow: 2px 2px 8px 1px rgb(0 0 0 / 8%) !important;
}
.term-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  text-align: center;
  .van-icon {
    color: #ee0a24;
  }
}
</style>
