<template>
  <LsLayout>
    <div class="pt-8" style="background: #ffffff">
      <div class="p-10 top w-full flex">
        <div class="name font-medium text-[17px] text-[#1D2023] flex-1 title">实验室申请</div>
        <LsTag class="ls-tag">草稿</LsTag>
      </div>
    </div>
    <van-form ref="vanFormRef" input-align="right" error-message-align="right" validate-trigger="onChange" label-width="120" style="margin-bottom: 16px">
      <van-field label="实验名称" v-model="form.projectName" placeholder="请输入" maxlength="100" show-word-limit required :rules="requiredRule('请输入实验名称')" />
      <van-field
        label="实验内容"
        :rules="requiredRule('请输入实验内容')"
        v-model="form.labContent"
        required
        class="van-field-reason"
        label-align="top"
        input-align="left"
        rows="2"
        type="textarea"
        maxlength="1000"
        placeholder="请输入"
        show-word-limit
      ></van-field>
      <van-field
        label="申请开放时长"
        v-model="form.classHour"
        placeholder="请输入"
        required
        :rules="[
          { required: true, message: '请输入申请开放时长' },
          { validator, required: true, message: '申请开放时长必须为正整数' }
        ]"
      >
        <template #right-icon>课时</template>
      </van-field>
      <van-field
        label="预期每周课时"
        v-model="form.expectWeekClassHour"
        placeholder="请输入"
        :rules="[
          { validator: validator1, required: false, message: '预期每周课时必须为正整数' },
          { validator: val => val < 61, required: false, message: '预期每周课时必须小于60' }
        ]"
      >
        <template #right-icon>课时</template>
      </van-field>
      <van-field
        label="预期每天课时"
        v-model="form.expectDayClassHour"
        placeholder="请输入"
        :rules="[
          { validator: validator1, required: false, message: '预期每天课时必须为正整数' },
          { validator: val => val < 13, required: false, message: '预期每天课时必须小于12' }
        ]"
      >
        <template #right-icon>课时</template>
      </van-field>
      <van-field label="优先周次" class="van-field-period" v-model="form.priorityWeek" label-align="top" input-align="left">
        <template #input>
          <div class="week-list">
            <div class="week-item" v-for="item in schoolCalendar.weekList.length" :key="item" :class="{ active: priorityWeek.findIndex(p => p === item + '') > -1 }" @click="priorityWeekToggle(item + '')">{{ item }}</div>
          </div>
        </template>
      </van-field>
      <van-field label="优先节次" class="van-field-period" v-model="form.prioritySection" label-align="top" input-align="left">
        <template #input>
          <div class="week-list">
            <div class="week-item" v-for="item in 12" :key="item" :class="{ active: prioritySection.findIndex(p => p === item + '') > -1 }" @click="prioritySectionToggle(item + '')">{{ item }}</div>
          </div>
        </template>
      </van-field>
      <van-field
        label="预期人数"
        v-model="form.expectPeople"
        placeholder="请输入"
        :rules="[
          { validator: validator1, required: false, message: '预期人数必须为正整数' },
          { validator: val => val < 1001, required: false, message: '预期人数必须小于1000' }
        ]"
      >
        <template #right-icon>人</template>
      </van-field>
      <van-field v-model="form.labName" :rules="requiredRule('请选择预期实验室')" label="预期实验室" @click="showLab = true" required readonly is-link placeholder="请选择" />
      <van-field v-model="form.roomName" :rules="requiredRule('请选择预期房间')" label="预期房间" @click="showRoom = true" required readonly is-link placeholder="请选择" />
      <van-field v-model="form.labTypeName" :rules="requiredRule('请选择实验类型')" label="实验类型" @click="showLabType = true" required readonly is-link placeholder="请选择" />
      <van-field v-model="form.collegeName" :rules="requiredRule('请选择归属学院')" label="归属学院" @click="showCollege = true" required readonly is-link placeholder="请选择" />
      <van-field
        label="项目简介"
        :rules="requiredRule('请输入项目简介')"
        v-model="form.projectProfile"
        required
        class="van-field-reason"
        label-align="top"
        input-align="left"
        rows="2"
        type="textarea"
        maxlength="200"
        placeholder="请输入"
        show-word-limit
      ></van-field>

      <van-field
        label="详情"
        :rules="requiredRule('请输入详情')"
        v-model="form.projectDetails"
        required
        class="van-field-reason"
        label-align="top"
        input-align="left"
        rows="2"
        type="textarea"
        maxlength="200"
        placeholder="请输入"
        show-word-limit
      ></van-field>
    </van-form>
    <template #footer>
      <div class="w-full flex-shrink-0 flex">
        <div v-if="query.id" @click="doDel" class="w-60 del-btn">
          <div>
            <van-icon name="delete-o" size="22" />
            <div>删除</div>
          </div>
        </div>
        <LsButtonBar main-text="提交" secondary-text="存草稿" type="doubleMain" @button-click="handleSubmit" :main-loading="loading" />
      </div>
    </template>
    <OptionsPicker :visible="showLab" title="选择实验室" :value="currentLab" :options="labOptions" @close-picker="showLab = false" @on-confirm="(val: any) => handleSelectLab(val)" />
    <OptionsPicker :visible="showRoom" title="选择房间" :value="form.labRoomInfoId" :options="roomOptions" @close-picker="showRoom = false" @on-confirm="(val: any) => handleSelectRoom(val)" />
    <OptionsPicker :visible="showLabType" title="选择实验类型" :value="form.labTypeId" :options="labTypeOptions" @close-picker="showLabType = false" @on-confirm="(val: any) => handleSelectLabType(val)" />
    <OptionsPicker :visible="showCollege" title="选择归属学院" :value="form.collegeCode" :options="collegeOptions" @close-picker="showCollege = false" @on-confirm="(val: any) => handleSelectCollege(val)" />
  </LsLayout>
</template>

<script setup lang="ts" name="LaboratorSubmit">
import { LsLayout, LsButtonBar, LsTag } from "ls-desgin";
import { getLabTypeApi, getCollegeListApi, getLabRoomListApi, getRoomListByLabApi, addApplyApi, getApplyInfoApi, labApplyRecordsDelApi, getCurrentSchoolCalendarApi } from "@/api/modules/laborator";
import { ref, onMounted } from "vue";
import OptionsPicker from "./components/OptionsPicker.vue";
import type { FormInstance } from "vant";
import { showToast, showDialog, showSuccessToast } from "vant";
import { useRoute } from "vue-router";

const vanFormRef = ref<FormInstance>();
const loading = ref(false);
const labOptions = ref<any>([]);
const roomOptions = ref<any>([]);
const labTypeOptions = ref<any>([]);
const collegeOptions = ref<any>([]);
const { query } = useRoute();
const form = ref<any>({
  labRoomInfoId: null,
  labTypeId: "",
  collegeCode: ""
});

const validator = (val: string) => /^[1-9]\d*$/.test(val);
const validator1 = (val: string) => !val || /^[1-9]\d*$/.test(val);

const schoolCalendar = ref<any>({ weekList: [] });
onMounted(() => {
  getCurrentSchoolCalendarApi().then(res => {
    schoolCalendar.value = res;
  });
  if (query.id) {
    getApplyInfoApi(query.id as string).then((res: any) => {
      form.value = res.data?.labInfo || {
        labRoomInfoId: "",
        labTypeId: "",
        collegeCode: ""
      };
      if (form.value.priorityWeek) {
        priorityWeek.value = form.value.priorityWeek.split(",");
      }
      if (form.value.prioritySection) {
        prioritySection.value = form.value.prioritySection.split(",");
      }
      currentLab.value = form.value.labCode;
      if (currentLab.value) {
        getRoomListByLabApi(currentLab.value).then((res: any) => {
          roomOptions.value = res.data.map(d => ({ text: d.roomName, value: d.id }));
        });
      }
    });
  }
  getLabTypeApi().then((res: any) => {
    labTypeOptions.value = (res.data || []).map(d => ({ text: d.labTypeName, value: d.id }));
  });
  getCollegeListApi().then((res: any) => {
    collegeOptions.value = (res.data || []).map(d => ({ text: d.collegeName, value: d.collegeCode }));
  });
  getLabRoomListApi().then((res: any) => {
    labOptions.value = [];
    (res.data || []).forEach(d => {
      if (labOptions.value.findIndex(l => l.value === d.labCode) === -1) {
        labOptions.value.push({ text: d.labName, value: d.labCode });
      }
    });
  });
});

const requiredRule = text => {
  return [{ required: true, message: text }];
};

const handleSubmit = async (type: string) => {
  try {
    loading.value = true;
    await vanFormRef.value?.validate();
    const params = {
      id: form.value.id,
      applyStatus: type === "main" ? "1" : "0",
      projectName: form.value.projectName,
      labContent: form.value.labContent,
      classHour: form.value.classHour,
      expectWeekClassHour: form.value.expectWeekClassHour,
      expectDayClassHour: form.value.expectDayClassHour,
      priorityWeek: form.value.priorityWeek,
      prioritySection: form.value.prioritySection,
      expectPeople: form.value.expectPeople,
      labRoomInfoId: form.value.labRoomInfoId,
      labTypeId: form.value.labTypeId,
      collegeCode: form.value.collegeCode,
      collegeName: form.value.collegeName,
      projectProfile: form.value.projectProfile,
      projectDetails: form.value.projectDetails
    };
    await addApplyApi(params);
    showToast(type === "main" ? "提交实验室预约成功" : "存草稿成功");
    history.back();
  } catch (error: any) {
    showDialog({
      title: "提示",
      message: error && error[0] && error[0].message,
      className: "reservation-repeat-dialog"
    });
  } finally {
    loading.value = false;
  }
};

// 选择实验室
const currentLab = ref("");
const showLab = ref(false);
const handleSelectLab = (val: string) => {
  currentLab.value = val[0];
  form.value.labName = (labOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showLab.value = false;
  if (currentLab.value) {
    getRoomListByLabApi(currentLab.value).then((res: any) => {
      roomOptions.value = res.data.map(d => ({ text: d.roomName, value: d.id }));
    });
  }
  form.value.labRoomInfoId = null;
  form.value.roomName = "";
};

// 选择房间
const showRoom = ref(false);
const handleSelectRoom = (val: string) => {
  if (val[0]) {
    form.value.labRoomInfoId = val[0];
  } else if (roomOptions.value.length) {
    form.value.labRoomInfoId = roomOptions.value[0].value;
  }
  form.value.roomName = (roomOptions.value.find((r: any) => r.value === form.value.labRoomInfoId) || { text: "" }).text;
  showRoom.value = false;
};

// 选择实验类型
const showLabType = ref(false);
const handleSelectLabType = (val: string) => {
  form.value.labTypeId = val[0];
  form.value.labTypeName = (labTypeOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showLabType.value = false;
};

// 选择归属学院
const showCollege = ref(false);
const handleSelectCollege = (val: string) => {
  form.value.collegeCode = val[0];
  form.value.collegeName = (collegeOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showCollege.value = false;
};

// 选择优先周次
const priorityWeek = ref<string[]>([]);
const priorityWeekToggle = (val: string) => {
  if (priorityWeek.value.findIndex(p => p === val) > -1) {
    priorityWeek.value = priorityWeek.value.filter(p => p !== val);
  } else {
    priorityWeek.value.push(val);
  }
  form.value.priorityWeek = priorityWeek.value.toString();
};

// 选择优先节次
const prioritySection = ref<string[]>([]);
const prioritySectionToggle = (val: string) => {
  if (prioritySection.value.findIndex(p => p === val) > -1) {
    prioritySection.value = prioritySection.value.filter(p => p !== val);
  } else {
    prioritySection.value.push(val);
  }
  form.value.prioritySection = prioritySection.value.toString();
};

const doDel = () => {
  showDialog({
    title: "删除",
    message: "确定删除?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await labApplyRecordsDelApi({ ids: [query.id] });
    showSuccessToast("删除成功");
    history.back();
  });
};
</script>
<style scoped lang="scss">
.title {
  height: 20px;
  padding-left: 8px;
  line-height: 18px;
  border-left: #1677ff 3px solid;
}
.week-list {
  display: flex;
  flex-wrap: wrap;
  .week-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 10px;
    margin-right: 8px;
    margin-bottom: 8px;
    font-size: 15px;
    line-height: 15px;
    text-align: center;
    cursor: pointer;
    border: #dddddd 1px solid;
    border-radius: 20px;
  }
  .week-item.active {
    color: #ffffff;
    background: #1677ff;
    border: #1677ff 1px solid;
  }
}
.del-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
  font-size: 12px;
  text-align: center;
  .van-icon {
    color: #ee0a24;
  }
}
</style>
