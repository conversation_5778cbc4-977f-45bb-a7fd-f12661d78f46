<template>
  <LsLayout>
    <BaseInfo :info="info" :role="role" :apply-status="info.applyStatus"></BaseInfo>
    <LabSchedule v-if="info.applyStatus !== '0'" :approval-record="approvalRecord" :id="id" :role="role" :apply-status="info.applyStatus"></LabSchedule>
    <Instructor
      v-if="['5', '7', '8', '9'].findIndex(s => s === info.applyStatus) > -1"
      :apply-status="info.applyStatus"
      :info="info"
      :applicant-userid="info.applicantUserid"
      :role="role"
      :id="id"
      @refresh="getDetail"
      @add="addInstructor"
    ></Instructor>
    <Students v-if="['5', '7', '8', '9'].findIndex(s => s === info.applyStatus) > -1 && role === '1'" :apply-status="info.applyStatus" :records="joinApproval" :id="id" @refresh="getDetail"></Students>
    <Students v-if="['5', '7', '8', '9'].findIndex(s => s === info.applyStatus) > -1 && role === '1'" :apply-status="info.applyStatus" :records="joinUser" :id="id" type="1" :info="info" @refresh="getDetail"></Students>
    <template #footer>
      <div v-if="info.applyStatus === '0' && role === '1'" class="w-full flex-shrink-0 flex">
        <div v-if="query.id" @click="doDel" class="w-60 term-btn">
          <div>
            <van-icon name="delete-o" size="22" />
            <div>删除</div>
          </div>
        </div>
        <LsButtonBar main-text="提交" secondary-text="修改" secondary-button-type="default" secondary-plain type="doubleEqual" @button-click="handleEdit" />
      </div>
      <div v-if="['1', '2'].findIndex(s => s === info.applyStatus) > -1 && role === '1'" class="w-full p-12 flex-shrink-0 footer-shadow">
        <van-button class="flex-1" type="warning" plain block @click="doRevocation">撤回</van-button>
      </div>
      <!-- <div v-if="role === '2' && info.applyStatus === '3'" class="w-full p-12 flex-shrink-0 footer-shadow">
        <van-button class="flex-1" type="warning" plain block @click="doRevocation">撤回</van-button>
      </div> -->
      <div v-if="role === '2' && ['1', '2'].findIndex(s => s === info.applyStatus) > -1" class="w-full flex-shrink-0 footer-shadow">
        <LsButtonBar main-text="提交审核" secondary-text="不通过" secondary-button-type="danger" type="doubleEqual" @button-click="handleAdminApproval" />
      </div>
      <div v-if="role === '3' && ['1', '2', '3'].findIndex(s => s === info.applyStatus) > -1" class="w-full flex-shrink-0 footer-shadow">
        <LsButtonBar main-text="通过" secondary-text="不通过" secondary-button-type="danger" type="doubleEqual" @button-click="handleAdminApproval" />
      </div>

      <div v-if="['5', '7'].findIndex(s => s === info.applyStatus) > -1 && (role === '2' || role === '3')" class="w-full p-12 flex-shrink-0 footer-shadow">
        <van-button class="flex-1" plain block type="danger" @click="doEarlyTerm">提前结束</van-button>
      </div>
      <div v-if="info.applyStatus === '5' && role === '1'" class="w-full p-12 flex-shrink-0 footer-shadow">
        <LsButtonBar main-text="学生加入邀请" secondary-text="提前结束" secondary-button-type="danger" main-plain secondary-plain type="doubleEqual" @button-click="handleApplicantAction" />
      </div>
      <div v-if="info.applyStatus === '7' && role === '1'" class="w-full flex-shrink-0 flex">
        <div class="w-80 term-btn">
          <div @click="doEarlyTerm">
            <van-icon name="clock-o" size="22" />
            <div>提前结束</div>
          </div>
        </div>
        <LsButtonBar main-text="签到" secondary-text="学生加入邀请" type="doubleEqual" main-plain @button-click="handleApplicantAction2" />
      </div>
    </template>

    <van-dialog v-model:show="showRefuseDialog" title="不通过" class-name="refuse-reason-dialog" show-cancel-button teleport="body">
      <div class="p-[16px]">
        <div v-if="role === '3'" class="flex" style="margin-bottom: 12px">
          驳回至：
          <van-radio-group v-model="backUserType" direction="horizontal">
            <van-radio :name="1">申请人</van-radio>
            <van-radio v-if="info.applyStatus === '3'" :name="2">实验室管理员</van-radio>
          </van-radio-group>
        </div>
        <van-field rows="2" type="textarea" v-model="refuseReason" placeholder="请输入不通过理由" show-word-limit maxlength="200" class="!bg-[#F8F8FA]" />
      </div>
      <template #footer>
        <div class="flex van-hairline--top van-dialog__footer">
          <van-button size="large" class="van-dialog__cancel" type="default" @click="showRefuseDialog = false">取消</van-button>
          <van-button size="large" class="van-dialog__confirm van-hairline--left" @click="handleRefuse" :loading="refuseLoading">确定</van-button>
        </div>
      </template>
    </van-dialog>
    <van-dialog v-model:show="showQrCodeDialog" title="二维码" class-name="refuse-reason-dialog" show-cancel-button teleport="body">
      <div class="qrcode-container">
        <vue-qrcode v-if="qrCode" :color="{ dark: '#000000ff', light: '#ffffffff' }" :margin="0" :value="qrCode" class="box-border p-8 w-184" type="image/png" />
      </div>
      <template #footer>
        <div class="flex van-hairline--top van-dialog__footer">
          <van-button size="large" class="van-dialog__cancel" type="default" @click="showQrCodeDialog = false">关闭</van-button>
        </div>
      </template>
    </van-dialog>
  </LsLayout>
</template>

<script setup lang="ts" name="LaboratorDetail">
import { LsLayout, LsButtonBar } from "ls-desgin";
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getApplyInfoApi, approvalApplyApi, addApplyApi, revocationApi, labApplyRecordsDelApi, earlyTerminationApi, addLabInstructorApi } from "@/api/modules/laborator";
import BaseInfo from "./components/BaseInfo.vue";
import LabSchedule from "./components/LabSchedule.vue";
import Instructor from "./components/Instructor.vue";
import Students from "./components/Students.vue";
import { showDialog, showSuccessToast } from "vant";
import VueQrcode from "vue-qrcode";
import { getConfigValueApi } from "@/api/modules/system";

const router = useRouter();
const { query } = useRoute();
const id = ref("");
const role = ref("");
const info = ref<any>({ applyStatus: "0", applicantUserid: "" });
const approvalRecord = ref<any[]>([]);
const joinApproval = ref<any[]>([]);
const joinUser = ref<any[]>([]);
const qrCode = ref("");
let corpId = "";
let agentId = "";
const getDetail = () => {
  getApplyInfoApi(id.value).then((res: any) => {
    info.value = res.data?.labInfo || {};
    approvalRecord.value = res.data?.approvalRecord || [];
    joinApproval.value = res.data?.joinApproval || [];
    joinUser.value = res.data?.joinUser || [];
  });
};

const doRevocation = () => {
  showDialog({
    title: "撤回",
    message: "确定撤回?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await revocationApi(id.value);
    showSuccessToast("撤回成功");
    getDetail();
  });
};

const doDel = () => {
  showDialog({
    title: "删除",
    message: "确定删除?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await labApplyRecordsDelApi({ ids: [query.id] });
    showSuccessToast("删除成功");
    history.back();
  });
};

const handleEdit = (type: string) => {
  if (type === "main") {
    showDialog({
      title: "提交",
      message: "确定提交?",
      showCancelButton: true,
      showConfirmButton: true,
      confirmButtonColor: "var(--van-primary-color)"
    }).then(async () => {
      const params = {
        id: info.value.id,
        applyStatus: "1",
        projectName: info.value.projectName,
        labContent: info.value.labContent,
        classHour: info.value.classHour,
        expectWeekClassHour: info.value.expectWeekClassHour,
        expectDayClassHour: info.value.expectDayClassHour,
        priorityWeek: info.value.priorityWeek,
        prioritySection: info.value.prioritySection,
        expectPeople: info.value.expectPeople,
        labRoomInfoId: info.value.labRoomInfoId,
        labTypeId: info.value.labTypeId,
        collegeCode: info.value.collegeCode,
        collegeName: info.value.collegeName,
        projectProfile: info.value.projectProfile,
        projectDetails: info.value.projectDetails
      };
      await addApplyApi(params);
      showSuccessToast("提交成功");
      history.back();
    });
  } else {
    router.push({ name: "LaboratorSubmit", query: { id: query.id } });
  }
};
const showRefuseDialog = ref(false);
const refuseLoading = ref(false);
const refuseReason = ref("");
const backUserType = ref(1);
const handleAdminApproval = (type: string) => {
  if (type === "main") {
    showDialog({
      title: role.value === "2" ? "提交审核" : "通过",
      message: role.value === "2" ? "是否确认提交审核?" : "是否确认通过申请?",
      showCancelButton: true,
      showConfirmButton: true,
      confirmButtonColor: "var(--van-primary-color)"
    }).then(async () => {
      await approvalApplyApi({
        approvalStatus: 1,
        operatorUserType: role.value,
        labApplyId: info.value.id
      });
      showSuccessToast("操作成功");
      history.back();
    });
  } else {
    refuseReason.value = "";
    backUserType.value = 1;
    showRefuseDialog.value = true;
  }
};
const handleRefuse = () => {
  refuseLoading.value = true;
  const p: any = {};
  if (role.value === "3") {
    p.backUserType = backUserType.value;
  }
  approvalApplyApi({
    approvalStatus: 2,
    operatorUserType: parseInt(role.value),
    labApplyId: info.value.id,
    errorMsg: refuseReason.value,
    ...p
  })
    .then(() => {
      showRefuseDialog.value = false;
      showSuccessToast("操作成功");
      history.back();
    })
    .finally(() => {
      refuseLoading.value = false;
    });
};

const doEarlyTerm = () => {
  showDialog({
    title: "提前结束",
    message: "确定提前结束?",
    showCancelButton: true,
    showConfirmButton: true,
    confirmButtonColor: "var(--van-primary-color)"
  }).then(async () => {
    await earlyTerminationApi({ labApplyId: id.value });
    showSuccessToast("提前结束成功");
    getDetail();
  });
};

const showQrCodeDialog = ref(false);
const handleApplicantAction = (type: string) => {
  if (type === "main") {
    openQrCode();
  } else {
    doEarlyTerm();
  }
};

const handleApplicantAction2 = (type: string) => {
  if (type === "main") {
    router.push({ name: "LaboratorInstructor" });
  } else {
    openQrCode();
  }
};

const openQrCode = () => {
  qrCode.value = window.location.href.replace("/detail", "/labInfo") + `&corpId=${corpId}&agentId=${agentId}`;
  showQrCodeDialog.value = true;
};

let mode = "-1";
const addInstructor = labUserType => {
  mode = labUserType;
  localStorage.setItem("addInstructorType", mode);
  router.push({ name: "UserSelect", query: { agentId } });
};

const typeDist = {
  "1": "第一指导教师",
  "2": "第二指导教师",
  "3": "实验协助人员"
};
onMounted(() => {
  getConfigValueApi("corpId").then(res => {
    corpId = res.msg;
  });
  getConfigValueApi("lab.apply.agentId").then(res => {
    agentId = res.msg;
  });
  id.value = query.id as string;
  role.value = query.role as string;
  getDetail();
  const addInstructorType: any = localStorage.getItem("addInstructorType");
  if (["1", "2", "3"].findIndex(m => m === addInstructorType) > -1) {
    const res = JSON.parse(localStorage.getItem("addInstructorSelectInfo") || "");
    if (res.userList?.length) {
      addLabInstructorApi({
        labUserType: addInstructorType,
        labApplyId: id.value,
        userid: res.userList[0].id
      }).then(() => {
        showSuccessToast(`编辑${typeDist[addInstructorType]}成功`);
        getDetail();
      });
    }
    localStorage.setItem("addInstructorSelectInfo", "{}");
    localStorage.setItem("addInstructorType", "-1");
  }
});
</script>
<style scoped lang="scss">
.footer-shadow {
  box-shadow: 2px 2px 8px 1px rgb(0 0 0 / 8%) !important;
}
.term-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
  font-size: 12px;
  text-align: center;
  .van-icon {
    color: #ee0a24;
  }
}
.qrcode-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
</style>
