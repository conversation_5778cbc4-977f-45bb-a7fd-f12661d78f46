<template>
  <LsLayout>
    <template #header>
      <div class="flex flex-col h-[40px] items-center pt-12">{{ schoolCalendar.teachYear }} 第{{ schoolCalendar.term }}学期</div>
      <van-search v-model="searchValue" placeholder="搜索实验项目" class="flex flex-1 items-center">
        <template #right-icon>
          <div class="flex justify-center items-center">
            <van-button @click="loadPage" type="primary" size="mini" class="w-44">搜索</van-button>
          </div>
        </template>
      </van-search>
      <div class="flex flex-col h-full">
        <div class="flex items-center">
          <LsFilterItem :value="currentLabText" position="center" :status="showLab" @click="showLab = true" />
          <LsFilterItem :value="currentRoomText" position="center" :status="showRoom" @click="showRoom = true" />
        </div>
        <van-tabs v-model:active="active" class="mt-1" @change="loadPage">
          <van-tab v-for="(item, index) in statusType" :key="index" :name="item.id" :title="item.name" />
        </van-tabs>
      </div>
    </template>
    <LsList v-if="loaded" ref="lsListRef" :get-list="getList" class="pt-8">
      <template #listItem="{ item }">
        <LabItem :info="item" role="0"></LabItem>
      </template>
    </LsList>
  </LsLayout>

  <OptionsPicker :visible="showLab" title="选择实验室" :value="currentLab" :options="labOptions" @close-picker="showLab = false" @on-confirm="(val: any) => handleSelectLab(val)" />
  <OptionsPicker :visible="showRoom" title="选择房间" :value="currentRoom" :options="roomOptions" @close-picker="showRoom = false" @on-confirm="(val: any) => handleSelectRoom(val)" />
</template>

<script setup lang="ts" name="LaboratorStudent">
import { ref, onMounted, onActivated } from "vue";
import { LsLayout, LsList } from "ls-desgin";
import LsFilterItem from "@/views/clockIn/components/LsFilterItem.vue";
import OptionsPicker from "./components/OptionsPicker.vue";
import LabItem from "./components/LabItem.vue";
import { getStudentPageLabApi, getLabRoomListApi, getRoomListByLabApi, getCurrentSchoolCalendarApi } from "@/api/modules/laborator";
import { useRoute } from "vue-router";

const searchValue = ref("");
const labOptions = ref<any>([]);
const active = ref("1");
const schoolCalendar = ref<any>({});
const { query } = useRoute();
let qrCode: any = {};
const loaded = ref(false);
onMounted(() => {
  currentLab.value = (query.labCode as string) || "-1";
  currentRoom.value = (query.labRoomInfoId as string) || "-1";
  if (currentLab.value !== "-1" && currentRoom.value !== "-1") {
    active.value = "2";
  }
  loaded.value = true;
  getCurrentSchoolCalendarApi().then(res => {
    schoolCalendar.value = res;
  });
  getLabRoomListApi().then((res: any) => {
    labOptions.value = [{ text: "全部实验室", value: "-1" }];
    (res.data || []).forEach(d => {
      if (labOptions.value.findIndex(l => l.value === d.labCode) === -1) {
        labOptions.value.push({ text: d.labName, value: d.labCode });
      }
    });
    qrCode = {
      labCode: query.labCode,
      labRoomInfoId: query.labRoomInfoId
    };
    if (qrCode.labCode && qrCode.labRoomInfoId) {
      active.value = "2";
      currentLab.value = qrCode.labCode;
      currentLabText.value = (labOptions.value.find((r: any) => r.value === currentLab.value) || { text: "" }).text;
      getRoomListByLabApi(currentLab.value).then((res: any) => {
        roomOptions.value = res.data.map(d => ({ text: d.roomName, value: d.id + "" }));
        roomOptions.value.unshift({ text: "全部房间", value: "-1" });
        currentRoom.value = qrCode.labRoomInfoId;
        currentRoomText.value = (roomOptions.value.find((r: any) => r.value === currentRoom.value) || { text: "" }).text;
      });
    }
  });
});

onActivated(() => {
  loadPage();
});

const statusType = ref([
  { id: "1", name: "可报名项目" },
  { id: "2", name: "我的报名" }
]);

const currentLabText = ref("全部实验室");
const currentLab = ref("-1");
const showLab = ref(false);
const handleSelectLab = (val: any) => {
  currentLab.value = val[0];
  currentLabText.value = (labOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showLab.value = false;
  currentRoom.value = "-1";
  currentRoomText.value = "全部房间";
  roomOptions.value = [{ text: "全部房间", value: "-1" }];
  if (currentLab.value !== "-1") {
    getRoomListByLabApi(currentLab.value).then((res: any) => {
      roomOptions.value = [];
      roomOptions.value = res.data.map(d => ({ text: d.roomName, value: d.id + "" }));
      roomOptions.value.unshift({ text: "全部房间", value: "-1" });
    });
  }
  loadPage();
};

const currentRoomText = ref("全部房间");
const currentRoom = ref("-1");
const showRoom = ref(false);
const roomOptions = ref<any>([{ text: "全部房间", value: "-1" }]);
const handleSelectRoom = (val: any) => {
  currentRoom.value = val[0];
  currentRoomText.value = (roomOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showRoom.value = false;
  loadPage();
};

const lsListRef = ref<InstanceType<typeof LsList>>();
const loadPage = () => {
  lsListRef.value?.reset();
};
const page = ref(1);
const getList = async (pageNum: number) => {
  page.value = pageNum;
  const params: any = {};
  if (currentLab.value !== "-1") {
    params.labCode = currentLab.value;
  }
  if (currentRoom.value !== "-1") {
    params.labRoomInfoId = currentRoom.value;
  }
  if (searchValue.value) {
    params.labName = searchValue.value;
  }
  // if (active.value) {
  //   params.applyStatus = active.value;
  // }
  try {
    const { code, data } = await getStudentPageLabApi({
      page: page.value,
      size: 10,
      type: active.value,
      ...params
    });
    if (+code === 0) {
      return Promise.resolve({
        list: data?.records ?? [],
        total: data?.total ?? 0
      });
    } else {
      return Promise.resolve({
        list: [],
        total: 0
      });
    }
  } catch (error) {
    return Promise.resolve({
      list: [],
      total: 0
    });
  }
};
</script>
<style scoped lang="scss">
.my-swipe .van-swipe-item {
  font-size: 20px;
  line-height: 150px;
  color: #ffffff;
  text-align: center;
}
</style>
