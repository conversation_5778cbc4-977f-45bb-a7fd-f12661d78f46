<template>
  <LsLayout v-if="loaded">
    <template #header>
      <div class="flex flex-col h-[40px] items-center pt-12">{{ schoolCalendar.teachYear }} 第{{ schoolCalendar.term }}学期</div>
      <div class="flex flex-col h-full">
        <div class="flex items-center">
          <div class="w-120 flex-shrink-0 role-filter role-text-c">
            <div class="role-text">
              <LsFilterItem :value="currentRoleText" position="center" :status="showRole" @click="showRole = true" />
            </div>
          </div>
          <van-search v-model="searchValue2" placeholder="搜索实验项目" class="flex flex-1 items-center">
            <template #right-icon>
              <div class="flex justify-center items-center">
                <van-button @click="search" type="primary" size="mini" class="w-44">搜索</van-button>
              </div>
            </template>
          </van-search>
        </div>
        <div class="flex items-center">
          <LsFilterItem :value="currentLabText" position="center" :status="showLab" @click="showLab = true" />
          <LsFilterItem :value="currentRoomText" position="center" :status="showRoom" @click="showRoom = true" />
        </div>
        <van-tabs v-model:active="active" class="mt-1" @click-tab="loadPage">
          <van-tab v-for="(item, index) in statusType" :key="index" :name="item.id" :title="item.name" />
        </van-tabs>
      </div>
    </template>
    <LsList v-if="currentRole" ref="lsListRef" :get-list="getList" class="pt-8">
      <template #listItem="{ item }">
        <LabItem :info="item" :role="currentRole"></LabItem>
      </template>
    </LsList>
    <template #footer>
      <div v-if="currentRole === '1'" @click="router.push({ name: 'LaboratorSubmit' })" class="router-link-btn">
        <div class="apply-btn"><van-icon name="plus" size="26" /> 实验室申请</div>
      </div>
    </template>
  </LsLayout>

  <OptionsPicker :visible="showRole" title="选择角色" :value="currentRole" :options="roleOptions" @close-picker="showRole = false" @on-confirm="(val: any) => handleSelectRole(val)" />
  <OptionsPicker :visible="showLab" title="选择实验室" :value="currentLab" :options="labOptions" @close-picker="showLab = false" @on-confirm="(val: any) => handleSelectLab(val)" />
  <OptionsPicker :visible="showRoom" title="选择房间" :value="currentRoom" :options="roomOptions" @close-picker="showRoom = false" @on-confirm="(val: any) => handleSelectRoom(val)" />
</template>

<script setup lang="ts" name="LaboratorIndex">
import { useUserStore } from "@/stores/modules/user";
import { ref, onMounted, onActivated, nextTick } from "vue";
import { LsLayout, LsList } from "ls-desgin";
import LsFilterItem from "@/views/clockIn/components/LsFilterItem.vue";
import OptionsPicker from "./components/OptionsPicker.vue";
import LabItem from "./components/LabItem.vue";
import { getLabRoomListApi, getApplyListApi, getRoleListApi, getRoomListByLabApi, getCurrentSchoolCalendarApi } from "@/api/modules/laborator";
import { useRouter, useRoute } from "vue-router";

const searchValue = ref("");
const searchValue2 = ref("");
const roleOptions = ref<any>([]);
const labOptions = ref<any>([]);
const active = ref("0");
const schoolCalendar = ref<any>({});
const router = useRouter();
const loaded = ref(false);
const { query } = useRoute();
onMounted(() => {
  currentLab.value = (query.labCode as string) || "-1";
  currentRoom.value = (query.labRoomInfoId as string) || "-1";
  setTimeout(() => {
    if (useUserStore().userInfo) {
      checkRole();
    } else {
      setTimeout(() => {
        if (useUserStore().userInfo) {
          checkRole();
        } else {
          setTimeout(() => {
            if (useUserStore().userInfo) {
              checkRole();
            }
          }, 400);
        }
      }, 200);
    }
  }, 100);
});

onActivated(() => {
  loadPage();
});

const search = () => {
  searchValue.value = searchValue2.value;
  loadPage();
};

const checkRole = () => {
  if (useUserStore().userInfo.userType === 1 || useUserStore().userInfo.userType === "1") {
    loaded.value = true;
    getData();
  } else {
    router.push({
      name: "LaboratorStudent",
      query: {
        labCode: query.labCode,
        labRoomInfoId: query.labRoomInfoId,
        corpId: query.corpId,
        agentId: query.agentId
      }
    });
  }
};

const getData = () => {
  getCurrentSchoolCalendarApi().then(res => {
    schoolCalendar.value = res;
  });
  getRoleListApi().then((res: any) => {
    roleOptions.value = (res.data || []).map(d => ({ text: d.label, value: d.value + "" }));
    if (roleOptions.value.length) {
      currentRoleText.value = roleOptions.value[0].text;
      currentRole.value = roleOptions.value[0].value;
    }
  });
  getLabRoomListApi().then((res: any) => {
    labOptions.value = [{ text: "全部实验室", value: "-1" }];
    (res.data || []).forEach(d => {
      if (labOptions.value.findIndex(l => l.value === d.labCode) === -1) {
        labOptions.value.push({ text: d.labName, value: d.labCode });
      }
    });
    if (currentLab.value !== "-1" && currentRoom.value !== "-1") {
      currentLabText.value = (labOptions.value.find((r: any) => r.value === currentLab.value) || { text: "" }).text;
      getRoomListByLabApi(currentLab.value).then((ret: any) => {
        roomOptions.value = [];
        roomOptions.value = ret.data.map(d => ({ text: d.roomName, value: d.id + "" }));
        roomOptions.value.unshift({ text: "全部房间", value: "-1" });
        currentRoomText.value = (roomOptions.value.find((r: any) => r.value === currentRoom.value) || { text: "" }).text;
      });
    }
  });
};

const statusType = ref([
  { id: "0", name: "草稿" },
  { id: "1,2,3", name: "审批中" },
  { id: "5", name: "待开始" },
  { id: "7", name: "进行中" },
  { id: "8,9", name: "已结束" }
]);

const currentRoleText = ref("");
const currentRole = ref("");
const showRole = ref(false);
const handleSelectRole = (val: string) => {
  currentRole.value = val[0];
  currentRoleText.value = (roleOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  if (val[0] === "1") {
    statusType.value = [
      { id: "0", name: "草稿" },
      { id: "1,2,3", name: "审批中" },
      { id: "5", name: "待开始" },
      { id: "7", name: "进行中" },
      { id: "8,9", name: "已结束" }
    ];
    nextTick(() => {
      active.value = "0";
      loadPage();
    });
  } else if (val[0] === "2") {
    statusType.value = [
      { id: "1,2", name: "待排课" },
      { id: "3", name: "待审核" },
      { id: "5,7,8,9", name: "已完成" }
    ];
    nextTick(() => {
      active.value = "1,2";
      loadPage();
    });
  } else if (val[0] === "3") {
    statusType.value = [
      { id: "1,2,3", name: "待审核" },
      { id: "5,7", name: "已完成" },
      { id: "8,9", name: "已结束" }
    ];
    nextTick(() => {
      active.value = "1,2,3";
      loadPage();
    });
  }
  showRole.value = false;
};

const currentLabText = ref("全部实验室");
const currentLab = ref("-1");
const showLab = ref(false);
const handleSelectLab = (val: string) => {
  currentLab.value = val[0];
  currentLabText.value = (labOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showLab.value = false;
  currentRoom.value = "-1";
  currentRoomText.value = "全部房间";
  roomOptions.value = [{ text: "全部房间", value: "-1" }];
  if (currentLab.value !== "-1") {
    getRoomListByLabApi(currentLab.value).then((res: any) => {
      roomOptions.value = [];
      roomOptions.value = res.data.map(d => ({ text: d.roomName, value: d.id + "" }));
      roomOptions.value.unshift({ text: "全部房间", value: "-1" });
    });
  } else {
    roomOptions.value = [{ text: "全部房间", value: "-1" }];
  }
  loadPage();
};

const currentRoomText = ref("全部房间");
const currentRoom = ref("-1");
const showRoom = ref(false);
const roomOptions = ref<any>([{ text: "全部房间", value: "-1" }]);
const handleSelectRoom = (val: string) => {
  currentRoom.value = val[0];
  currentRoomText.value = (roomOptions.value.find((r: any) => r.value === val[0]) || { text: "" }).text;
  showRoom.value = false;
  loadPage();
};

const lsListRef = ref<InstanceType<typeof LsList>>();
const loadPage = () => {
  lsListRef.value?.reset();
};
const page = ref(1);
const getList = async (pageNum: number) => {
  page.value = pageNum;
  const params: any = {};
  if (currentLab.value !== "-1") {
    params.labCode = currentLab.value;
  }
  if (currentRoom.value !== "-1") {
    params.labRoomInfoId = currentRoom.value;
  }
  if (searchValue.value) {
    params.projectName = searchValue.value;
  }
  params.applyStatus = active.value;
  try {
    const { code, data } = await getApplyListApi({
      page: page.value,
      size: 10,
      role: currentRole.value,
      ...params
    });
    if (+code === 0) {
      return Promise.resolve({
        list: data?.records ?? [],
        total: data?.total ?? 0
      });
    } else {
      return Promise.resolve({
        list: [],
        total: 0
      });
    }
  } catch (error) {
    return Promise.resolve({
      list: [],
      total: 0
    });
  }
};
</script>
<style scoped lang="scss">
.my-swipe .van-swipe-item {
  font-size: 20px;
  line-height: 150px;
  color: #ffffff;
  text-align: center;
}
.router-link-btn {
  width: 100%;
  padding: 16px;
  background: #ffffff;
  .apply-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 54px;
    font-size: 18px;
    color: #ffffff;
    cursor: pointer;
    background: #1677ff;
    border-radius: 8px;
  }
}
.role-text-c {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .role-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 110px;
    height: 34px;
    background: #f7f7f7;
    border-radius: 3px;
  }
}
</style>
