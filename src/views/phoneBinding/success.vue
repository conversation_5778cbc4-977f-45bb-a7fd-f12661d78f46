<template>
  <div class="w-full h-full flex flex-col bg-[#FFFFFF]">
    <div class="mx-[15px] ml-[0] flex items-center justify-between">
      <ExitBinding />
      <div @click="router.push({ name: 'PhoneBindingChangePhone' })" class="text-[14px] text-[#3C68E5] mt-[20px]">换绑手机号</div>
    </div>
    <div class="flex-1 flex flex-col items-center">
      <div :class="isRender ? '' : 'invisible'" class="flex flex-col items-center">
        <template v-if="!isExit">
          <img class="w-100 h-100 mt-48" src="@/assets/images/welcomeNew/success.png" />
          <div class="mb-72 text-[18px] text-[#1D2023] font-[600] mt-[5px]">已绑定手机号</div>
        </template>
        <template v-else>
          <img class="w-105 h-105 mt-48" src="@/assets/images/phoneBinging/exit.png" />
          <div class="mb-72 text-[18px] text-[#1D2023] font-[600] mt-[5px]">已退出企业</div>
        </template>
      </div>
      <div class="w-[90%]">
        <div class="flex flex-col items-center">
          <div class="flex items-center justify-start w-full">
            <div class="text-[16px] text-[#707783]">姓<span style="visibility: hidden">空</span>名：</div>
            <div class="text-[16px] text-[#5C6371]">{{ userInfo?.userName }}</div>
          </div>
          <div class="flex items-center justify-start w-full">
            <div class="text-[16px] text-[#707783]">性<span style="visibility: hidden">空</span>别：</div>
            <div class="text-[16px] text-[#5C6371]">{{ ["", "男", "女"][userInfo?.gender] || "未知" }}</div>
          </div>
          <div class="flex items-center justify-start w-full">
            <div class="text-[16px] text-[#707783]">学工号：</div>
            <div class="text-[16px] text-[#5C6371]">{{ userInfo?.userId }}</div>
          </div>
          <div class="flex items-center justify-start w-full">
            <div class="text-[16px] text-[#707783]">手机号：</div>
            <div class="text-[16px] text-[#5C6371]">{{ phone }}</div>
          </div>
        </div>
        <div :class="isRender ? '' : 'invisible'">
          <div v-if="!isExit" class="mt-8 mb-24 text-sm text-[#979DAB]">此处展示的为您绑定系统的手机号，若您在企业微信更换过手机号不会在此回显</div>
          <div v-else class="mt-8 mb-24 text-sm text-[#979DAB]">您已退出企业，若想重新加入企业微信，请联系管理员邀请您加入。</div>
        </div>
      </div>
      <van-button v-if="isRender && !isExit" block class="!w-[345px] mx-auto !mt-[40px]" type="primary" @click="goTo">
        <div class="flex items-center">
          <img alt="" class="w-[22px] h-[19px] mr-[8px]" src="@/assets/images/phoneBinging/qw.png" />
          绑定完成，请下载企业微信
        </div>
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
// import { useUserStore } from "@/stores/modules/user";
import { getUserPhoneApi, getUserInfoApi, getUserByUseridApi } from "@/api/modules/phoneBinding/index";
import ExitBinding from "./components/ExitBinding.vue";
import { showFailToast } from "vant";

import { useCustomSetting } from "@/stores/modules/customSetting";
// import { parseJwt } from "./index";
const router = useRouter();

const customSettings = computed(() => {
  return useCustomSetting().customSettings;
});

const goTo = () => {
  const downloadUrl = customSettings.value.phoneBindingDownloadUrl;
  if (downloadUrl) {
    router.push(downloadUrl);
  } else {
    window.location.href = "https://work.weixin.qq.com/#indexDownload";
  }
};

// const userStore = useUserStore();
// // 解析JWT
// const userInfo = computed(() => parseJwt(userStore.tokenInfo?.token));

const isExit = ref(false);
const isRender = ref(false);
const userInfo = ref<any>({});
async function getUserInfo() {
  const { data } = await getUserInfoApi().catch(res => {
    if (res.code === 90010) {
      router.replace({
        path: "/phoneBinding/filed",
        query: {
          type: "1",
          msg: res.msg
        }
      });
    } else {
      showFailToast(res.msg);
    }
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject(0);
  });
  userInfo.value = data;
  const { data: userData } = await getUserByUseridApi(data.userId);
  if (userData && userData.status && +userData.status === 5) {
    // 退出企业
    isExit.value = true;
  }
  isRender.value = true;
}
getUserInfo();

const phone = ref("");
async function getPhone() {
  const { data } = await getUserPhoneApi();
  phone.value = data?.phoneNo || "";
}
getPhone();
</script>
