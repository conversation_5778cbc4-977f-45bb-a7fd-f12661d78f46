<template>
  <div class="mt-[20px] ml-[15px] text-[14px] text-[#3C68E5] leading-[20px]" @click="onClick">退出登录</div>
</template>
<script setup lang="tsx">
import { showConfirmDialog } from "vant";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/modules/user";

const router = useRouter();
function onClick() {
  showConfirmDialog({
    title: "提示",
    width: "300px",
    message: () => <div>您即将退出登录，确定继续吗?</div>
  })
    .then(async () => {
      if (window.sessionStorage.getItem("preLogin")) {
        const userStore = useUserStore();
        userStore.setToken(null);
        router.replace("/preLogin");
      } else {
        // 确认
        const openUrl = `${window.location.origin}/auth/cas/logout?servicePath=app-base/PhoneBindingBindPhone`;
        window.location.replace(openUrl);
      }
    })
    .catch(() => {
      // 取消
    });
}
</script>
