<template>
  <div>
    <template v-if="dataLoaded">
      <RegisterHome v-if="userType === 1" />
      <AuditorHome v-else />
    </template>
  </div>
</template>

<script setup lang="ts" name="EBikeHome">
import { onMounted, ref } from "vue";
import RegisterHome from "@/views/eBike/register/RegisterHome.vue";
import AuditorHome from "@/views/eBike/auditor/AuditorHome.vue";
import { getConfig, getHomeData } from "@/api/modules/eBike/api";
import { useEBikeStore } from "@/stores/modules/eBike";

const store = useEBikeStore();
const dataLoaded = ref(false);
const userType = ref(1);

onMounted(() => {
  dataLoaded.value = false;
  getHomeData()
    .then(res => {
      store.setInfo(res.data);
      if (+res.code === 0 && res.data.auditFlag === true) {
        userType.value = 2;
      } else {
        userType.value = 1;
      }
      dataLoaded.value = true;
    })
    .catch(error => {
      dataLoaded.value = true;
      console.log(error);
    });
  getConfig().then(res => {
    store.setConfig(res.data);
  });
});
</script>

<style scoped lang="scss"></style>
