<template>
  <div class="flex flex-col h-full overflow-hidden bg-[#F7F8FA]">
    <van-nav-bar title="" v-if="false">
      <template #left>
        <div class="back-button" @click="back">返回</div>
      </template>
    </van-nav-bar>
    <div class="overflow-auto flex-1">
      <div class="flex-col bg-white" style="padding: 20px 0 0">
        <div class="flex flex-row items-center ml-15">
          <div style="width: 2px; height: 12px; margin-right: 6px; background: #1677ff" />
          <div style="font-size: 16px; font-weight: bold; color: #1d2023">基本信息</div>
          <div style="flex: 1"></div>
        </div>
        <van-field v-model="userInfo.name" label="登记人" placeholder="请输入" input-align="right" readonly />
        <van-field v-model="userInfo.role" label="身份" placeholder="请选择" input-align="right" readonly />
        <van-field v-model="userInfo.department" label="所在院系" placeholder="请选择" input-align="right" readonly />
        <van-field v-model="userInfo.mobile" label="联系方式" placeholder="请输入" input-align="right" required />
      </div>
      <div style="height: 8px"></div>
      <BikeInfo v-model:data="bikeInfo" @check-example="checkExample" />
    </div>
    <div style="height: 12px"></div>
    <div class="px-[16px] py-[12px] bg-white">
      <van-button type="primary" block @click="onSubmitClick">提交申请</van-button>
    </div>
    <PopupExample v-model:show="showExample" />
    <PopupCommitment v-model:show="showCommitment" :signature-data="signatureData" @hand-write="showSignature = true" @close="showCommitment = false" @confirm="onCommitmentConfirm" />
    <PopupSignature v-model:show="showSignature" @confirm="onSignatureConfirm" />
  </div>
</template>

<script setup lang="ts">
import BikeInfo from "@/views/eBike/components/BikeInfo.vue";
import PopupExample from "@/views/eBike/components/PopupExample.vue";
import PopupCommitment from "@/views/eBike/components/PopupCommitment.vue";
import PopupSignature from "@/views/eBike/components/PopupSignature.vue";

import router from "@/routers";
import { inject, onMounted, ref } from "vue";
import { uploadBucket } from "@/api/modules/upload";
import { useEBikeStore } from "@/stores/modules/eBike";
import { submitRegister } from "@/api/modules/eBike/api";
import { IEBike } from "@/api/modules/eBike/types";
import { closeToast, showConfirmDialog, showDialog, showFailToast, showLoadingToast, showToast } from "vant";
import { TInjectionKey } from "@/utils/PageStackPlugin";
import { onBeforeRouteLeave } from "vue-router";
import { mobileRegex } from "@/utils/formValidator";

const store = useEBikeStore();

const userInfo = ref<IEBike.IUserInfo>({});

const showExample = ref(false);
const showCommitment = ref(false);
const showSignature = ref(false);

const signatureData = ref("");

const bikeInfo = ref<IEBike.IBikeInfo | null>(null);

const skipBackDialog = ref(false);

onBeforeRouteLeave(async (to, from, next) => {
  if (skipBackDialog.value) {
    // 提交成功后的自动返回不需要弹出确认弹窗
    next();
    return;
  }
  // 用户输入了信息，返回是弹出确认弹窗
  let hasInputData = false;
  if (bikeInfo.value?.licenseNumber || bikeInfo.value?.licenseFront || bikeInfo.value?.licenseBack || bikeInfo.value?.bikeFront || bikeInfo.value?.bikeBack) {
    hasInputData = true;
  }
  if (hasInputData && from.name === "RegisterBike" && to.name === "EBikeHome") {
    try {
      await showConfirmDialog({
        closeOnPopstate: false,
        message: "返回后，将不保存当前已输入的内容?",
        confirmButtonText: "确定"
      });
      next();
    } catch (e) {
      next(false);
    }
  } else {
    next();
  }
});

onMounted(() => {
  const info = new IEBike.IUserInfo();
  info.name = store.info.userName;
  info.department = store.info.departmentName;
  info.mobile = store.info.phone;
  const target = store.config?.userTypeList?.find(item => item.value === store.info.userType);
  info.role = target?.label ?? "";
  userInfo.value = info;
});

function back() {
  router.back();
}

function onSubmitClick() {
  if (!userInfo.value.mobile || userInfo.value.mobile === "") {
    showToast("请输入联系方式");
    return;
  }
  if (!mobileRegex.test(userInfo.value.mobile)) {
    showToast("请输入正确的手机号");
    return;
  }
  if (!bikeInfo.value?.licenseNumber || bikeInfo.value?.licenseNumber === "") {
    showToast("请输入车牌号");
    return;
  }
  if (!bikeInfo.value?.bikeFront || bikeInfo.value?.bikeFront === "") {
    showToast("请上传车身正面照");
    return;
  }
  if (!bikeInfo.value?.bikeBack || bikeInfo.value?.bikeBack === "") {
    showToast("请上传车身侧面照");
    return;
  }
  showCommitment.value = true;
}

const checkExample = () => {
  console.log("checkExample");
  showExample.value = true;
};

const onSignatureConfirm = data => {
  signatureData.value = data;
};

const onCommitmentConfirm = () => {
  submit();
};

const eventBus = inject<TInjectionKey["eventBus"]>("eventBus");

const submit = () => {
  showLoadingToast({
    duration: 0,
    message: "加载中...",
    forbidClick: true
  });
  uploadRequest(signatureData.value)
    .then(res => {
      const params = {
        userId: store.info.userId,
        userType: store.info.userType,
        userName: store.info.userName,
        userDepartment: store.info.departmentName,
        phone: userInfo.value.mobile,
        licensePlate: bikeInfo.value?.licenseNumber,
        drivingLicense: [bikeInfo.value?.licenseFront ?? "", bikeInfo.value?.licenseBack ?? ""],
        vehiclePhotos: [bikeInfo.value?.bikeFront ?? "", bikeInfo.value?.bikeBack ?? ""],
        sign: res
      };
      submitRegister(params)
        .then(res => {
          closeToast();
          if (+res.code === 0) {
            showToast("提交成功");
            eventBus?.emit("updateRegisterData");
            skipBackDialog.value = true;
            back();
          } else {
            if (+res.code === -2) {
              showDialog({
                title: "提示",
                message: res.msg || "提交出错"
              });
            }
            console.error(res);
          }
        })
        .catch(err => {
          closeToast();
          if (+err.code === -2) {
            showDialog({
              title: "提示",
              message: err.msg || "提交出错",
              confirmButtonText: "我知道了"
            });
          } else {
            if (err.msg) {
              showFailToast(err.msg);
            }
          }
          console.error(err);
        });
    })
    .catch(err => {
      console.error(err);
      closeToast();
    });
};

/**
 * 图片上传到服务器
 */
const uploadRequest = async (base64Data: string) => {
  try {
    const params = new FormData();
    params.append("file", base64ToBlob(base64Data, "image/png"));
    const { data } = await uploadBucket(params, "referral", true);
    return Promise.resolve(data.url);
  } catch (error) {
    return Promise.reject(error);
  }
};

function base64ToBlob(base64, mimeType) {
  // 移除base64的data URI部分
  const base64Data = base64.split(",")[1];
  // 解码Base64字符串
  const byteCharacters = atob(base64Data);
  // 转换为8位字节数组
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  // 创建Uint8Array
  const byteArray = new Uint8Array(byteNumbers);
  // 创建Blob对象
  const blob = new Blob([byteArray], { type: mimeType });
  // 返回File对象
  return new File([blob], new Date().getTime() + ".png", { type: mimeType });
}
</script>

<style scoped lang="scss">
.back-button {
  padding: 4px 12px;
  font-size: 12px;
  color: #5c6371;
  background: #ffffff;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
}
.error-container {
  padding: 6px 15px;
  font-size: 12px;
  color: #f03741;
  background: #fff3ed;
}
:deep(.van-cell::after) {
  border-bottom: 1px solid #ffffff;
}
:deep(.van-field) {
  font-size: 14px;
  .van-field__label {
    font-size: 15px;
  }
  .van-field__error-message {
    margin-top: 2px;
    font-size: 15px;
  }
  .van-field__label--required::after {
    margin-left: 2px;
    color: var(--van-field-required-mark-color);
    content: "*";
  }
  .van-field__label--required::before {
    display: none;
  }
}
</style>
