<template>
  <div class="flex flex-col h-[100vh] bg-[#F7F8FA] overflow-hidden">
    <van-tabs v-model:active="activeTab" title-active-color="#1677FF" title-inactive-color="#5C6371" style="box-shadow: 0 1px 0 0 #d6d6d6" @change="refreshList">
      <van-tab :title-style="{ fontSize: '16px' }" :name="0" title="登记" />
      <van-tab :title-style="{ fontSize: '16px' }" :name="1" title="违规查询" />
    </van-tabs>
    <div class="overflow-auto" style="flex: 1">
      <LsList ref="lsListRef" :get-list="getList" :empty-content="activeTab === 0 ? '暂无登记信息，去添加' : '暂无违规信息'">
        <template #listItem="{ item, index }">
          <RegisterItem :item="item" :index="index" v-if="activeTab === 0" @cancel-click="onCancelClick(item)" />
          <MyViolationItem :item="item" v-else @click="violationDetail(item)" />
        </template>
      </LsList>
    </div>
    <div class="px-[16px] py-[12px] bg-white" v-if="activeTab === 0">
      <van-button type="primary" icon="plus" block @click="add">添加</van-button>
    </div>
    <PopupViolationDetail :item="currentViolationItem" v-model:show="showViolationDetail" />
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, onUnmounted, ref } from "vue";
import { LsList } from "ls-desgin";
import RegisterItem from "@/views/eBike/components/RegisterItem.vue";
import MyViolationItem from "@/views/eBike/components/MyViolationItem.vue";
import PopupViolationDetail from "@/views/eBike/components/PopupViolationDetail.vue";
import router from "@/routers";
import { closeToast, showConfirmDialog, showLoadingToast, showToast } from "vant";
import { ResultData } from "@/api/interface";
import { cancelRegister, getRegisterList, getViolationList } from "@/api/modules/eBike/api";
import { useEBikeStore } from "@/stores/modules/eBike";
import { TInjectionKey } from "@/utils/PageStackPlugin";
import { IEBike } from "@/api/modules/eBike/types";

const store = useEBikeStore();

const lsListRef = ref<InstanceType<typeof LsList>>();

const showViolationDetail = ref(false);
const currentViolationItem = ref(null);

/**
 * 切换栏绑定model与change事件
 */
const activeTab = ref<number>(0);

const eventBus = inject<TInjectionKey["eventBus"]>("eventBus");

onMounted(() => {
  eventBus?.on("updateRegisterData", () => {
    refreshList();
  });
});

onUnmounted(() => {
  eventBus?.off("updateRegisterData");
});

const refreshList = () => {
  lsListRef.value?.reset();
};

const getList = async (pageNum: number): Promise<{ list: IEBike.IRegisterItemVo[]; total: number }> => {
  try {
    const params = {
      userId: store.info.userId,
      page: pageNum,
      size: 10
    };
    let res: ResultData;
    if (activeTab.value === 1) {
      res = await getViolationList(params);
    } else {
      res = await getRegisterList(params);
    }
    if (res && +res.code === 0) {
      return Promise.resolve({
        list: res.data?.records ?? [],
        total: res.data?.total ?? 0
      });
    } else {
      return Promise.resolve({
        list: [],
        total: 0
      });
    }
  } catch (e) {
    return Promise.resolve({
      list: [],
      total: 0
    });
  }
};

const add = () => {
  router.push({ name: "RegisterBike" });
};

const violationDetail = item => {
  currentViolationItem.value = item;
  showViolationDetail.value = true;
};

const onCancelClick = item => {
  showConfirmDialog({
    message: "该车牌号将在学校电动车管理系统中注销?",
    confirmButtonText: "注销"
  }).then(() => {
    cancel(item.id);
  });
};

function cancel(itemId: number) {
  showLoadingToast({
    duration: 0,
    message: "加载中...",
    forbidClick: true
  });
  cancelRegister(itemId)
    .then(res => {
      closeToast();
      if (+res.code === 0) {
        showToast("操作成功");
        refreshList();
      } else {
        showToast("操作失败");
      }
    })
    .catch(err => {
      console.error(err);
      closeToast();
    });
}
</script>

<style scoped lang="scss"></style>
