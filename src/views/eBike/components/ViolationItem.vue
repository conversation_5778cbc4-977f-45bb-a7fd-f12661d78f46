<template>
  <div class="pl-16 pr-16 mt-8">
    <div class="container" @click="handleClick">
      <div class="row">
        <div class="title">车牌号：</div>
        <div class="number">{{ data?.licensePlate || "" }}</div>
        <div style="flex: 1"></div>
        <div class="state-yellow" v-if="count === 1">首次违规</div>
        <div class="state-yellow" v-if="count === 2">二次违规</div>
        <div class="state-red" v-if="count >= 3">三次及以上违规</div>
      </div>
      <div class="row" v-for="item in labelList" :key="item.label">
        <div class="label">{{ item.label }}</div>
        <div style="flex: 1">{{ getPropValue(item.prop) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

import { IEBike } from "@/api/modules/eBike/types";
import { useEBikeStore } from "@/stores/modules/eBike";

const store = useEBikeStore();

const props = withDefaults(
  defineProps<{
    data: IEBike.IViolationVo | null;
  }>(),
  {
    data: null
  }
);

const emits = defineEmits(["handleClick"]);

const labelList = ref([
  {
    label: "登记人：\u3000",
    prop: "userName"
  },
  {
    label: "身份：\u3000\u3000",
    prop: "userType"
  },
  {
    label: "违规时间：",
    prop: "violationTime"
  }
]);

function getPropValue(prop: string) {
  if (props.data) {
    const value = props.data[prop];
    if (prop === "userName") {
      const suffix = props.data.userId ?? props.data.phone ?? "";
      if (suffix) {
        return `${value}（${suffix}）`;
      }
      return value;
    }
    if (prop === "userType") {
      const target = store.config?.userTypeList?.find(item => item.value === value);
      return target?.label ?? "";
    }
    return value;
  }
  return "";
}

const count = computed(() => {
  if (props.data) {
    const value = props.data.violationType;
    const target = store.config?.violationTypeList?.find(item => item.id === value);
    if (target?.name === "首次违规") {
      return 1;
    }
    if (target?.name === "二次违规") {
      return 2;
    }
    if (target?.name === "三次及以上违规") {
      return 3;
    }
    return 0;
  }
  return 0;
});

const handleClick = () => {
  emits("handleClick");
};
</script>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 16px;
  background: #ffffff;
  border-radius: 4px;
  .row {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
    color: #0f1d34;
    .title {
      padding: 4px 0;
      font-size: 14px;
      font-weight: bold;
    }
    .number {
      padding: 2px 4px;
      color: #5c6371;
      border: 1px solid #c4c4c4;
      border-radius: 2px;
    }
    .state-yellow {
      padding: 2px 8px;
      color: #ff9d00;
      background: #fff6e4;
      border-radius: 4px;
    }
    .state-red {
      padding: 2px 8px;
      color: #f03741;
      background: #fff3ed;
      border-radius: 4px;
    }
    .label {
      font-size: 14px;
      color: #979dab;
    }
  }
}
</style>
