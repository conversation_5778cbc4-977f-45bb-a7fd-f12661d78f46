<template>
  <div class="flex-col bg-white" style="padding: 20px 0">
    <div class="flex flex-row items-center margin-left">
      <div style="width: 2px; height: 12px; margin-right: 6px; background: #1677ff" />
      <div style="font-size: 16px; font-weight: bold; color: #1d2023">车辆信息</div>
    </div>
    <div class="row items-center margin-left">
      <div>车牌号</div>
      <div class="required">*</div>
      <input class="input" maxlength="10" :readonly="!editable" v-model="licenseNumber" @input="e => handeNumberChange((e.target as any).value)" placeholder="请输入" autoComplete="off" />
    </div>
    <div style="height: 1px; margin-right: 15px; margin-left: 15px; background: #efeff1"></div>
    <div class="row items-center margin-left">
      <div>行驶证</div>
      <div style="flex: 1"></div>
      <div class="check" @click="checkExample">查看示例</div>
    </div>
    <div class="row pl-15 pr-15">
      <div style="flex: 1">
        <van-uploader :readonly="!editable" :deletable="editable" :preview-size="uploaderPreviewSize" :before-read="beforeRead" v-model="licenseFrontList" :after-read="afterRead1" :before-delete="delete1" :max-count="1">
          <template #preview-delete>
            <img class="py-4 px-4" src="@/assets/images/eBike/icon_uploader_delete.svg" alt="delete" />
          </template>
          <img ref="licenseFrontRef" src="@/assets/images/eBike/license_front.png" alt="license_front" />
        </van-uploader>
      </div>
      <div style="width: 12px"></div>
      <div style="flex: 1">
        <van-uploader :readonly="!editable" :deletable="editable" :preview-size="uploaderPreviewSize" :before-read="beforeRead" v-model="licenseBackList" :after-read="afterRead2" :before-delete="delete2" :max-count="1">
          <template #preview-delete>
            <img class="py-4 px-4" src="@/assets/images/eBike/icon_uploader_delete.svg" alt="delete" />
          </template>
          <img src="@/assets/images/eBike/license_back.png" alt="license_back" />
        </van-uploader>
      </div>
    </div>
    <div class="row pl-15 pr-15">
      <div class="image-name">行驶证正面</div>
      <div style="width: 12px"></div>
      <div class="image-name">行驶证背面</div>
    </div>
    <div style="height: 1px; margin-top: 18px; margin-right: 15px; margin-left: 15px; background: #efeff1"></div>
    <div class="row items-center margin-left">
      <div>车辆照片</div>
      <div class="required">*</div>
      <div style="flex: 1"></div>
      <div class="check" @click="checkExample">查看示例</div>
    </div>
    <div style="margin-top: -8px; margin-left: 15px; font-size: 12px; color: #f56c6c">请添加清晰的正面及侧面照片，共2张照片</div>
    <div class="row pl-15 pr-15 mt-12">
      <div style="flex: 1">
        <van-uploader :readonly="!editable" :deletable="editable" :preview-size="uploaderPreviewSize" :before-read="beforeRead" v-model="bikeFrontList" :after-read="afterRead3" :before-delete="delete3" :max-count="1">
          <template #preview-delete>
            <img class="py-4 px-4" src="@/assets/images/eBike/icon_uploader_delete.svg" alt="delete" />
          </template>
          <img src="@/assets/images/eBike/bike_front.png" alt="bike_front" />
        </van-uploader>
      </div>
      <div style="width: 12px"></div>
      <div style="flex: 1">
        <van-uploader :readonly="!editable" :deletable="editable" :preview-size="uploaderPreviewSize" :before-read="beforeRead" v-model="bikeBackList" :after-read="afterRead4" :before-delete="delete4" :max-count="1">
          <template #preview-delete>
            <img class="py-4 px-4" src="@/assets/images/eBike/icon_uploader_delete.svg" alt="delete" />
          </template>
          <img src="@/assets/images/eBike/bike_back.png" alt="bike_back" />
        </van-uploader>
      </div>
    </div>
    <div class="row pl-15 pr-15">
      <div class="image-name">车身正面照</div>
      <div style="width: 12px"></div>
      <div class="image-name">车身侧面照</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { uploadBucket } from "@/api/modules/upload";
import { showToast, UploaderFileListItem } from "vant";
import { IEBike } from "@/api/modules/eBike/types";

const emits = defineEmits(["checkExample", "update:data"]);

const props = withDefaults(
  defineProps<{
    data: IEBike.IBikeInfo | null;
    editable?: boolean; // 能否修改
  }>(),
  {
    editable: true,
    data: null
  }
);

const info = ref<IEBike.IBikeInfo>(new IEBike.IBikeInfo());

const licenseFrontRef = ref<HTMLImageElement | null>(null);
const uploaderPreviewSize = ref<[number, number]>([80, 60]);
const licenseFrontList = ref<UploaderFileListItem[]>([]);
const licenseBackList = ref<UploaderFileListItem[]>([]);
const bikeFrontList = ref<UploaderFileListItem[]>([]);
const bikeBackList = ref<UploaderFileListItem[]>([]);

watch(
  () => info,
  () => {
    emits("update:data", info.value);
  },
  {
    deep: true
  }
);

/**
 * van-uploader的宽高是否设置好了
 *
 * 在van-uploader宽高设置后再去设置van-uploader的回显图片
 *
 * 通过licenseFrontRef加载默认图片后将licenseFrontRef的宽高设置给van-uploader
 * 如果在设置宽高前给van-uploader绑定了回显的图片路径，
 * 可能会导致van-uploader先回显图片，此时从licenseFrontRef获取到的宽高不是预期的宽高
 */
const previewSizeReady = ref(false);

watch(
  () => props.data,
  function () {
    info.value = props.data ?? new IEBike.IBikeInfo();
    if (previewSizeReady.value) {
      setModel();
    }
  },
  {
    deep: true,
    immediate: true
  }
);

function setModel() {
  if (props.data?.licenseFront) {
    licenseFrontList.value = [
      {
        url: props.data?.licenseFront,
        isImage: true
      }
    ];
  }
  if (props.data?.licenseBack) {
    licenseBackList.value = [
      {
        url: props.data?.licenseBack,
        isImage: true
      }
    ];
  }
  if (props.data?.bikeFront) {
    bikeFrontList.value = [
      {
        url: props.data?.bikeFront,
        isImage: true
      }
    ];
  }
  if (props.data?.bikeBack) {
    bikeBackList.value = [
      {
        url: props.data?.bikeBack,
        isImage: true
      }
    ];
  }
  if (props.data?.licenseNumber) {
    licenseNumber.value = props.data?.licenseNumber;
  }
}

const licenseNumber = ref("");

const handeNumberChange = (val: string) => {
  licenseNumber.value = val;
  if (info.value) {
    info.value.licenseNumber = val;
  }
};

const beforeRead = file => {
  if (!file.type.includes("image")) {
    showToast("请上传图片格式文件");
    return false;
  }
  if (file.type.includes("svg")) {
    showToast("不支持svg格式图片");
    return false;
  }
  if (file.type.includes("gif")) {
    showToast("不支持gif格式图片");
    return false;
  }
  return true;
};

const afterRead1 = file => {
  afterRead(file, 1);
};
const afterRead2 = file => {
  afterRead(file, 2);
};
const afterRead3 = file => {
  afterRead(file, 3);
};
const afterRead4 = file => {
  afterRead(file, 4);
};

const afterRead = (file, type) => {
  file.status = "uploading";
  file.message = "上传中...";
  uploadRequest(file.file)
    .then(res => {
      file.url = res;
      file.status = "success";
      file.message = "上传成功";
      console.log("type = ", type);
      if (info.value) {
        if (type === 1) {
          info.value.licenseFront = file.url;
        }
        if (type === 2) {
          info.value.licenseBack = file.url;
        }
        if (type === 3) {
          info.value.bikeFront = file.url;
        }
        if (type === 4) {
          info.value.bikeBack = file.url;
        }
      }
      console.log("info = ", info.value);
    })
    .catch(err => {
      file.status = "failed";
      file.message = "上传失败";
      console.log(err);
    });
};

/**
 * 图片上传到服务器
 * @param {File} file
 */
const uploadRequest = async (file: File) => {
  try {
    const params = new FormData();
    params.append("file", file);
    const { data } = await uploadBucket(params, "referral", true);
    return Promise.resolve(data.url);
  } catch (error) {
    return Promise.reject(error);
  }
};

const delete1 = () => {
  if (info.value) {
    info.value.licenseFront = "";
  }
  return true;
};
const delete2 = () => {
  if (info.value) {
    info.value.licenseBack = "";
  }
  return true;
};
const delete3 = () => {
  if (info.value) {
    info.value.bikeFront = "";
  }
  return true;
};
const delete4 = () => {
  if (info.value) {
    info.value.bikeBack = "";
  }
  return true;
};

onMounted(() => {
  info.value = props.data ?? new IEBike.IBikeInfo();
  const img = licenseFrontRef.value;
  if (img?.complete) {
    uploaderPreviewSize.value = [img?.width ?? 80, img?.height ?? 60];
    previewSizeReady.value = true;
    setModel();
  } else {
    if (img) {
      img.onload = () => {
        uploaderPreviewSize.value = [img?.width ?? 80, img?.height ?? 60];
        previewSizeReady.value = true;
        setModel();
      };
    }
  }
});

const checkExample = () => {
  emits("checkExample");
};
</script>

<style scoped lang="scss">
:deep(.van-uploader__preview) {
  margin: 0;
}
:deep(.van-image__img) {
  border-radius: 4px;
}
.margin-left {
  margin-left: 15px;
}
.row {
  display: flex;
  flex-direction: row;
  font-size: 15px;
  color: #1d2023;
  .input {
    flex: 1;
    justify-content: flex-end;
    width: 100%;
    padding: 15px;
    text-align: right;
  }
  .required {
    color: red;
  }
  .check {
    padding: 14px 18px;
    font-size: 12px;
    color: #1677ff;
  }
  .image-name {
    flex: 1;
    font-size: 12px;
    color: #5c6371;
    text-align: center;
  }
}
</style>
