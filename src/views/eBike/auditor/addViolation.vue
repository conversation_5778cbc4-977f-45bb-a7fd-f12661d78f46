<template>
  <div class="flex flex-col h-full overflow-hidden bg-[#F7F8FA]">
    <van-nav-bar title="" v-if="false">
      <template #left>
        <div class="back-button" @click="back">返回</div>
      </template>
    </van-nav-bar>
    <div class="overflow-auto flex-1">
      <UserInfo :data="userInfo" />
      <div style="height: 8px"></div>
      <div class="flex-col bg-white" style="padding: 19px 15px">
        <div class="flex flex-row items-center">
          <div style="width: 2px; height: 12px; margin-right: 6px; background: #1677ff" />
          <div style="font-size: 16px; font-weight: bold; color: #1d2023">车辆信息</div>
        </div>
        <div class="flex flex-row items-center text-[14px] text-[#1D2023] mt-16">
          <div class="text-[#5C6371]">车牌号：</div>
          <div class="ml-14">{{ userInfo?.licenseNumber ?? "-" }}</div>
        </div>
      </div>
      <div style="height: 8px"></div>
      <div class="flex-col bg-white" style="padding: 20px 0 0">
        <div class="flex flex-row items-center ml-15">
          <div style="width: 2px; height: 12px; margin-right: 6px; background: #1677ff" />
          <div style="font-size: 16px; font-weight: bold; color: #1d2023">违规信息添加</div>
        </div>
        <van-field v-model="_form.violationType" label="违规类型" placeholder="请选择" required input-align="right" size="large" readonly />
        <van-field v-model="_form.violationDesc" label="违规说明" placeholder="请输入" required type="textarea" label-align="top" autosize size="large" maxlength="200" show-word-limit />
        <van-field v-model="_form.violationTime" label="违规时间" placeholder="请选择" required input-align="right" size="large" is-link readonly @click="showDatePicker = true" />
        <van-popup
          v-model:show="showDatePicker"
          :style="{
            '--van-picker-title-font-size': '18px',
            '--van-picker-action-font-size': '16px',
            '--van-picker-option-font-size': '22px'
          }"
          position="bottom"
          safe-area-inset-bottom
          teleport="body"
        >
          <van-picker-group title="违规时间" :tabs="['选择日期', '选择时间']" next-step-text="下一步" @confirm="onConfirm" @cancel="showDatePicker = false">
            <van-date-picker v-model="currentDate" :max-date="new Date()" />
            <van-time-picker v-model="currentTime" />
          </van-picker-group>
        </van-popup>
        <van-field v-model="_form.violationLocation" label="违规地点" placeholder="请输入" required type="textarea" label-align="top" autosize size="large" maxlength="200" show-word-limit />
        <van-field input-align="left" label="违规照片" label-align="top" model-value="支持2张" size="large">
          <template #label>
            <div class="w-full flex items-center">
              <span>违规照片</span>
              <div class="required">*</div>
              <div style="flex: 1"></div>
              <span style="font-size: 15px; font-weight: 400; color: #979dab">支持2张</span>
            </div>
          </template>
          <template #input>
            <van-uploader class="mt-15" :before-read="beforeRead" v-model="_form.fileList" :after-read="afterRead" :max-count="2" />
          </template>
        </van-field>
      </div>
    </div>
    <div style="height: 12px"></div>
    <div class="px-[16px] py-[12px] bg-white">
      <van-button type="primary" block @click="onSubmitClick">加入违规</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import UserInfo from "@/views/eBike/components/UserInfo.vue";
import { uploadBucket } from "@/api/modules/upload";

import router from "@/routers";
import { inject, onMounted, reactive, ref } from "vue";
import { closeToast, showConfirmDialog, showLoadingToast, showToast } from "vant";
import dayjs from "dayjs";
import { useEBikeStore } from "@/stores/modules/eBike";
import { IEBike } from "@/api/modules/eBike/types";
import { addViolation } from "@/api/modules/eBike/api";
import { onBeforeRouteLeave, useRoute } from "vue-router";
import { TInjectionKey } from "@/utils/PageStackPlugin";

const store = useEBikeStore();
const route = useRoute();

const userInfo = ref<IEBike.IUserInfo | null>(null);
const currentDate = ref<string[]>([]);
currentDate.value = dayjs().format("YYYY-MM-DD").split("-");
const currentTime = ref(["12", "00"]);
currentTime.value = dayjs().format("HH:mm").split(":");

const showDatePicker = ref(false);

const _form = reactive({
  violationType: "",
  violationDesc: "",
  fileList: [] as Array<any>,
  violationTime: "",
  violationLocation: "",
  loading: false
});

const currentViolationType = ref<IEBike.IViolationTypeVO>({});

let typeList: IEBike.IViolationTypeVO[] = [];

if (store.config.violationTypeList) {
  typeList = store.config.violationTypeList;
}

onMounted(() => {
  const jsonString = route.query.userInfo as string;
  if (jsonString) {
    userInfo.value = JSON.parse(jsonString);
  }

  const violationCount = route.query.violationCount as string;
  if (violationCount) {
    const count = Math.min(Number(violationCount), 2);
    const target = typeList.find(item => count + 1 === (item.sort ?? 1));
    if (target) {
      _form.violationType = target.name ?? "";
      _form.violationDesc = target.violationExplain ?? "";
      currentViolationType.value = target;
    }
  }
});

const onConfirm = () => {
  _form.violationTime = currentDate.value.join("-") + " " + currentTime.value.join(":");
  showDatePicker.value = false;
};

function back() {
  router.back();
}

const skipBackDialog = ref(false);

onBeforeRouteLeave(async (to, from, next) => {
  if (skipBackDialog.value) {
    // 提交成功后的自动返回不需要弹出确认弹窗
    next();
    return;
  }
  // 用户输入了信息，返回是弹出确认弹窗
  let hasInputData = false;
  if (_form.violationDesc !== currentViolationType.value.violationExplain) {
    hasInputData = true;
  }
  if (_form.violationTime || _form.violationLocation || _form.fileList.length > 0) {
    hasInputData = true;
  }
  if (hasInputData && from.name === "AddViolation" && to.name === "EBikeHome") {
    try {
      await showConfirmDialog({
        closeOnPopstate: false,
        message: "返回后，将不保存当前已输入的内容?",
        confirmButtonText: "确定"
      });
      next();
    } catch (e) {
      next(false);
    }
  } else {
    next();
  }
});

function onSubmitClick() {
  if (!_form.violationType) {
    showToast("请选择违规类型");
    return;
  }
  if (!_form.violationDesc) {
    showToast("请输入违规说明");
    return;
  }
  if (!_form.violationTime) {
    showToast("请选择违规时间");
    return;
  }
  if (!_form.violationLocation) {
    showToast("请输入违规地点");
    return;
  }
  const imageUrl: any[] = [];
  let isUploading = false;
  _form.fileList.forEach(file => {
    if (file.status === "uploading") {
      isUploading = true;
      return;
    }
    if (file.url) {
      imageUrl.push(file.url);
    }
  });
  if (isUploading) {
    showToast("请等待上传完成");
    return;
  }
  if (!imageUrl || imageUrl.length === 0) {
    showToast("请至少上传一张违规照片");
    return;
  }
  showConfirmDialog({
    message: "确定添加违规?",
    confirmButtonText: "确定"
  }).then(() => {
    submit();
  });
}

const eventBus = inject<TInjectionKey["eventBus"]>("eventBus");

const submit = () => {
  console.log("submit");
  showLoadingToast({
    duration: 0,
    message: "加载中...",
    forbidClick: true
  });
  const params = {
    recordId: route.query?.itemId,
    violationType: currentViolationType.value?.id ?? 0,
    violationExplain: _form.violationDesc,
    violationLocation: _form.violationLocation,
    violationPhotos: _form.fileList.map(item => item.url),
    violationTime: _form.violationTime + ":00",
    auditId: store.info.userId,
    auditName: store.info.userName
  };
  addViolation(params)
    .then(res => {
      closeToast();
      if (+res.code === 0) {
        showToast("添加成功");
        eventBus?.emit("updateAuditorData");
        skipBackDialog.value = true;
        back();
      } else {
        showToast("添加失败");
      }
    })
    .catch(err => {
      console.error(err);
      closeToast();
    });
};

/**
 * 上传图片校验
 */
const beforeRead = file => {
  if (!file.type.includes("image")) {
    showToast("请上传图片格式文件");
    return false;
  }
  if (file.type.includes("svg")) {
    showToast("不支持svg格式图片");
    return false;
  }
  if (file.type.includes("gif")) {
    showToast("不支持gif格式图片");
    return false;
  }
  return true;
};

const afterRead = file => {
  console.log(file);
  file.status = "uploading";
  file.message = "上传中...";
  uploadRequest(file.file)
    .then(res => {
      file.url = res;
      file.status = "success";
      file.message = "上传成功";
    })
    .catch(err => {
      file.status = "failed";
      file.message = "上传失败";
      console.log(err);
    });
};

/**
 * 图片上传到服务器
 * @param {File} file
 */
const uploadRequest = async (file: File) => {
  console.log("file = ", file);
  try {
    const params = new FormData();
    params.append("file", file);
    const { data } = await uploadBucket(params, "referral", true);
    return Promise.resolve(data.url);
  } catch (error) {
    return Promise.reject(error);
  }
};
</script>

<style scoped lang="scss">
.back-button {
  padding: 4px 12px;
  font-size: 12px;
  color: #5c6371;
  background: #ffffff;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
}
.error-container {
  padding: 6px 15px;
  font-size: 12px;
  color: #f03741;
  background: #fff3ed;
}
:deep(.van-field) {
  font-size: 14px;
  .van-field__label {
    font-size: 15px;
  }
  .van-field__error-message {
    margin-top: 2px;
    font-size: 15px;
  }
  .van-field__label--required::after {
    margin-left: 2px;
    color: var(--van-field-required-mark-color);
    content: "*";
  }
  .van-field__label--required::before {
    display: none;
  }
}
.required {
  margin-left: 2px;
  color: var(--van-field-required-mark-color);
}
</style>
