<template>
  <div class="flex flex-col h-full overflow-hidden bg-[#F7F8FA]">
    <van-nav-bar title="" v-if="false">
      <template #left>
        <div class="back-button" @click="back">返回</div>
      </template>
    </van-nav-bar>
    <div class="overflow-auto flex-1">
      <div class="flex-col bg-white" style="padding: 20px 0 0">
        <div class="flex flex-row items-center ml-15">
          <div style="width: 2px; height: 12px; margin-right: 6px; background: #1677ff" />
          <div style="font-size: 16px; font-weight: bold; color: #1d2023">基本信息</div>
          <div style="flex: 1"></div>
        </div>
        <van-field v-model="name" label="登记人" placeholder="请输入" required input-align="right" size="large" />
        <van-field v-model="role" label="身份" placeholder="请选择" required input-align="right" size="large" is-link readonly @click="withRole.show = true" />
        <van-field v-model="mobile" label="联系方式" placeholder="请输入" required input-align="right" size="large" />
      </div>
      <div style="height: 8px"></div>
      <BikeInfo v-model:data="data" @check-example="checkExample" />
    </div>
    <div style="height: 12px"></div>
    <div class="px-[16px] py-[12px] bg-white">
      <van-button type="primary" block @click="onSubmitClick">添加</van-button>
    </div>
    <PopupExample v-model:show="showExample" />
    <van-popup
      v-model:show="withRole.show"
      :style="{
        '--van-picker-title-font-size': '18px',
        '--van-picker-action-font-size': '16px',
        '--van-picker-option-font-size': '22px'
      }"
      position="bottom"
      safe-area-inset-bottom
      teleport="body"
    >
      <van-picker :columns="withRole.columns" title="选择登记人身份" @cancel="withRole.show = false" @confirm="handleRoleConfirm" />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import BikeInfo from "@/views/eBike/components/BikeInfo.vue";
import PopupExample from "@/views/eBike/components/PopupExample.vue";

import router from "@/routers";
import { inject, reactive, ref } from "vue";
import { closeToast, PickerColumn, PickerConfirmEventParams, showConfirmDialog, showDialog, showFailToast, showLoadingToast, showToast } from "vant";
import { useEBikeStore } from "@/stores/modules/eBike";
import { IEBike } from "@/api/modules/eBike/types";
import { mobileRegex } from "@/utils/formValidator";
import { auditorSubmitRegister } from "@/api/modules/eBike/api";
import { TInjectionKey } from "@/utils/PageStackPlugin";
import { onBeforeRouteLeave } from "vue-router";

const store = useEBikeStore();

const name = ref("");
const roleType = ref(0);
const role = ref("");
const mobile = ref("");
const showExample = ref(false);

const data = ref<IEBike.IBikeInfo | null>(null);

let roleList: IEBike.ITypeVO[] = [];
if (store.config.userTypeList) {
  roleList = store.config.userTypeList.filter(item => item.value > 2);
}

/**
 * 基本信息身份相关
 * show:弹窗开启状态
 * columns:选择数组
 */
const withRole = reactive<{
  show: boolean;
  columns: PickerColumn;
}>({
  show: false,
  columns: (() => {
    const columns: PickerColumn = [];
    for (const role of roleList) {
      columns.push({
        text: role.label,
        value: role.value
      });
    }
    return columns;
  })()
});
/**
 * 身份类型确认事件
 */
const handleRoleConfirm = ({ selectedOptions }: PickerConfirmEventParams) => {
  const selectOption = selectedOptions[0];
  if (!selectOption) {
    withRole.show = false;
    throw new Error(`身份类型选择错误，选择项行数据:${selectedOptions}`);
  }
  roleType.value = Number(selectOption?.value);
  role.value = selectOption.text as string;
  withRole.show = false;
};

function back() {
  router.back();
}

const skipBackDialog = ref(false);

onBeforeRouteLeave(async (to, from, next) => {
  if (skipBackDialog.value) {
    // 提交成功后的自动返回不需要弹出确认弹窗
    next();
    return;
  }
  // 用户输入了信息，返回是弹出确认弹窗
  let hasInputData = false;
  if (name.value || role.value || mobile.value) {
    hasInputData = true;
  }
  if (data.value?.licenseNumber || data.value?.licenseFront || data.value?.licenseBack || data.value?.bikeFront || data.value?.bikeBack) {
    hasInputData = true;
  }
  if (hasInputData && from.name === "AuditorAddRegister" && to.name === "EBikeHome") {
    try {
      await showConfirmDialog({
        closeOnPopstate: false,
        message: "返回后，将不保存当前已输入的内容?",
        confirmButtonText: "确定"
      });
      next();
    } catch (e) {
      next(false);
    }
  } else {
    next();
  }
});

function onSubmitClick() {
  if (!name.value || name.value === "") {
    showToast("请输入登记人");
    return;
  }
  if (!role.value || role.value === "") {
    showToast("请选择身份");
    return;
  }
  if (!mobile.value || mobile.value === "") {
    showToast("请输入联系方式");
    return;
  }
  if (!mobileRegex.test(mobile.value)) {
    showToast("请输入正确的手机号");
    return;
  }
  if (!data.value?.licenseNumber || data.value?.licenseNumber === "") {
    showToast("请输入车牌号");
    return;
  }
  if (!data.value?.bikeFront || data.value?.bikeFront === "") {
    showToast("请上传车身正面照");
    return;
  }
  if (!data.value?.bikeBack || data.value?.bikeBack === "") {
    showToast("请上传车身侧面照");
    return;
  }
  showConfirmDialog({
    message: "确定添加登记?",
    confirmButtonText: "确定"
  }).then(() => {
    submit();
  });
}

const checkExample = () => {
  showExample.value = true;
};

const eventBus = inject<TInjectionKey["eventBus"]>("eventBus");

const submit = () => {
  showLoadingToast({
    duration: 0,
    message: "加载中...",
    forbidClick: true
  });
  const params = {
    userName: name.value,
    userType: roleType.value,
    phone: mobile.value,
    licensePlate: data.value?.licenseNumber,
    drivingLicense: [data.value?.licenseFront ?? "", data.value?.licenseBack ?? ""],
    vehiclePhotos: [data.value?.bikeFront ?? "", data.value?.bikeBack ?? ""],
    sign: "222",
    auditId: store.info.userId,
    auditName: store.info.userName
  };
  auditorSubmitRegister(params)
    .then(res => {
      closeToast();
      if (+res.code === 0) {
        showToast("提交成功");
        eventBus?.emit("updateAuditorData");
        skipBackDialog.value = true;
        back();
      } else {
        console.error(res);
      }
    })
    .catch(err => {
      closeToast();
      if (+err.code === -2) {
        showDialog({
          title: "提示",
          message: err.msg || "提交出错",
          confirmButtonText: "我知道了"
        });
      } else {
        if (err.msg) {
          showFailToast(err.msg);
        }
      }
      console.error(err);
    });
};
</script>

<style scoped lang="scss">
.back-button {
  padding: 4px 12px;
  font-size: 12px;
  color: #5c6371;
  background: #ffffff;
  border: 1px solid #d8d8d8;
  border-radius: 4px;
}
.error-container {
  padding: 6px 15px;
  font-size: 12px;
  color: #f03741;
  background: #fff3ed;
}
:deep(.van-field) {
  font-size: 14px;
  .van-field__label {
    font-size: 15px;
  }
  .van-field__error-message {
    margin-top: 2px;
    font-size: 15px;
  }
  .van-field__label--required::after {
    margin-left: 2px;
    color: var(--van-field-required-mark-color);
    content: "*";
  }
  .van-field__label--required::before {
    display: none;
  }
}
</style>
