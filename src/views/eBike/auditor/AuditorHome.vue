<template>
  <div class="flex flex-col h-[100vh] bg-[#F7F8FA] overflow-hidden">
    <van-tabs v-model:active="activeTab" title-active-color="#1677FF" title-inactive-color="#5C6371" style="box-shadow: 0 1px 0 0 #d6d6d6" @change="refreshList">
      <van-tab :title-style="{ fontSize: '16px' }" :name="0" title="待审批" />
      <van-tab :title-style="{ fontSize: '16px' }" :name="1" title="全部查询" />
      <van-tab :title-style="{ fontSize: '16px' }" :name="2" title="违规查询" />
    </van-tabs>
    <div class="flex flex-row items-center bg-white">
      <div class="flex flex-row items-center" style="gap: 1px; padding: 19px 12px 19px 15px" @click="withRole.show = true">
        <div>{{ role }}</div>
        <img src="@/assets/images/eBike/icon_arrow_down.svg" alt="" />
      </div>
      <input class="input" v-model="licenseNumber" placeholder="输入车牌号" />
      <div style="width: 8px"></div>
      <input class="input" v-model="username" placeholder="输入姓名" />
      <div style="padding: 17px 12px" @click="refreshList">
        <div class="search-button">搜索</div>
      </div>
    </div>
    <div class="overflow-auto" style="flex: 1">
      <LsList ref="lsListRef" :get-list="getList">
        <template #listItem="{ item }">
          <AuditItem v-if="activeTab === 0" :type="activeTab" :data="item" @click="check(item)" @approve-click="approve(item)" @reject-click="reject(item)" />
          <AuditItem v-if="activeTab === 1" :type="activeTab" :data="item" @add-violation-click="addViolation(item)" />
          <ViolationItem :data="item" v-if="activeTab === 2" @click="violationDetail(item)" />
        </template>
      </LsList>
    </div>
    <div class="px-[16px] py-[12px] bg-white" v-if="activeTab === 1">
      <van-button icon="plus" type="primary" block @click="add">添加</van-button>
    </div>
    <van-popup
      v-model:show="withRole.show"
      :style="{
        '--van-picker-title-font-size': '18px',
        '--van-picker-action-font-size': '16px',
        '--van-picker-option-font-size': '22px'
      }"
      position="bottom"
      safe-area-inset-bottom
      teleport="body"
    >
      <van-picker :columns="withRole.columns" title="选择登记人身份" @cancel="withRole.show = false" @confirm="handleRoleConfirm" />
    </van-popup>
    <PopupViolationDetail :item="currentViolationItem" v-model:show="showViolationDetail" />
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, onUnmounted, reactive, ref } from "vue";
import { LsList } from "ls-desgin";
import AuditItem from "@/views/eBike/components/AuditItem.vue";
import ViolationItem from "@/views/eBike/components/ViolationItem.vue";
import router from "@/routers";
import { closeToast, PickerColumn, PickerConfirmEventParams, showConfirmDialog, showLoadingToast, showToast } from "vant";
import { PickerOption } from "vant/lib/picker/types";
import { auditRegister, getRegisterList, getViolationList } from "@/api/modules/eBike/api";
import { ResultData } from "@/api/interface";
import { IEBike } from "@/api/modules/eBike/types";
import { useEBikeStore } from "@/stores/modules/eBike";
import { TInjectionKey } from "@/utils/PageStackPlugin";
import PopupViolationDetail from "@/views/eBike/components/PopupViolationDetail.vue";

const store = useEBikeStore();

const lsListRef = ref<InstanceType<typeof LsList>>();

const currentRole = ref<PickerOption | null>(null);

const licenseNumber = ref("");
const username = ref("");

const showViolationDetail = ref(false);
const currentViolationItem = ref(null);

const violationDetail = item => {
  currentViolationItem.value = item;
  showViolationDetail.value = true;
};

const role = computed(() => {
  return currentRole?.value?.text ?? "选择身份";
});

let roleList: IEBike.ITypeVO[] = [];
if (store.config.userTypeList) {
  roleList = store.config.userTypeList;
  const all: IEBike.ITypeVO = {
    label: "全部",
    value: -1
  };
  roleList.unshift(all);
}

/**
 * 基本信息身份相关
 * show:弹窗开启状态
 * columns:选择数组
 */
const withRole = reactive<{
  show: boolean;
  columns: PickerColumn;
}>({
  show: false,
  columns: (() => {
    const columns: PickerColumn = [];
    for (const role of roleList) {
      columns.push({
        text: role.label,
        value: role.value
      });
    }
    return columns;
  })()
});
/**
 * 身份类型确认事件
 */
const handleRoleConfirm = ({ selectedOptions }: PickerConfirmEventParams) => {
  const selectOption = selectedOptions[0];
  console.log("selectOption ", selectOption);
  if (!selectOption) {
    withRole.show = false;
    throw new Error(`身份类型选择错误，选择项行数据:${selectedOptions}`);
  }
  currentRole.value = selectOption;
  withRole.show = false;
  refreshList();
};

/**
 * 切换栏绑定model与change事件
 */
const activeTab = ref<number>(0);

const eventBus = inject<TInjectionKey["eventBus"]>("eventBus");

onMounted(() => {
  eventBus?.on("updateAuditorData", () => {
    refreshList();
  });
});

onUnmounted(() => {
  eventBus?.off("updateAuditorData");
});

const refreshList = () => {
  lsListRef.value?.reset();
};

const getList = async (pageNum: number): Promise<{ list: any[]; total: number }> => {
  try {
    const params = {
      page: pageNum,
      size: 10
    };
    if (activeTab.value === 0) {
      params.auditStatus = 0;
    }
    if (activeTab.value === 1) {
      params.auditStatus = 2;
    }
    if (currentRole.value && currentRole.value?.text !== "全部") {
      params.userType = currentRole.value.value;
    }
    if (licenseNumber.value) {
      params.licensePlate = licenseNumber.value;
    }
    if (username.value) {
      params.userName = username.value;
    }
    let res: ResultData;
    if (activeTab.value === 2) {
      res = await getViolationList(params);
    } else {
      res = await getRegisterList(params);
    }
    if (res && +res.code === 0) {
      return Promise.resolve({
        list: res.data?.records ?? [],
        total: res.data?.total ?? 0
      });
    } else {
      return Promise.resolve({
        list: [],
        total: 0
      });
    }
  } catch (e) {
    return Promise.resolve({
      list: [],
      total: 0
    });
  }
};

const add = () => {
  router.push({ name: "AuditorAddRegister" });
};

const check = item => {
  router.push({ name: "AuditorDetail", query: { item: JSON.stringify(item) } });
};

const reject = item => {
  showConfirmDialog({
    message: "确定驳回该申请?",
    confirmButtonText: "确定"
  }).then(() => {
    changeStatus(1, item.id);
  });
};

const approve = item => {
  showConfirmDialog({
    message: "确定通过该申请?",
    confirmButtonText: "通过"
  }).then(() => {
    changeStatus(2, item.id);
  });
};

function changeStatus(status: number, itemId: string) {
  showLoadingToast({
    duration: 0,
    message: "加载中...",
    forbidClick: true
  });
  auditRegister({
    id: itemId,
    auditStatus: status,
    auditId: store.info.userId,
    auditName: store.info.userName
  })
    .then(res => {
      closeToast();
      if (+res.code === 0) {
        showToast("操作成功");
        refreshList();
      } else {
        showToast("操作失败");
      }
    })
    .catch(err => {
      console.error(err);
      closeToast();
    });
}

const addViolation = item => {
  const userInfo = new IEBike.IUserInfo();
  userInfo.licenseNumber = item.licensePlate;
  userInfo.name = item.userName;
  userInfo.userId = item.userId;
  userInfo.department = item.userDepartment;
  userInfo.mobile = item.phone;
  const target = store.config?.userTypeList?.find(type => type.value === item.userType);
  userInfo.role = target?.label ?? "";
  router.push({ name: "AddViolation", query: { itemId: item.id, violationCount: item.violationCount, userInfo: JSON.stringify(userInfo) } });
};
</script>

<style scoped lang="scss">
.input {
  flex: 1;
  min-width: 10px;
  padding: 5px 12px;
  background: #f2f2f2;
  border-radius: 6px;
}
.search-button {
  padding: 4px 10px;
  font-size: 12px;
  color: #ffffff;
  background: #1677ff;
  border-radius: 5px;
}
</style>
