<template>
  <div class="sign-page" v-if="!isSign">
    <div class="Background-image">
      <img src="@/assets/images/studentTask/bg_img.png" alt="" />
    </div>
    <div class="bottom-content">
      <div class="w-full overflow-hidden flex flex-col h-full">
        <div class="flex-1 flex justify-center items-center py-[16px] px-[15px] rounded-[8px] mt-[12px] overflow-hidden">
          <Range class="mb-[30px]" :is-clock="isClock" @click="handleSign">
            <div class="flex flex-col justify-center items-center">
              <img src="@/assets/images/meeting/saoma.png" alt="" style="width: 42px; height: 42px" />
              <div class="text-[16px] font-[400] mt-[10px]">扫码签到</div>
            </div>
          </Range>
        </div>
      </div>
      <!-- 签到范围/结果 -->
      <div class="sign-range">
        <div class="sign-notice">
          <van-icon :name="isClock ? 'passed' : 'warning'" :color="isClock ? '#07C160' : '#faad14'" size="18" />
          <div style="margin-left: 5px; font-size: 14px" v-if="!isClock">您当前不在签到范围</div>
          <span style="margin-left: 5px; font-size: 14px" v-else>已进入打卡范围：</span>
        </div>
        <div class="range-list">
          <div v-for="(area, idx) in signAddress" :key="idx" class="range-item">
            {{ area.address }}
          </div>
        </div>
      </div>
      <!-- 底部提示 -->
      <div class="sign-tip">请到达学校后进行签到任务</div>
    </div>
    <!-- 错误提示 -->
    <van-dialog v-model:show="showErrorDialog" title="提示">
      <div style="padding: 10px; text-align: center">二维码无效或非本校签到码，请重试！</div>
    </van-dialog>
  </div>
  <div v-else class="sign-page-over">
    <img class="sign-page-over-img" src="@/assets/images/studentTask/sign_over.png" alt="" />
    <div class="flex">
      <img class="top-img" src="@/assets/images/studentTask/top_img.png" alt="" />
      <div class="class-name">欢迎{{ collegeNameStr + classNameStr }}</div>
      <div class="student-name">{{ nameStr }}同学</div>
      <div class="school-name">—— 加入沈阳职业技术学院! ——</div>
      <div class="room-name-bg">
        <div class="room-name">您的宿舍在{{ buildingNameStr }}-{{ roomNumberStr }}-{{ bedNumberStr }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";
import Range from "./components/Range.vue";
import { getSysConfigVoByKeyApi, finishStuTaskApi, getStudentInfoApi, finishSthCheckInApi } from "@/api/modules/studentEnrollment";
import { useRoute } from "vue-router";
import { useLsMap } from "@/hooks/useLsMap";
import { getLocation, scanQRCode } from "./index";

const route = useRoute();
const taskId = ref<string>("");
const isClock = ref(false); // 默认为不在范围内
let locationCheckTimer: number | null = null;
const isSign = ref(false);
const classNameStr = ref("");
const nameStr = ref("");
const collegeNameStr = ref(""); // 院系名称
const buildingNameStr = ref(""); // 宿舍楼栋名称
const roomNumberStr = ref(""); // 宿舍编号
const bedNumberStr = ref(""); // 宿舍楼栋编号

onMounted(() => {
  taskId.value = route.query.taskId as string;
  getSignAddress();
  getStudentInfo();
});

// 清理定时器
onBeforeUnmount(() => {
  if (locationCheckTimer) {
    clearInterval(locationCheckTimer);
    locationCheckTimer = null;
  }
});

const getStudentInfo = async () => {
  try {
    const { data, code } = await getStudentInfoApi();
    if (+code === 0) {
      const { className, name, collegeName, buildingName, roomNumber, bedNumber, checkInStatus } = data;
      classNameStr.value = className;
      nameStr.value = name;
      collegeNameStr.value = collegeName;
      buildingNameStr.value = buildingName;
      roomNumberStr.value = roomNumber;
      bedNumberStr.value = bedNumber;
      isSign.value = checkInStatus;
    }
  } catch {
    console.log("获取学员信息失败");
  }
};

// 检查当前位置是否在任意签到范围内
const checkCurrentLocationInRange = (currentLocation: { lat: number; lng: number; radius: number }) => {
  for (const area of signAddress.value) {
    try {
      const { isJudgeRange } = useLsMap(undefined, { lat: area.lat, lng: area.lng, radius: area.scope });
      const isInRange = isJudgeRange({ lat: area.lat, lng: area.lng, radius: area.scope as number }, currentLocation);

      if (isInRange) {
        console.log(`在签到范围内: ${area.address}`);
        return true;
      }
    } catch (error) {
      console.error(`检查范围失败 - ${area.address}:`, error);
    }
  }
  return false;
};

// 获取是否进入签到范围，在任意一个校区签到范围内均可以签到
const getSignEnable = () => {
  isClock.value = false;

  if (signAddress.value.length === 0) {
    return;
  }
  // 启动位置检测
  getLocation((currentLocation: { lat: number; lng: number; radius: number }) => {
    console.log("当前位置:", currentLocation);
    const inRange = checkCurrentLocationInRange(currentLocation);
    isClock.value = inRange;
    if (inRange) {
      console.log("已进入签到范围");
    } else {
      console.log("当前不在任何签到范围内");
    }
  });
};

// 可签到地点
const signAddress = ref<any[]>([]);
const getSignAddress = async () => {
  const { data } = (await getSysConfigVoByKeyApi("welcome.new.stu.schoolArea")) as { data: any };
  const { configValue } = data ?? {};
  const configObj = JSON.parse(configValue);
  signAddress.value = configObj;
  getSignEnable();
};

const showErrorDialog = ref(false);

function handleSign() {
  if (isClock.value) {
    scanQRCode(async (res: { resultStr: string }) => {
      console.log("调用扫码功能开始扫码=====999999", res.resultStr);
      if (res.resultStr === "sign_qrcode") {
        finishTask();
        finishCheckIn();
      } else {
        showErrorDialog.value = true;
      }
    });
  }
}

// 完成任务
async function finishTask() {
  try {
    const { code } = await finishStuTaskApi(taskId.value);
    if (+code === 0) {
      console.log("完成任务");
    }
  } catch (error) {
    console.log(error);
  }
}

// 完成打卡任务
async function finishCheckIn() {
  const { code } = await finishSthCheckInApi();
  if (+code === 0) {
    getStudentInfo();
    console.log("完成打卡任务");
  }
}
</script>

<style scoped>
.sign-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
}
.Background-image {
  position: fixed;
  top: 0;
  z-index: 5;
  width: 100%;
  height: 300px;
  text-align: center;
}
.Background-image img {
  width: 100%;
  height: 221px;
  object-fit: contain;
  object-position: top;
}
.bottom-content {
  z-index: 10;
  height: 100%;
  margin-top: 180px; /* 给出足够的上边距，避免被固定定位的背景图覆盖 */
  background-color: transparent;
}
.sign-range {
  margin: 0 0 16px;
  color: #666666;
  text-align: center;
}
.sign-notice {
  display: flex;
  align-items: center;
  justify-content: center;
}
.range-list {
  margin-top: 4px;
}
.range-item {
  margin: 2px 0;
  font-size: 14px;
  color: #1677ff;
}
.sign-welcome {
  margin: 24px 0 16px;
  font-size: 15px;
  line-height: 1.8;
  color: #333333;
  text-align: center;
}
.sign-tip {
  margin: 24px 0 0;
  font-size: 14px;
  color: #888888;
  text-align: center;
}
.sign-page-over {
  width: 100%;
  height: 100vh;
}
.sign-page-over-img {
  position: fixed;
  z-index: 1;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100vw;
}
.top-img {
  z-index: 10;
  width: 258px;
  height: 66px;
  margin-top: 62px;
  object-fit: cover;
}
.class-name {
  z-index: 10;
  margin-top: 12px;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
}
.student-name {
  z-index: 10;
  margin-top: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #fff1bb;
}
.school-name {
  z-index: 10;
  margin-top: 12px;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
}
.room-name-bg {
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 234px;
  height: 28px;
  margin-top: 12px;
  background-color: #fff1bb;
  border-radius: 14px;
}
.room-name {
  font-size: 14px;
  font-weight: bold;
  color: #0b79be;
}
</style>
