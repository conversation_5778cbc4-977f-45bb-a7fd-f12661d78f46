{"// 警告": "配置过的字段不可删除，只允许修改！否则只能清除localStorage数据才能生效！", "// 提示": "此处配置字段值可看 src/stores/interface/index.ts 中 customSettings 定义的字段", "huat": {"hiddenGroupFnBtn": true, "classGroupNeedApproval": true}, "zuel": {"isShowGroup": true}, "hzau": {"phoneBindingFormTipsText": true, "phoneBindingDownloadUrl": "/phoneBinding/download"}, "csmz": {"verificationFormExcludeFields": ["zkzh"], "verificationUseridCanBeEmpty": true, "verificationCustomPage": true, "themeColor": "#CA2930"}, "lnvut": {"verificationNoAuth": true, "verificationFormExcludeFields": ["zkzh", "sjh"], "verificationNoCheckCode": true}, "hbskzy": {"verificationNoAuth": true, "verificationFormExcludeFields": [], "verificationFormKeyLabelMap": {"zkzh": "学号"}, "verificationNoCheckCode": true}, "wust": {"isPay": true, "verificationFormExcludeFields": ["sjh"], "verificationFormKeyLabelMap": {"zkzh": "学号"}, "toOldQrSign": true}, "ccnu": {"meetingThemeColor": "#00849a"}, "hjnu": {"hideBuildingOptions": true}, "lzut": {"hideCurriculumConFig": true}, "vtcsy": {"verificationNoAuth": true}, "wcsu": {"verificationNoAuth": true}}