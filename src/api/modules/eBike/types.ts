export namespace IEBike {
  export interface IConfigVO {
    /**
     * 车辆审核状态
     */
    auditStatusList?: ITypeVO[];
    /**
     * 操作日志业务类型
     */
    businessTypeList?: ITypeVO[];
    /**
     * 用户身份
     */
    userTypeList?: ITypeVO[];
    /**
     * 违规类型
     */
    violationTypeList?: IViolationTypeVO[];
  }
  export interface ITypeVO {
    label: string;
    value: number;
  }
  export interface IViolationTypeVO {
    /**
     * 创建时间
     */
    createTime?: string;
    id?: number;
    /**
     * 违规名称
     */
    name?: string;
    /**
     * 违规说明
     */
    violationExplain?: string;
    /**
     * 违规对应次数
     */
    sort?: number;
  }
  export interface IHomeVO {
    /**
     * 是否审核人
     */
    auditFlag?: boolean;
    /**
     * 部门
     */
    departmentName?: string;
    /**
     * 手机号
     */
    phone?: string;
    /**
     * 用户id
     */
    userId?: string;
    /**
     * 姓名
     */
    userName?: string;
    /**
     * 用户身份
     */
    userType?: number;
  }
  export class IBikeInfo {
    licenseNumber?: string;
    licenseFront?: string;
    licenseBack?: string;
    bikeFront?: string;
    bikeBack?: string;
  }

  export class IViolationVo {
    /**
     * 创建时间
     */
    createTime?: string;
    id?: number;
    /**
     * 车牌号
     */
    licensePlate?: string;
    /**
     * 用户手机号
     */
    phone?: string;
    /**
     * 用户部门
     */
    userDepartment?: string;
    /**
     * 用户id
     */
    userId?: string;
    /**
     * 用户姓名
     */
    userName?: string;
    /**
     * 用户身份 1教师 2学生 3第三类人员 4第四类人员
     */
    userType?: number;
    /**
     * 车辆照片 两张
     */
    vehiclePhotos?: string[];
    /**
     * 违规说明
     */
    violationExplain?: string;
    /**
     * 违规地点
     */
    violationLocation?: string;
    /**
     * 违规照片 最多两张
     */
    violationPhotos?: string[];
    /**
     * 违规时间
     */
    violationTime?: string;
    /**
     * 违规类型
     */
    violationType?: number;
  }

  export class IUserInfo {
    name?: string;
    role?: string;
    department?: string;
    mobile?: string;
    userId?: string;
    licenseNumber?: string;
  }

  export class IRegisterItemVo {
    /**
     * 审核状态 0审批中 1未通过 2已登记 3注销
     */
    auditStatus?: number;
    /**
     * 申请时间
     */
    createTime?: string;
    /**
     * 行驶证 最多两张
     */
    drivingLicense?: string[];
    id?: number;
    /**
     * 车牌号
     */
    licensePlate?: string;
    /**
     * 用户手机号
     */
    phone?: string;
    /**
     * 不通过理由
     */
    rejectReason?: string;
    /**
     * 手写签名图片
     */
    sign?: string;
    /**
     * 修改时间
     */
    updateTime?: string;
    /**
     * 用户部门
     */
    userDepartment?: string;
    /**
     * 用户id
     */
    userId?: string;
    /**
     * 用户姓名
     */
    userName?: string;
    /**
     * 用户身份 1教师 2学生 3第三类人员 4第四类人员
     */
    userType?: number;
    /**
     * 车辆照片 两张
     */
    vehiclePhotos?: string[];
    /**
     * 违规次数
     */
    violationCount?: number;
  }
}
