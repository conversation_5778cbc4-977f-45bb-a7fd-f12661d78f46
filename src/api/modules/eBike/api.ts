import { IEBike } from "@/api/modules/eBike/types";
import { APP_SERVICE } from "@/api/config/servicePort";
import http from "@/api";

export const getConfig = () => {
  return http.post<IEBike.IConfigVO>(`${APP_SERVICE}/vehicle/index/getConfigInfo`);
};
/**
 * 首页数据
 */
export const getHomeData = () => {
  return http.post<IEBike.IHomeVO>(`${APP_SERVICE}/vehicle/index/getIndex`);
};

/**
 * 提交登记
 */
export const submitRegister = (params: any) => {
  return http.post<any>(`${APP_SERVICE}/vehicle/registerRecord/saveVehicleRegister`, params, { noErrorMessage: true });
};

/**
 * 审核人提交登记
 */
export const auditorSubmitRegister = (params: any) => {
  return http.post<any>(`${APP_SERVICE}/vehicle/registerRecord/saveVehicleRegisterByAudit`, params, { noErrorMessage: true });
};

/**
 * 登记记录
 */
export const getRegisterList = (params: any) => {
  return http.post<{ total: number; records: IEBike.IRegisterItemVo[] }>(`${APP_SERVICE}/vehicle/registerRecord/getVehicleRegisterPageList`, params);
};

/**
 * 添加违规
 */
export const addViolation = (params: any) => {
  return http.post<any>(`${APP_SERVICE}/vehicle/violationRecord/saveVehicleViolation`, params);
};

/**
 * 违规记录
 */
export const getViolationList = (params: any) => {
  return http.post<{ total: number; records: IEBike.IViolationVo[] }>(`${APP_SERVICE}/vehicle/violationRecord/getVehicleViolationPageList`, params);
};

/**
 * 审核登记
 */
export const auditRegister = (params: any) => {
  return http.post<any>(`${APP_SERVICE}/vehicle/registerRecord/auditVehicleRegister`, params);
};

/**
 * 注销登记
 */
export const cancelRegister = (id: number) => {
  return http.post<any>(`${APP_SERVICE}/vehicle/registerRecord/offVehicle/${id}`);
};
