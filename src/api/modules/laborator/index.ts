import { startAutoLBSOne } from "@/hooks/WecomSDK";
import { APP_SERVICE } from "@/api/config/servicePort";
import http from "@/api";
import dayjs from "dayjs";

// 获取当前时间的学年学期
export const getCurrentSchoolCalendarApi = () => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/manage/schoolCalendar/current`).then(res => {
    if (res.data && res.data.length) {
      const temp = res.data[0];
      let date = dayjs(temp.startTime);
      temp.endTime = dayjs(temp.endTime).format("YYYY-MM-DD");
      let week: any = [{}, {}, {}, {}, {}, {}, {}];
      temp.weekList = [];
      const dateName = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      while (date.format("YYYY-MM-DD") <= temp.endTime) {
        const day = date.day();
        if (day === 0) {
          week[6] = { date: date.format("YYYY-MM-DD"), abbr: date.format("M-D"), name: dateName[day], dayOfWeek: day };
          temp.weekList.push(week);
          week = [{}, {}, {}, {}, {}, {}, {}];
        } else {
          week[day - 1] = { date: date.format("YYYY-MM-DD"), abbr: date.format("M-D"), name: dateName[day], dayOfWeek: day };
        }
        date = date.add(1, "day");
      }
      if (week[0].date) {
        temp.weekList.push(week);
      }
      return temp;
    } else {
      return {};
    }
  });
};

// 我的实验项目
export const getApplyListApi = (param: any) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/getApplyList`, param);
};

// 发起实验室申请
export const addApplyApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/add`, data);
};

// 获取实验室申请详情
export const getApplyInfoApi = (id: string) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/info?id=${id}`);
};

// 根据实验项目获取排课记录
export const getLabApplySchedulingByLabApplyIdApi = (id: string) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/getLabApplySchedulingByLabApplyId?labApplyId=${id}`);
};

// 实验室项目审批
export const approvalApplyApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/approval`, data);
};

// 根据选定周获取已排课的日期
export const getPlanCourseDateListApi = (roomId: number, teachWeek: number) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/getPlanCourseDateList?realLabRoomId=${roomId}&teachWeek=${teachWeek}`);
};

// 获取教师签到项目和地点
export const getSignAddressAndLessonApi = () => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/teacher/getSignAddressAndLesson`);
};
// 教师签到
export const teacherSignInApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/teacher/teacherSignIn`, data);
};
// 教师签到列表
export const teacherCheckInRecordsApi = (id: any) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/teacherCheckInRecords?labApplyId=${id}`);
};
// 查看签到记录
export const viewCheckInRecordApi = (id: any, userid: any) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/viewCheckInRecord?labApplyId=${id}&userid=${userid}`);
};
// 排课
export const savePlanCourseApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/savePlanCourse`, data);
};

// 删除
export const labApplyRecordsDelApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/manage/labApplyRecords/del`, data);
};

// 撤回
export const revocationApi = (id: string) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/revocation?labApplyId=${id}`);
};

// 提前结束
export const earlyTerminationApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/earlyTermination`, data);
};

// 添加指导教师
export const addLabInstructorApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/addLabInstructor`, data);
};

// 学生加入审批
export const examineAndApproveApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/examineAndApprove`, data);
};
// 移除参与人
export const removeParticipantApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/project/removeParticipant`, data);
};

// 通用接口
export const getLabTypeApi = () => {
  return http.get(`${APP_SERVICE}/mapi/lab/reservation/common/getLabType`);
};
export const getCollegeListApi = () => {
  return http.get(`${APP_SERVICE}/mapi/lab/reservation/common/getCollegeList`);
};
export const getLabRoomListApi = () => {
  return http.get(`${APP_SERVICE}/mapi/lab/reservation/common/getLabRoomList`);
};
export const getRoleListApi = () => {
  return http.get(`${APP_SERVICE}/mapi/lab/reservation/common/getRoleList`);
};
export const getRoomListByLabApi = (labCode: string) => {
  return http.get(`${APP_SERVICE}/mapi/lab/reservation/common/getRoomListByLab?labCode=${labCode}`);
};

// 学生相关
// 我的实验项目
export const getStudentPageLabApi = (param: any) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/student/pageLab`, param);
};
// 申请加入实验项目
export const applyForLabApi = (labApplyId: string) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/student/applyForLab`, { labApplyId });
};
// 获取实验详情
export const getLabInfoApi = (id: string) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/student/labInfo?id=${id}`);
};
// 获取项目签到地点和节次
export const getLabApplySignAddressAndLessonApi = (id: string) => {
  return http.get<any>(`${APP_SERVICE}/mapi/lab/reservation/front/student/getLabApplySignAddressAndLesson?labApplyId=${id}`);
};
// 学生签到
export const userSignApi = (data: any) => {
  return http.post<any>(`${APP_SERVICE}/mapi/lab/reservation/front/student/userSign`, data);
};

/**
 * sdk调用定位
 */
(window as any).meetingTimer = null;
export function getLocation(fn: Function) {
  clearInterval((window as any).meetingTimer);
  try {
    startAutoLBSOne(true, (v: any) => {
      fn({ lat: v.latitude, lng: v.longitude, radius: v.accuracy });
      (window as any).meetingTimer = setInterval(async () => {
        try {
          await startAutoLBSOne(true, (v: any) => {
            fn({ lat: v.latitude, lng: v.longitude, radius: v.accuracy });
          });
        } catch (error) {
          clearInterval((window as any).meetingTimer);
        }
      }, 2000);
    });
  } catch (error) {
    console.log(error, "sdk调用定位失败！！！");
  }
}
