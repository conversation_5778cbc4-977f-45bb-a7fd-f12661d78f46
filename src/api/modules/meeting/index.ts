import { APP_SERVICE, COMMON_SERVICE } from "@/api/config/servicePort";
import http from "@/api";
import type { IMeeting } from "./type";
import type { ResPage } from "@/api/interface";

// 分页查询人员签到信息
export const pageUserMeetingApi = (params: { page: number; size: number; signInStatus: number }) => {
  return http.get<IMeeting.PageUserMeetingApiReturn>(`${APP_SERVICE}/mapi/meeting/user/pageUserMeeting`, params);
};

// 查询会议信息
export const getMeetingInfoApi = (params: { meetingId: number }) => {
  return http.get<IMeeting.MeetingDetails>(`${APP_SERVICE}/mapi/meeting/info/get`, params);
};

// 获取用户签到状态
export const getUserMeetingStatusApi = (params: { meetingId: number }) => {
  return http.get<IMeeting.MeetingStatus>(`${APP_SERVICE}/mapi/meeting/user/getUserMeetingStatus`, params);
};

// 简单签到
export const simpleSignInApi = (params: { meetingId: number }) => {
  return http.post<IMeeting.PageUserMeetingApiReturn>(`${APP_SERVICE}/mapi/meeting/user/simpleSignIn`, params);
};

/**
 * 分页查询会议列表
 * /mapi/meeting/info/page
 */
export const getMeetingListApi = (params: IMeeting.getMeetingListParams) => {
  return http.get<ResPage<IMeeting.meetingVO>>(`${APP_SERVICE}/mapi/meeting/info/page`, params);
};

/**
 * 新增、编辑会议
 */
export const addOrUpdateMeetingApi = (params: IMeeting.addOrUpdateParams) => {
  return http.post(`${APP_SERVICE}/mapi/meeting/info/addOrUpdate`, params);
};

/**
 * 导入会议人员
 * /mapi/meeting/user/import
 */
export const importMeetingUserApi = (params: any) => {
  return http.post<IMeeting.importMeetingResponse>(`${APP_SERVICE}/mapi/meeting/user/import`, params);
};

/**
 * /mapi/meeting/user/batchDel
 * 批量删除人员
 */
export const batchDelMeetingUserApi = (meetingUserIds: Array<number>) => {
  return http.post(`${APP_SERVICE}/mapi/meeting/user/batchDel`, { meetingUserIds });
};

/**
 * 下载导入模板
 * /mapi/meeting/user/downloadTemplate
 */
export const downloadTemplateApi = () => {
  return http.get<Blob>(
    `${APP_SERVICE}/mapi/meeting/user/downloadTemplate`,
    {},
    {
      responseType: "blob"
    }
  );
};

export const getPageMeetingUser = (params: IMeeting.MeetingUserRequest) => {
  return http.get<IMeeting.MeetingUserResponse>(`${APP_SERVICE}/mapi/meeting/user/pageMeetingUser`, params);
};

export const meetingBatchDel = (data: { meetingIds?: number[] }) => {
  return http.post(`${APP_SERVICE}/mapi/meeting/info/batchDel`, data);
};

export const meetingInfoGet = (meetingId: string) => {
  return http.get<{
    creatorName: string;
    createDate: string;
    signInAddress: string;
    meetingName: string;
    signInAddressCenterPoint: string;
    signInMode: number;
    signInRange: number;
    userMode: number;
    meetingTypeName: string;
    noticeFlag: boolean;
  }>(`${APP_SERVICE}/mapi/meeting/info/get`, { meetingId });
};

export const meetingUserUpdate = (data: { signInStatus: number; meetingUserId: number }) => {
  return http.post(`${APP_SERVICE}/mapi/meeting/user/update`, data);
};

/**
 * 添加会议人员
 */
export const meetingUserAdd = (data: { name: string; signInStatus: number; meetingId: number; userid: string }) => {
  return http.post(`${APP_SERVICE}/mapi/meeting/user/add`, data);
};

/**
 * 根据配置键名查询配置值
 */
export const getSysConfigVoByKeyApi = (configKey: string) => {
  return http.get<IMeeting.ConfigList>(`${COMMON_SERVICE}/system/config/getSysConfigVoByKey`, { configKey });
};
