import http from "@/api";
import { StudentEnrollment } from "@/api/modules/studentEnrollment/type";
import { COMMON_SERVICE } from "@/api/config/servicePort";

export const getCollegeListApi = () => {
  return http.get<{ colleges: StudentEnrollment.ICollege[] }>(`${COMMON_SERVICE}/newStudentData/college/info`);
};

export const getDashboardApi = (params: any) => {
  return http.post<StudentEnrollment.IDashboardData>(`${COMMON_SERVICE}/newStudentData/mobile/dashboard`, params);
};

export const getStudentDetailApi = (params: any) => {
  return http.post<StudentEnrollment.IStudentInfo[]>(`${COMMON_SERVICE}/newStudentData/student/detail`, params);
};

// 新生任务列表
export const stuTaskPageApi = (params: any) => {
  return http.get<any>(`${COMMON_SERVICE}/newStudentTask/page`, params, {
    loading: true
  });
};

/**
 * 完成新生任务
 */
export const finishStuTaskApi = (newStudentTaskId: string) => {
  return http.post<any>(`${COMMON_SERVICE}/newStudentTask/completeNewStudentTask?newStudentTaskId=${newStudentTaskId}`, {
    loading: true
  });
};

/**
 * 完成新生打卡任务
 */
export const finishSthCheckInApi = () => {
  return http.post<any>(`${COMMON_SERVICE}/newStudentTask/clockIn`, {
    loading: true
  });
};

/**
 * 获取所有已完成的新生任务
 */
export const getFinishStuTaskApi = () => {
  return http.get<any>(`${COMMON_SERVICE}/newStudentTask/getAllCompletedNewStudentTask`, {
    loading: true
  });
};

/**
 * 获取学员信息
 */
export const getStudentInfoApi = () => {
  return http.get<any>(`${COMMON_SERVICE}/newStudentData/getCurrentUserInfo`, {
    loading: true
  });
};

/**
 * 提交任务收集表单
 */
export const submitStuTaskFormApi = (params: any) => {
  return http.post<any>(`${COMMON_SERVICE}/newStudentTrafficInfo/save`, params, { loading: true });
};
/**
 * 获取学员信息带userid参数
 */
export const getStudentInfoApiWithParams = params => {
  return http.get<any>(`${COMMON_SERVICE}/newStudentData/getCurrentUserInfo`, params, {
    loading: true
  });
};

/**
 * 查询采集到的交通信息
 */
export const getMethodByNewStudentDataId = (params: any) => {
  return http.get<any>(`${COMMON_SERVICE}/newStudentTrafficInfo/getByNewStudentDataId`, params, {
    loading: true
  });
};
/*
 * 保存新生基本信息
 */
export const saveStudentInfoApi = data => {
  return http.post<any>(`${COMMON_SERVICE}/newStudentInfoCollection/save`, data, {
    loading: true
  });
};

/**
 * 根据学生id查询基本信息采集
 */
export const getByNewStudentDataId = params => {
  return http.get<any>(`${COMMON_SERVICE}/newStudentInfoCollection/getByNewStudentDataId`, params);
};

/**
 * 根据配置键名查询配置值
 */
export const getSysConfigVoByKeyApi = (configKey: string) => {
  return http.get<StudentEnrollment.ConfigList>(`${COMMON_SERVICE}/system/config/getSysConfigVoByKey`, { configKey });
};
