import http from "@/api";
import { StudentEnrollment } from "@/api/modules/studentEnrollment/type";
import { COMMON_SERVICE } from "@/api/config/servicePort";

export const getCollegeListApi = () => {
  return http.get<{ colleges: StudentEnrollment.ICollege[] }>(`${COMMON_SERVICE}/newStudentData/college/info`);
};

export const getDashboardApi = (params: any) => {
  return http.post<StudentEnrollment.IDashboardData>(`${COMMON_SERVICE}/newStudentData/mobile/dashboard`, params);
};

export const getStudentDetailApi = (params: any) => {
  return http.post<StudentEnrollment.IStudentInfo[]>(`${COMMON_SERVICE}/newStudentData/student/detail`, params);
};
