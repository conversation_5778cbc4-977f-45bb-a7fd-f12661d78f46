export namespace StudentEnrollment {
  export interface IClassroom {
    classCode: string;
    className: string;
  }

  export interface IMajor {
    majorCode: string;
    majorName: string;
    classes: IClassroom[];
  }

  export interface ICollege {
    collegeCode: string;
    collegeName: string;
    majors: IMajor[];
  }

  export interface IRankData {
    collegeCode: string;
    collegeName: string;
    totalCount: number;
    reportedCount: number;
    reportRate: number;
  }

  export interface IDashboardData {
    totalStudents?: number;
    checkedInCount?: number;
    totalReportCount?: number;
    reportedCount?: number;
    collegeReportRanks: IRankData[];
  }

  export interface IStudentInfo {
    xh: string;
    name: string;
    wechatStatus: boolean;
    basicInfoStatus: boolean;
    trafficInfoStatus: boolean;
    checkInStatus: boolean;
  }
}
