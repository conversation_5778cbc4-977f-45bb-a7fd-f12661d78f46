import type { ILogin } from "@/api/modules/login/type";
import type { IUser } from "@/api/modules/system/type"; /* UserState */

/* UserState */
export interface UserState {
  tokenInfo: ILogin.TokenInfo | any;
  userInfo: IUser.UserInfo | null;
  authMenuList: Menu.MenuOptions[];
  weChat: {
    agentId: string;
    corpId: string;
  };
  aiInfo: {
    appCallToken: string | null;
    appSearchToken: string | null;
    aiUrl: string | null;
  };
}

/**
 * xm 姓名
 * sjh 手机号
 * sfzh 身份证号
 * zkzh 准考证号
 */
export type VerificationFormExcludeFields = "xm" | "sjh" | "sfzh" | "zkzh";

/**
 * 系统自定义参数
 */
export interface CustomSetting {
  /*
   * 接口配置
   */
  systemConfig: {
    projectName?: string;
  };
  /**
   * 是否拉取系统配置
   */
  hasGetSettings: boolean;
  /**
   * 通过系统配置后根据 src/assets/json/customSettingConfig.json 拉取自定义设置
   */
  customSettings: Partial<{
    /**
     * 手机号绑定表单下方注意提示文案
     */
    phoneBindingFormTipsText: boolean;
    /**
     * 下载企微跳转地址
     */
    phoneBindingDownloadUrl: string;
    /**
     * 隐藏建群详情功能按钮（更新群聊、修改群名称、邀请加入群聊）
     */
    hiddenGroupFnBtn: boolean;
    /**
     * 二次验证无统一身份认证
     */
    verificationNoAuth: boolean;
    /**
     * 二次验证表单排除字段
     */
    verificationFormExcludeFields: VerificationFormExcludeFields[];
    /**
     * 二次验证表单字段别名
     */
    verificationFormKeyLabelMap: Record<VerificationFormExcludeFields, string>;
    /**
     * 二次验证是否不需要验证码
     */
    verificationNoCheckCode: boolean;
    /**
     * 二次验证用户id可为空
     */
    verificationUseridCanBeEmpty: boolean;
    /**
     * 二次验证自定义页面
     */
    verificationCustomPage: boolean;
    /**
     * 班级建群是否需要审批
     */
    classGroupNeedApproval: boolean;
    /**
     * 主题色
     */
    themeColor: string;
    /**
     * 场馆预约是否需要调整支付页面
     */
    isPay: boolean;
    /**
     * 签到会议主题色
     */
    meetingThemeColor: string;

    /**
     * 空教室查询是否隐藏楼栋查询
     */
    hideBuildingOptions: boolean;
    /**
     * 课表是否显示群聊相关
     */
    isShowGroup: boolean;
    /**
     * 课表隐藏设置
     */
    hideCurriculumConFig: boolean;
    /**
     * 扫码签到优化版
     */
    toOldQrSign: boolean;
  }>;
}
