import { defineStore } from "pinia";
import piniaPersistConfig from "@/stores/helper/persist";
import { getSystemConfigApi } from "@/api/modules/customSetting";
import customConfig from "@/assets/json/customSettingConfig.json";
import type { CustomSetting } from "@/stores/interface";

const initialSetting: CustomSetting["customSettings"] = {
  verificationFormExcludeFields: ["sjh"]
};

const projectNameMap: Record<string, any> = {
  whut: "武汉理工",
  huat: "湖北汽车学院",
  hzau: "华中农业大学",
  wcsu: "首义学院",
  zuel: "中南财经政法大学",
  lzju: "兰州交大",
  lzut: "兰州理工",
  hbue: "湖北第二师范学院",
  csmz: "长沙民政",
  lnvut: "辽宁理工职业大学",
  hbskzy: "湖北生物科技职业学院",
  wust: "武汉科技大学",
  ccnu: "华中师范大学",
  test: "测试",
  hjnu: "汉江师范学院",
  vtcsy: "沈阳职业技术学院"
};

export const useCustomSetting = defineStore({
  id: "ls-customSetting",
  state: (): CustomSetting => ({
    hasGetSettings: false,
    systemConfig: {},
    customSettings: { ...initialSetting }
  }),
  actions: {
    /**
     * 获取远程配置，根据远程配置的 projectName 选择对应的自定义配置
     */
    async getSettings() {
      try {
        const { data } = await getSystemConfigApi();
        /*
         * 配置名称是否发生变化
         * 主要针对同一域名下配置发生变化，localstorage 缓存的配置没被清除
         */
        if (data.projectName && !projectNameMap[data.projectName]) {
          data.projectName = "default";
        }
        data.projectName ||= "default";
        const projectNameChanged = this.systemConfig.projectName !== data.projectName;
        if (projectNameChanged) {
          this.resetSettings();
        }
        this.systemConfig = { ...this.systemConfig, ...data };
        const projectConfig = (customConfig as Record<string, any>)[data.projectName];
        this.customSettings = {
          ...this.customSettings,
          ...projectConfig
        };
      } finally {
        this.hasGetSettings = true;
      }
    },
    resetSettings() {
      this.customSettings = { ...initialSetting };
    }
  },
  persist: piniaPersistConfig("ls-appBase-customSetting", ["systemConfig", "customSettings"])
});
