export const studentTask = [
  {
    path: "/studentTask",
    name: "studentTask",
    component: () => import("@/views/studentTask/index.vue"),
    meta: {
      title: "新生任务"
    }
  },
  {
    path: "/studentSign",
    name: "studentSign",
    component: () => import("@/views/studentSign/index.vue"),
    meta: {
      title: "到校签到",
      keepAlive: true
    }
  },
  {
    path: "/gather",
    name: "gather",
    component: () => import("@/views/studentTask/gather.vue"),
    meta: {
      title: "新生报道交通信息收集表"
    }
  },
  {
    path: "/freshInfoGather",
    name: "freshInfoGather",
    component: () => import("@/views/studentTask/freshInfoGather.vue"),
    meta: {
      title: "新生基本信息采集",
      keepAlive: true
    }
  },
  {
    path: "/freshInfoGather",
    name: "freshInfoGather",
    component: () => import("@/views/studentTask/freshInfoGather.vue"),
    meta: {
      title: "新生基本信息采集",
      keepAlive: true
    }
  },
  {
    path: "/richText",
    name: "richText",
    component: () => import("@/views/studentTask/richText.vue"),
    meta: {
      title: "任务详情",
      keepAlive: true
    }
  },
  {
    path: "/webView",
    name: "webView",
    component: () => import("@/views/studentTask/webView.vue"),
    meta: {
      title: "任务详情",
      keepAlive: false
    }
  }
];
