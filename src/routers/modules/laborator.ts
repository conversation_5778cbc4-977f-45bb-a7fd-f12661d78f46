export const laborator = [
  {
    path: "/laborator/index",
    name: "LaboratorIndex",
    component: () => import("@/views/laborator/index.vue"),
    meta: {
      title: "实验室预约",
      keepAlive: true
    }
  },
  {
    path: "/laborator/submit",
    name: "LaboratorSubmit",
    component: () => import("@/views/laborator/submit.vue"),
    meta: {
      title: "实验室申请"
    }
  },
  {
    path: "/laborator/detail",
    name: "LaboratorDetail",
    component: () => import("@/views/laborator/detail.vue"),
    meta: {
      title: "实验项目详情"
    }
  },
  {
    path: "/laborator/plan",
    name: "LaboratorPlan",
    component: () => import("@/views/laborator/plan.vue"),
    meta: {
      title: "排课"
    }
  },
  {
    path: "/laborator/instructor",
    name: "LaboratorInstructor",
    component: () => import("@/views/laborator/instructor.vue"),
    meta: {
      title: "签到"
    }
  },
  {
    path: "/laborator/student",
    name: "LaboratorStudent",
    component: () => import("@/views/laborator/student.vue"),
    meta: {
      title: "实验室预约",
      keepAlive: true
    }
  },
  {
    path: "/laborator/labInfo",
    name: "LaboratorLabInfo",
    component: () => import("@/views/laborator/labInfo.vue"),
    meta: {
      title: "实验项目详情"
    }
  },
  {
    path: "/laborator/signin",
    name: "LaboratorSignin",
    component: () => import("@/views/laborator/signin.vue"),
    meta: {
      title: "签到"
    }
  },
  {
    path: "/laborator/position",
    name: "LaboratorPosition",
    component: () => import("@/views/laborator/position.vue"),
    meta: {
      title: "签到位置"
    }
  }
];
