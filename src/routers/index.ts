import { createRouter, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, create<PERSON>eb<PERSON><PERSON><PERSON> } from "vue-router";
import { useUserStore } from "@/stores/modules/user";
import { useAppStore } from "@/stores/modules/appStore";
import { appSearch } from "./modules/appSearch";
import { group } from "./modules/group";
import { curriculum } from "./modules/curriculum";
import { bookStadium } from "./modules/bookStadium";
import { fleaMarket } from "./modules/fleaMarket";
import { welcomeNewRouter } from "@/routers/modules/welcomeNewRouter";
import { phoneBinding } from "@/routers/modules/phoneBinding";
import { identityCode } from "./modules/identityCode";
import { identityAuthenticationRouter } from "@/routers/modules/identityAuthenticationRouter";
import { inviteOutRouter } from "./modules/inviteOut";
import { verificationRouter } from "./modules/verification";
import { emptyClassroom } from "./modules/emptyClassroom";
import { informationPolymerization } from "./modules/informationPolymerization";
import { suggestionBox } from "./modules/suggestionBox";
import { getAuth, markAuth, updateAppInfoBySearchParams } from "@/utils/login";
import { loginWithCode, loginWithCodeMock, wechatLogin } from "@/api/modules/login";
import { externalJoining } from "./modules/externalJoining";
import { useCustomSetting } from "@/stores/modules/customSetting";
import { reportForRepairRouters } from "@/routers/modules/reportForRepair";
import { registerConfig } from "@/utils/WxConfig";
import { clockIn } from "./modules/clockIn";
import { cockpit } from "./modules/cockpit";
import { medicalCareRouter } from "./modules/medicalCare";
import { meetingRouter } from "./modules/meeting";
import { queryApp } from "./modules/queryApp";
import { openAccount } from "./modules/openAccount";
import { esQuery } from "./modules/esQuery";
import { studentTask } from "./modules/studentTask";
import { getEnv } from "@/utils";
import { cardApplication } from "@/routers/modules/cardApplication";
import { laborator } from "./modules/laborator";
import { eBike } from "@/routers/modules/eBike";
import { checkIn } from "@/routers/modules/checkIn";
import { repairService } from "@/routers/modules/repairService";
import { appointBus } from "@/routers/modules/appointBus";
import { onboardingProcedures } from "@/routers/modules/onboardingProcedures";
import { classroomBooking } from "@/routers/modules/classroomBooking";
import { studentEnrollment } from "./modules/studentEnrollment";

// 企微应用跳转key对应的path
export const replace = {
  welcome: "",
  reservation: "/bookStadium/index",
  classGroup: "/group/classGroup",
  search: "/appSearch/index",
  appZone: "/appZone",
  fleaMarket: "/fleaMarket/index",
  scanCodeSign: "/scanCodeSign",
  deptGroup: "/group/departmentGroup",
  lessonGroup: "/group/lessonGroup"
};

const mode = import.meta.env.VITE_ROUTER_MODE;
const routerMode = {
  hash: () => createWebHashHistory(),
  history: () => createWebHistory(`${import.meta.env.VITE_PATH_NAME}/`)
};
const router = createRouter({
  history: routerMode[mode](),
  routes: [
    ...studentEnrollment,
    ...classroomBooking,
    ...onboardingProcedures,
    ...repairService,
    ...checkIn,
    ...eBike,
    ...cardApplication,
    ...appSearch,
    ...bookStadium,
    ...fleaMarket,
    ...group,
    ...identityCode,
    ...phoneBinding,
    ...inviteOutRouter,
    ...externalJoining,
    ...curriculum,
    ...verificationRouter,
    ...informationPolymerization,
    ...emptyClassroom,
    ...suggestionBox,
    ...clockIn,
    ...cockpit,
    ...medicalCareRouter,
    ...meetingRouter,
    ...queryApp,
    ...openAccount,
    ...laborator,
    ...esQuery,
    ...appointBus,
    ...studentTask,
    // 旧版登录
    {
      path: "/index", // 获取企业微信授权地址
      name: "Index",
      component: () => import("@/views/index.vue"),
      meta: {
        title: "跳转"
      }
    },
    {
      path: "/auth", // 登录跳转
      name: "Auth",
      component: () => import("@/views/auth.vue"),
      meta: {
        title: "登录"
      }
    },
    // 览山代理企微登录
    {
      path: "/authorize",
      name: "Authorize",
      component: () => import("@/views/LsAuth.vue"),
      meta: {
        title: "授权"
      }
    },
    {
      path: "/ls-login",
      name: "LsLogin",
      component: () => import("@/views/LsLogin.vue"),
      meta: {
        title: "认证"
      }
    },
    {
      path: "/test-auth",
      name: "TestAuth",
      component: () => import("@/views/TestAuth.vue"),
      meta: {
        title: "测试认证"
      }
    },
    // 新版登录
    {
      path: "/login", // 登录
      name: "Login",
      component: () => import("@/views/login.vue"),
      meta: {
        title: "跳转"
      }
    },
    {
      path: "/oauth2", // 登录
      name: "Oauth2",
      component: () => import("@/views/oauth2.vue"),
      meta: {
        title: "登录"
      }
    },
    {
      path: "/auth/sso/login", // 统一身份认证登录
      name: "AuthSsoLogin",
      component: () => import("@/views/authSsoLogin.vue"),
      meta: {
        title: "统一身份认证"
      }
    },
    {
      path: "/auth/sso/login1",
      name: "authSso",
      component: () => import("@/views/login/authSso.vue"),
      meta: {
        title: "auth登录"
      }
    },
    {
      path: "/auth/forbidden",
      name: "forbidden",
      component: () => import("@/views/login/forbidden.vue"),
      meta: {
        title: "无权访问"
      }
    },
    {
      path: "/appRemoval",
      name: "appRemoval",
      component: () => import("@/views/appRemoval.vue"),
      meta: {
        title: "应用已下架"
      }
    },
    {
      path: "/application",
      name: "application",
      component: () => import("@/views/application.vue"),
      meta: {
        title: ""
      }
    },
    {
      path: "/ai-search",
      name: "aiSearch",
      component: () => import("@/views/aiSearch/index.vue"),
      meta: {
        title: "AI智能搜索"
      }
    },
    {
      path: "/previewFile",
      name: "previewFile",
      component: () => import("@/views/previewFile/index.vue"),
      meta: {
        title: "帮助中心"
      }
    },
    {
      path: "/botPreview",
      name: "botPreview",
      component: () => import("@/views/botPreview/index.vue"),
      meta: {
        title: "智能体"
      }
    },
    {
      path: "/userSelect",
      name: "UserSelect",
      component: () => import("@/views/userSelect/index.vue"),
      meta: {
        title: "选人"
      }
    },
    // 迎新路由
    ...welcomeNewRouter,
    // 身份认证路由
    ...identityAuthenticationRouter,
    // 报修路由
    ...reportForRepairRouters
  ],
  strict: false,
  scrollBehavior: to => {
    const appStore = useAppStore();
    const currentPage = appStore.pageStack.find(v => v.name === to.name);
    const position = currentPage?.position ?? 0;
    const scrollEl = currentPage?.scrollEl ?? ".ls-main";
    const el = document.querySelector(scrollEl);
    if (el) {
      el.scrollTop = position;
    }
    return false;
  }
});

// 查找name的函数
export function findNameByPath(path: string) {
  for (const route of router.getRoutes()) {
    if (route.path === path) {
      return route.name;
    }
  }
  return null;
}

export const whitelist = [
  "ClassroomBookingHome",
  "Login",
  "Oauth2",
  "AuthSsoLogin",
  "Auth",
  "Index",
  "ShowSignCode",
  "Authorize",
  "LsLogin",
  "TestAuth",
  "appRemoval",
  "InviteForm",
  "InviteRes",
  "application",
  "PhoneBindingPreLogin",
  "OpenAccount",
  "OpenAccountSuccess",
  "OpenAccountFailed",
  "botPreview",
  "UserCard",
  "AppointForm",
  "ReplenishAppointForm",
  "ExternalUserIndex",
  "ExternalSelectRoute",
  "ExternalAppointSuccess",
  "ExternalUserRecordDetail"
];

// 判断是否企微环境
const isWxEnv = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  // Check for both MicroMessenger (WeChat base) and wxwork/WXWork variants
  return userAgent.indexOf("micromessenger") !== -1 && (userAgent.indexOf("wxwork") !== -1 || userAgent.indexOf("wework") !== -1);
};

/**
 * @description 路由拦截 beforeEach
 * */
router.beforeEach(async (to, from, next) => {
  // 动态设置标题
  document.title = to.meta.title ? `${to.meta.title}` : "";

  // 获取自定义设置
  const customSetting = useCustomSetting();
  if (!customSetting.hasGetSettings) {
    await customSetting.getSettings().catch(err => {
      console.log(err);
    });
  }

  // CodeAddress页面，非企微环境，不执行登录操作
  if (to.meta?.name === "CodeAddress" && !isWxEnv()) {
    return next();
  }
  const userStore = useUserStore();
  const appStore = useAppStore();
  const hasToken = userStore.tokenInfo;
  const { mockCode, agentId, corpId, code, token, path: pathName, loginType, userId } = to.query ?? {};
  if (agentId || loginType || corpId) updateAppInfoBySearchParams(to.query);
  if (to.meta?.noAuth || whitelist.includes(to.name as string) || userId) next();
  else {
    const appInfo = JSON.parse(localStorage.getItem("appInfo") || "{}");
    if (!hasToken) {
      if (mockCode) {
        try {
          appStore.updatePageLoading(true);
          const { data } = await loginWithCodeMock({
            agentId: (agentId ?? appInfo?.agentId ?? "1000056") as string,
            code: to.query.mockCode as string,
            corpId: (corpId ?? appInfo?.corpId ?? "ww3d03682990e6d799") as string
          });
          await userStore.setToken(data);
        } catch (error) {
          console.error(error);
        } finally {
          appStore.updatePageLoading(false);
        }
        next(true);
        return false;
      }
      if (code) {
        try {
          const env = getEnv();
          appStore.updatePageLoading(true);
          const { data } = await (env === "wechat"
            ? wechatLogin({
                appId: corpId + "",
                code: code + ""
              })
            : loginWithCode({
                code: code as string,
                agentId: agentId ?? appInfo?.agentId,
                corpId: corpId ?? appInfo.corpId
              }));
          markAuth(false);
          userStore.setUserTicket(data.userTicket as string);
          await userStore.setToken(data);
        } catch (error) {
          console.error(error);
        } finally {
          appStore.updatePageLoading(false);
        }
        next(true);
        return false;
      }
      if (token) {
        // 统一身份认证登录
        try {
          appStore.updatePageLoading(true);
          await userStore.setToken({
            token: token as string
          });
          markAuth(false);
        } catch (error) {
          console.error(error);
        } finally {
          appStore.updatePageLoading(false);
        }
        next({ name: pathName as string, query: appInfo, replace: true });
        return false;
      }
      getAuth({ path: to.path as string });
      next(false);
    } else {
      if (to?.meta?.needWxConfig) {
        await registerConfig(corpId ?? appInfo?.corpId, agentId ?? appInfo?.agentId);
      }
      next();
    }
  }
});
export default router;
