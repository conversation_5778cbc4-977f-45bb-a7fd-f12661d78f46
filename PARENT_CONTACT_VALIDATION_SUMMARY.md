# 家长联系方式必填校验实现总结

## 修改内容

### 1. 表单字段校验规则

为家长联系方式表格中的每个字段添加了详细的校验规则：

#### 姓名字段
```vue
<van-field 
  autocomplete="off" 
  v-model="item.name" 
  required 
  :rules="[{ required: true, message: '请填写姓名' }]"
  placeholder="请输入姓名"
/>
```

#### 关系字段
```vue
<van-field 
  autocomplete="off" 
  v-model="item.relationCHN" 
  required 
  :rules="[{ required: true, message: '请选择关系' }]"
  @click="handleFocusSelect(question, index)" 
  @focus="handleFocus" 
  right-icon="arrow-down" 
  placeholder="请选择关系"
/>
```

#### 手机号字段
```vue
<van-field 
  autocomplete="off" 
  v-model="item.phone" 
  required 
  :rules="parentPhoneRules"
  placeholder="请输入手机号"
/>
```

### 2. 新增手机号校验规则

```javascript
const parentPhoneRules = [
  { required: true, message: "请输入家长手机号" },
  { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" }
];
```

### 3. 增强提交校验逻辑

```javascript
const handleSubmit = () => {
  const options = questions.value[9].options as ParentContactInfo[];
  
  // 检查是否至少有一个家长联系方式
  if (!options.length) {
    showToast(`请填写家长联系方式`);
    return;
  }

  // 检查每个家长联系方式的必填字段
  for (let i = 0; i < options.length; i++) {
    const item = options[i];
    
    if (!item.name || item.name.trim() === '') {
      showToast(`请填写第${i + 1}个家长的姓名`);
      return;
    }
    
    if (!item.relationCHN || item.relationCHN.trim() === '') {
      showToast(`请选择第${i + 1}个家长的关系`);
      return;
    }
    
    if (!item.phone || item.phone.trim() === '') {
      showToast(`请填写第${i + 1}个家长的手机号`);
      return;
    }
    
    // 验证手机号格式
    const phonePattern = /^1[3-9]\d{9}$/;
    if (!phonePattern.test(item.phone)) {
      showToast(`第${i + 1}个家长的手机号格式不正确`);
      return;
    }
  }
  
  // 继续原有的提交逻辑...
};
```

## 校验功能

### 实时校验
- ✅ 姓名字段：必填校验
- ✅ 关系字段：必填校验
- ✅ 手机号字段：必填校验 + 格式校验

### 提交时校验
- ✅ 检查是否至少有一个家长联系方式
- ✅ 逐个检查每个家长信息的完整性
- ✅ 验证手机号格式（1开头，第二位3-9，总共11位）
- ✅ 提供具体的错误提示信息

### 用户体验优化
- ✅ 添加了 placeholder 提示文本
- ✅ 详细的错误提示信息（指明第几个家长的哪个字段有问题）
- ✅ 手机号格式实时校验

## 测试场景

1. **空表单提交**：提示"请填写家长联系方式"
2. **部分字段未填写**：提示具体哪个字段未填写
3. **手机号格式错误**：提示手机号格式不正确
4. **多个家长信息**：逐个校验每个家长的信息完整性

## 兼容性

- ✅ 保持原有功能不变
- ✅ 兼容已有的表单校验机制
- ✅ 不影响其他字段的校验逻辑
