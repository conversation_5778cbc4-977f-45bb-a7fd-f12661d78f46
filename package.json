{"name": "admin", "private": true, "version": "1.3.0", "type": "module", "description": "management system", "scripts": {"dev": "vite --mode development", "serve": "vite --mode test", "prd": "vite --mode prd", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prd": "vite build --mode prd", "build": "vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": " vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git pull && git add -A && git-cz && git push"}, "dependencies": {"@coze/api": "^1.0.21", "@coze/realtime-api": "^1.0.5", "@types/crypto-js": "^4.2.2", "@vant/touch-emulator": "^1.4.0", "@vant/use": "^1.6.0", "@vueuse/core": "^10.4.1", "@wecom/jssdk": "^1.4.5", "axios": "^1.5.0", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.9", "docx-preview": "^0.3.3", "docxtemplater": "^3.54.0", "dom-to-image": "^2.6.0", "echarts": "^5.5.1", "floating-ball": "0.2.8", "lib-flexible": "^0.3.2", "ls-desgin": "^0.9.4", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pdf-vue3": "^1.0.12", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "pizzip": "^3.1.7", "postcss-pxtorem": "^6.1.0", "qrcode": "^1.5.4", "qs": "^6.11.2", "screenfull": "^6.0.2", "signature_pad": "^5.0.7", "sortablejs": "^1.15.0", "v3-waterfall": "^2.0.0-beta.1", "vant": "^4.9.6", "vconsole": "^3.15.1", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "^0.5.0", "vue-qrcode": "^2.2.2", "vue-router": "^4.2.4", "vue3-markdown-it": "^1.0.10"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@fcli/vue-grid-waterfall": "^1.0.1", "@types/dom-to-image": "^2.6.7", "@types/md5": "^2.3.2", "@types/nprogress": "^0.2.0", "@types/postcss-pxtorem": "^6.0.3", "@types/qs": "^6.9.8", "@types/sortablejs": "^1.15.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vant/auto-import-resolver": "^1.0.2", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "autoprefixer": "^10.4.15", "cnjm-postcss-px-to-viewport": "^1.0.0", "commitizen": "^4.3.0", "commitlint": "^17.7.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.2.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "postcss": "^8.4.29", "postcss-html": "^1.5.0", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.66.1", "standard-version": "^9.5.0", "stylelint": "^15.10.3", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.0.0", "tailwindcss": "^3.3.3", "typescript": "^5.1.6", "unplugin-vue-components": "^0.25.2", "unplugin-vue-setup-extend-plus": "^1.0.0", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-pwa": "^0.16.5", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.11"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}