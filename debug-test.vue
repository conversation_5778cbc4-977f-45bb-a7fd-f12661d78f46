<template>
  <div>
    <van-button :type="getTaskButtonType(testItem)">测试按钮</van-button>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const projectName = ref<string>("vtcsy");
const canDoStepFour = ref(false);

const testItem = {
  id: 4,
  finishStatus: 1
};

// 获取任务按钮类型
const getTaskButtonType = (item: any): string => {
  if (!item) return "primary";
  
  if (item?.finishStatus === 2) {
    return "default";
  }

  // 只有当projectName为vtcsy且任务id为4时，才需要特殊处理
  if (projectName.value === "vtcsy" && Number(item.id) === 4) {
    return canDoStepFour.value ? "primary" : "default";
  }

  return "primary";
};
</script>
