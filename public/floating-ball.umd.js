(function(r0,K){typeof exports=="object"&&typeof module<"u"?K(exports):typeof define=="function"&&define.amd?define(["exports"],K):(r0=typeof globalThis<"u"?globalThis:r0||self,K(r0.floatingBall={}))})(this,function(r0){"use strict";var ua=Object.defineProperty;var ha=(r0,K,m0)=>K in r0?ua(r0,K,{enumerable:!0,configurable:!0,writable:!0,value:m0}):r0[K]=m0;var G=(r0,K,m0)=>ha(r0,typeof K!="symbol"?K+"":K,m0);const K="data:image/svg+xml;base64,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",m0="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='80px'%20height='80px'%20viewBox='0%200%2080%2080'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3eGroup%205%3c/title%3e%3cdefs%3e%3clinearGradient%20x1='24.7076195%25'%20y1='7.45976224%25'%20x2='76.0061882%25'%20y2='94.4011002%25'%20id='linearGradient-1'%3e%3cstop%20stop-color='%2387E8FF'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='%231480FF'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3ccircle%20id='path-2'%20cx='28'%20cy='28'%20r='28'%3e%3c/circle%3e%3cfilter%20x='-42.9%25'%20y='-21.4%25'%20width='185.7%25'%20height='185.7%25'%20filterUnits='objectBoundingBox'%20id='filter-3'%3e%3cfeOffset%20dx='0'%20dy='12'%20in='SourceAlpha'%20result='shadowOffsetOuter1'%3e%3c/feOffset%3e%3cfeGaussianBlur%20stdDeviation='6'%20in='shadowOffsetOuter1'%20result='shadowBlurOuter1'%3e%3c/feGaussianBlur%3e%3cfeColorMatrix%20values='0%200%200%200%200%200%200%200%200%200.538143985%200%200%200%200%201%200%200%200%200.202998661%200'%20type='matrix'%20in='shadowBlurOuter1'%3e%3c/feColorMatrix%3e%3c/filter%3e%3clinearGradient%20x1='24.7076195%25'%20y1='7.45976224%25'%20x2='76.0061882%25'%20y2='94.4011002%25'%20id='linearGradient-4'%3e%3cstop%20stop-color='%236AC8FF'%20offset='0%25'%3e%3c/stop%3e%3cstop%20stop-color='%231480FF'%20offset='100%25'%3e%3c/stop%3e%3c/linearGradient%3e%3ccircle%20id='path-5'%20cx='28'%20cy='27'%20r='27'%3e%3c/circle%3e%3cfilter%20x='-3.7%25'%20y='-3.7%25'%20width='107.4%25'%20height='107.4%25'%20filterUnits='objectBoundingBox'%20id='filter-6'%3e%3cfeGaussianBlur%20stdDeviation='1.5'%20in='SourceAlpha'%20result='shadowBlurInner1'%3e%3c/feGaussianBlur%3e%3cfeOffset%20dx='1'%20dy='-1'%20in='shadowBlurInner1'%20result='shadowOffsetInner1'%3e%3c/feOffset%3e%3cfeComposite%20in='shadowOffsetInner1'%20in2='SourceAlpha'%20operator='arithmetic'%20k2='-1'%20k3='1'%20result='shadowInnerInner1'%3e%3c/feComposite%3e%3cfeColorMatrix%20values='0%200%200%200%201%200%200%200%200%201%200%200%200%200%201%200%200%200%200.5%200'%20type='matrix'%20in='shadowInnerInner1'%3e%3c/feColorMatrix%3e%3c/filter%3e%3cpath%20d='M28.5,14%20C29.3284271,14%2030,14.6715729%2030,15.5%20L29.999,25.5%20L40,25.5%20C40.8284271,25.5%2041.5,26.1715729%2041.5,27%20C41.5,27.8284271%2040.8284271,28.5%2040,28.5%20L29.999,28.5%20L30,38.5%20C30,39.3284271%2029.3284271,40%2028.5,40%20C27.6715729,40%2027,39.3284271%2027,38.5%20L26.999,28.5%20L17,28.5%20C16.1715729,28.5%2015.5,27.8284271%2015.5,27%20C15.5,26.1715729%2016.1715729,25.5%2017,25.5%20L26.999,25.5%20L27,15.5%20C27,14.6715729%2027.6715729,14%2028.5,14%20Z'%20id='path-7'%3e%3c/path%3e%3cfilter%20x='-21.2%25'%20y='-21.2%25'%20width='150.0%25'%20height='150.0%25'%20filterUnits='objectBoundingBox'%20id='filter-8'%3e%3cfeOffset%20dx='1'%20dy='1'%20in='SourceAlpha'%20result='shadowOffsetOuter1'%3e%3c/feOffset%3e%3cfeGaussianBlur%20stdDeviation='2'%20in='shadowOffsetOuter1'%20result='shadowBlurOuter1'%3e%3c/feGaussianBlur%3e%3cfeColorMatrix%20values='0%200%200%200%200.0683595728%200%200%200%200%200.139337499%200%200%200%200%200.259564828%200%200%200%200.318650466%200'%20type='matrix'%20in='shadowBlurOuter1'%3e%3c/feColorMatrix%3e%3c/filter%3e%3c/defs%3e%3cg%20id='2024.6'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球-窄'%20transform='translate(-292.000000,%20-681.000000)'%3e%3cg%20id='Group-5'%20transform='translate(304.000000,%20681.000000)'%3e%3cg%20id='Oval'%3e%3cuse%20fill='black'%20fill-opacity='1'%20filter='url(%23filter-3)'%20xlink:href='%23path-2'%3e%3c/use%3e%3cuse%20fill='url(%23linearGradient-1)'%20fill-rule='evenodd'%20xlink:href='%23path-2'%3e%3c/use%3e%3c/g%3e%3cg%20id='Oval'%3e%3cuse%20fill-opacity='0'%20fill='url(%23linearGradient-4)'%20fill-rule='evenodd'%20xlink:href='%23path-5'%3e%3c/use%3e%3cuse%20fill='black'%20fill-opacity='1'%20filter='url(%23filter-6)'%20xlink:href='%23path-5'%3e%3c/use%3e%3c/g%3e%3cg%20id='Combined-Shape'%20transform='translate(28.500000,%2027.000000)%20rotate(-315.000000)%20translate(-28.500000,%20-27.000000)%20'%3e%3cuse%20fill='black'%20fill-opacity='1'%20filter='url(%23filter-8)'%20xlink:href='%23path-7'%3e%3c/use%3e%3cuse%20fill='%23FFFFFF'%20fill-rule='evenodd'%20xlink:href='%23path-7'%3e%3c/use%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Nr="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='12px'%20height='12px'%20viewBox='0%200%2012%2012'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3eGroup%203%3c/title%3e%3cg%20id='2024.6'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球-宽'%20transform='translate(-125.000000,%20-478.000000)'%3e%3cg%20id='Group-8'%20transform='translate(111.000000,%20462.000000)'%3e%3cg%20id='Group-7'%20transform='translate(4.000000,%200.000000)'%3e%3cg%20id='Group-3'%20transform='translate(10.000000,%2016.000000)'%3e%3ccircle%20id='Oval'%20stroke-opacity='0.375467794'%20stroke='%231677FF'%20cx='6'%20cy='6'%20r='5.5'%3e%3c/circle%3e%3ccircle%20id='Oval'%20fill='%231677FF'%20cx='6'%20cy='6'%20r='2'%3e%3c/circle%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Tr="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='4px'%20height='8px'%20viewBox='0%200%204%208'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3eSlice%3c/title%3e%3cg%20id='2024.6'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球-宽'%20transform='translate(-343.000000,%20-524.000000)'%20fill='%23979DAB'%3e%3cg%20id='Group-8'%20transform='translate(111.000000,%20462.000000)'%3e%3cg%20id='Group-6'%20transform='translate(4.000000,%2044.000000)'%3e%3cg%20id='表单/箭头'%20transform='translate(228.000000,%2018.000000)'%3e%3cpath%20d='M3.14270359,3.99821979%20L0.0972842295,7.34653822%20C-0.0324280765,7.4913906%20-0.0324280765,7.72352582%200.0972842295,7.86775917%20L0.119278925,7.89190124%20C0.250119164,8.03551556%200.462170586,8.03613459%200.593010825,7.89313929%20C0.593574791,7.89252026%200.593574791,7.89190124%200.594138758,7.89190124%20L3.86514473,4.30154315%20L3.87980786,4.2866865%20L3.90180256,4.26254443%20C4.02361933,4.12759649%204.03320677,3.91403209%203.92436122,3.76732263%20C3.91702966,3.75679917%203.90913413,3.74813278%203.90123859,3.73884737%20L3.8792439,3.71470531%20L0.59865049,0.108252516%20C0.467810251,-0.0359808345%200.255758829,-0.0359808345%200.12491859,0.107633489%20C0.12491859,0.107633489%200.124354624,0.108252516%200.124354624,0.108252516%20L0.102359928,0.132394579%20C-0.0273523776,0.27662793%20-0.0273523776,0.508763151%200.102359928,0.653615529%20L3.14270359,3.99821979%20Z'%20id='Fill-1'%20transform='translate(2.000000,%204.000000)%20rotate(-360.000000)%20translate(-2.000000,%20-4.000000)%20'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Hr="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='14px'%20height='14px'%20viewBox='0%200%2014%2014'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e06%20icon/16/1/电话备份%3c/title%3e%3cg%20id='武汉理工'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球-窄'%20transform='translate(-195,%20-591)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组'%20transform='translate(183,%20454)'%3e%3cg%20id='Group-6-Copy备份'%20transform='translate(0,%20120)'%3e%3cg%20id='编组-4'%20transform='translate(12,%2016)'%3e%3cg%20id='06-icon/16/1/电话备份'%20transform='translate(1.3125,%202.3125)'%3e%3cg%20id='编组'%3e%3cpath%20d='M10.1417277,7.82623832%20L11.2741705,8.38017612%20C11.336015,8.41060126%2011.375,8.47225802%2011.375,8.53964306%20C11.375,8.60702811%2011.336015,8.66868487%2011.2741705,8.69911001%20L5.89125814,11.3364859%20C5.78756933,11.387838%205.66486269,11.387838%205.56117388,11.3364859%20L0.171151956,8.69911001%20C0.135908482,8.68213001%200.107365953,8.65438089%200.0899004558,8.6201171%20C0.0442460992,8.53204151%200.0806089994,8.4246595%200.171151956,8.38017612%20L1.30461049,7.82327608%20L5.56117388,9.90671418%20C5.66509435,9.95711928%205.78733767,9.95711928%205.89125814,9.90671418%20L10.1417277,7.82623832%20Z%20M5.49109445,0.0385244271%20C5.59513786,-0.0128414757%205.71815094,-0.0128414757%205.82219435,0.0385244271%20L11.2051068,2.68281221%20C11.2669512,2.71323734%2011.3059362,2.7748941%2011.3059362,2.84227916%20C11.3059362,2.90966421%2011.2669512,2.97132097%2011.2051068,3.0017461%20L5.82219435,5.63912201%20C5.71803445,5.69001254%205.59525436,5.69001254%205.49109445,5.63912201%20L0.102088182,3.0017461%20C0.0395877333,2.97184078%200,2.91000272%200,2.84227916%20C0,2.77455559%200.0395877333,2.71271753%200.102088182,2.68281221%20L5.49109445,0.0385244271%20Z'%20id='形状'%3e%3c/path%3e%3c/g%3e%3cpath%20d='M10.1417277,4.96274519%20L11.2741705,5.52359487%20C11.336015,5.55402001%2011.375,5.61567677%2011.375,5.68306182%20C11.375,5.75044687%2011.336015,5.81210363%2011.2741705,5.84252876%20L5.89125814,8.47595503%20C5.78756933,8.52730713%205.66486269,8.52730713%205.56117388,8.47595503%20L0.171151956,5.83857912%20C0.13590848,5.82159912%200.107365951,5.79385%200.0899004558,5.7595862%20C0.0469480694,5.67250044%200.0829297221,5.56799335%200.171151956,5.52359487%20L1.30461049,4.96669483%20L5.56117388,7.05013294%20C5.66509435,7.10053804%205.78733767,7.10053804%205.89125814,7.05013294%20L10.1417277,4.96274519%20Z'%20id='路径'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",zr="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='14px'%20height='14px'%20viewBox='0%200%2014%2014'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e06%20icon/16/1/电话%3c/title%3e%3cg%20id='武汉理工'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球-窄'%20transform='translate(-195,%20-615)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组'%20transform='translate(183,%20454)'%3e%3cg%20id='Group-6-Copy备份'%20transform='translate(0,%20120)'%3e%3cg%20id='编组-2'%20transform='translate(12,%2040)'%3e%3cg%20id='编组'%20transform='translate(0.4375,%202.75)'%3e%3cpath%20d='M2.20463191,9.53095157%20C2.02611517,9.62019991%201.8633499,9.63463714%201.70846037,9.51651434%20C1.56144658,9.41217254%201.54635142,9.2566442%201.56144658,9.07158515%20C1.62313987,8.70146706%201.6703943,8.33134898%201.70911668,7.96057465%20C1.702343,7.86919041%201.66379945,7.78305758%201.60016896,7.71711045%20C0.988486588,7.13109015%200.462124715,6.48666468%200.198943778,5.67227364%20C-0.297884075,4.05727256%200.151033034,2.67326716%201.37571041,1.51632001%20C3.61373284,-0.579703338%207.52534975,-0.483892627%209.66361282,1.68694183%20C10.4216527,2.4567087%2010.8410358,3.36756669%2010.8561309,4.42673441%20C10.6627946,4.3434164%2010.4543183,4.30096618%2010.2437922,4.30204923%20L10.2129456,4.30204923%20C9.48903394,4.32961122%208.89441567,4.85722636%208.81172041,5.54561976%20C8.79619475,5.66850411%208.79619475,5.79285349%208.81172041,5.91573784%20C8.54000742,6.17495175%208.20725746,6.33769871%207.79706274,6.40463496%20L7.78853069,6.40463496%20C7.25429307,6.51291419%206.83294105,6.90534436%206.70561661,7.41327237%20C6.57986708,7.92587724%206.77134722,8.46412878%207.19259979,8.78218431%20C6.72622001,8.91806368%206.24470507,8.99522086%205.75921534,9.01186752%20C5.1373697,9.04041315%204.51455943,8.98299738%203.90841673,8.8412457%20C3.80678465,8.82919096%203.70374562,8.8394937%203.60651341,8.87143264%20L2.20528822,9.53029533%20L2.20463191,9.53095157%20Z%20M10.0501803,6.38363536%20C9.69249053,6.30160564%209.45621837,5.97676795%209.50084755,5.62764948%20C9.55374605,5.25565905%209.86811333,4.97659228%2010.2437922,4.96813054%20C10.5850743,4.96813054%2010.9020727,5.18337652%2010.9722981,5.52396391%20C11.0641817,6.03451688%2011.2978286,6.47944607%2011.6771767,6.84956415%20C11.7060544,6.87778238%2011.7047418,6.93750001%2011.7034292,6.99656141%20C11.7034292,7.03199825%2011.7021165,7.06677885%2011.7080233,7.09434083%20C11.6811146,7.09434083%2011.6522369,7.09630954%2011.6233591,7.0989345%20C11.5537901,7.10549687%2011.4855337,7.11074678%2011.4527181,7.07924736%20C11.064838,6.69403581%2010.5844179,6.4938833%2010.0501803,6.38363536%20L10.0501803,6.38363536%20Z%20M7.92766874,7.0641539%20C8.44746662,6.984734%208.93110588,6.75003293%209.31511138,6.39085397%20C9.35186483,6.35476089%209.4352164,6.35738585%209.50609804,6.3600108%20L9.5546651,6.36132327%20C9.55008065,6.39171977%209.54876052,6.42251933%209.55072723,6.45319656%20C9.55203985,6.50832053%209.55335248,6.56213202%209.52381846,6.59035024%20C9.13659464,6.96046833%208.92723125,7.40539752%208.81893984,7.91660672%20C8.74149507,8.28672481%208.3851179,8.50131455%208.01364555,8.44159692%20C7.64086058,8.38253552%207.36258448,8.07147883%207.36258448,7.72367283%20C7.36258448,7.41261614%207.60345083,7.12387153%207.92766874,7.0641539%20L7.92766874,7.0641539%20Z%20M10.9565466,8.89374472%20C11.3182831,8.5460118%2011.5596874,8.09199297%2011.6456737,7.59767518%20C11.717868,7.23543195%2012.0683384,6.98737408%2012.4516243,7.02674834%20C12.8388482,7.07268499%2013.125,7.36799197%2013.125,7.7387663%20C13.125,8.09444715%2012.9005414,8.38253552%2012.5290691,8.44947177%20C12.0735889,8.52887654%2011.6627378,8.71327934%2011.3214558,8.99677405%20L11.1967566,9.1089907%20C11.1193119,9.18314557%2011.0031447,9.24942558%2010.9257,9.1385214%20C10.894197,9.07946%2010.9106048,8.93836889%2010.9565466,8.89374472%20L10.9565466,8.89374472%20Z%20M10.3455205,10.4943086%20C9.98914335,10.5382765%209.6170147,10.3236868%209.54022625,9.98244314%20C9.43193484,9.45745294%209.19894423,8.99808652%208.79662525,8.61287497%20C8.76774754,8.58465675%208.76840385,8.53740763%208.77037278,8.48753356%20C8.77209643,8.45746319%208.77033455,8.42729439%208.76512229,8.39762899%20L8.94626428,8.38450423%20C8.97382937,8.38450423%208.99483134,8.38778542%209.00533232,8.39762899%20C9.36761631,8.74477876%209.80603243,8.95280612%2010.2910467,9.08339743%20L10.4538119,9.12342793%20C10.795094,9.18248933%2010.9729544,9.47976503%2010.9880496,9.78294687%20C10.9880496,10.1386277%2010.7176492,10.4418096%2010.3448642,10.4943086%20L10.3455205,10.4943086%20Z'%20id='形状'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Mr="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='14px'%20height='14px'%20viewBox='0%200%2014%2014'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e06%20icon/16/1/电话%3c/title%3e%3cg%20id='武汉理工'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球-窄'%20transform='translate(-195,%20-639)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组'%20transform='translate(183,%20454)'%3e%3cg%20id='Group-6-Copy备份'%20transform='translate(0,%20120)'%3e%3cg%20id='编组-3'%20transform='translate(12,%2064)'%3e%3cg%20id='06-icon/16/1/电话'%20transform='translate(1.1184,%202.4159)'%3e%3cpath%20d='M1.63595444,1.15250051%20C2.14800621,0.965058973%202.71714819,1.17685118%203.39865271,1.85251913%20C4.33112462,2.77948721%204.34971586,3.44082816%203.56979553,4.36106211%20C3.38417891,4.57567705%203.31359172,4.65845609%203.26193399,4.72641666%20L3.22005032,4.78376882%20C2.71853962,5.48125904%202.90717914,5.75858762%204.79802067,7.34602877%20C6.38064225,8.67435726%206.77115257,8.92803901%207.15984534,8.76587128%20C7.33787383,8.67683074%207.48814626,8.57616978%207.62938526,8.45470438%20L7.6671676,8.42543129%20C7.70585063,8.39521564%207.77301825,8.34244944%207.87764032,8.26023256%20C8.01612412,8.15159037%208.10382906,8.08345845%208.12587773,8.06763529%20C8.79351129,7.5853292%209.3545683,7.70605157%2010.2600872,8.60954697%20C10.9215432,9.26793462%2011.0989141,9.80252576%2010.8572736,10.2492659%20C10.6777595,10.5819651%2010.3187878,10.8319999%209.57788361,11.1392132%20C8.09188806,11.7554697%205.25447318,10.696034%202.93346052,8.48344052%20C0.58590581,6.24730704%20-0.392837689,3.4867767%200.504130165,2.23883687%20C1.01313609,1.53021707%201.26561311,1.28732312%201.63595444,1.15250051%20Z'%20id='形状结合'%20transform='translate(5.5677,%206.1999)%20rotate(2)%20translate(-5.5677,%20-6.1999)'%3e%3c/path%3e%3cpath%20d='M10.7516934,5.65396323%20C10.7516934,2.94771192%208.55784321,0.753861764%205.85159189,0.753861764%20C5.78751364,0.753861764%205.72532005,0.756311815%205.66312645,0.7585734%20L5.66312645,0.00471163603%20C5.72532005,0.00263851617%205.78751364,0%205.85159189,0%20C8.97418956,0%2011.5055551,2.53136556%2011.5055551,5.65396323%20L10.7516934,5.65396323%20L10.7516934,5.65396323%20Z%20M9.41358873,5.65396323%20L8.65218835,5.65396323%20C8.46355112,4.14664495%207.18220261,3.01556809%205.66312645,3.01544706%20L5.66312645,2.26158529%20C7.59891277,2.26158529%209.2200101,3.72788017%209.41358873,5.65396323%20L9.41358873,5.65396323%20Z'%20id='形状'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Or="data:image/png;base64,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",Zr="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='18px'%20height='18px'%20viewBox='0%200%2018%2018'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e03图标Icon/叉%3c/title%3e%3cg%20id='武汉理工'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='悬浮球_1'%20transform='translate(-349,%20-673)'%3e%3cg%20id='Group-5'%20transform='translate(349,%20673)'%3e%3ccircle%20id='Oval'%20fill-opacity='0'%20fill='%23000000'%20cx='9'%20cy='9'%20r='9'%3e%3c/circle%3e%3ccircle%20id='Oval'%20fill-opacity='0.26'%20fill='%23000000'%20cx='9'%20cy='9'%20r='8'%3e%3c/circle%3e%3cpath%20d='M9.0625,5%20C9.30412458,5%209.5,5.19587542%209.5,5.4375%20L9.5,8.499%20L12.5,8.5%20C12.7761424,8.5%2013,8.72385763%2013,9%20C13,9.27614237%2012.7761424,9.5%2012.5,9.5%20L9.5,9.499%20L9.5,12.5625%20C9.5,12.8041246%209.30412458,13%209.0625,13%20L8.9375,13%20C8.69587542,13%208.5,12.8041246%208.5,12.5625%20L8.5,9.499%20L5.5,9.5%20C5.22385763,9.5%205,9.27614237%205,9%20C5,8.72385763%205.22385763,8.5%205.5,8.5%20L8.5,8.499%20L8.5,5.4375%20C8.5,5.19587542%208.69587542,5%208.9375,5%20L9.0625,5%20Z'%20id='Combined-Shape'%20fill='%23FFFFFF'%20transform='translate(9,%209)%20rotate(-315)%20translate(-9,%20-9)'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Gr=`.floating-ball {\r
  width: 80px;\r
  height: 80px;\r
  position: fixed;\r
  right: 0px;\r
  bottom: 45px;\r
  z-index: 9999;\r
  cursor: pointer;\r
  transition: all 0.2s ease 0s;\r
}\r
.floating-ball .icon {\r
  width: 100%;\r
  height: 100%;\r
  object-fit: cover;\r
}\r
.floating-ball .floating-panel {\r
  position: absolute;\r
  bottom: 91px;\r
  right: 11px;\r
  background: rgb(255, 255, 255);\r
  /* padding-left: 4px; */\r
  font-size: 12px;\r
  color: #333;\r
  display: flex;\r
  /* border-bottom: 1px solid #E8EAEF; */\r
  border-radius: 8px;\r
  flex-direction: column;\r
  min-width: 178px;\r
  max-width: 250px;\r
  transition: opacity 0.2s ease-in-out;\r
}\r
.floating-ball .floating-panel .event-item {\r
  font-size: 12px;\r
  padding: 14px 12px 14px 10px;\r
  color: #333;\r
  display:flex;\r
  border-bottom: 1px solid #E8EAEF;\r
  align-items: center;\r
  cursor: auto;\r
}\r
.floating-ball .floating-panel .event-item .icon_radio {\r
  width:12px;\r
  height:12px;\r
  margin-top: 2px;\r
  flex-shrink: 0;\r
}\r
.floating-ball .floating-panel .event-item .event-item__text {\r
  font-size: 12px;\r
  color: #0F1D34;\r
  line-height: 16px;\r
  margin-left:6px;\r
  margin-right:18px;\r
  white-space: pre-line;\r
  flex: 1;\r
  min-width: 0;\r
  word-break: break-all;\r
}\r
.floating-ball .floating-panel .event-item .icon_arrow {\r
  margin-left: auto;\r
  flex-shrink: 0;\r
  align-self: center;\r
}\r
.floating-ball .floating-panel .event-item.no_border {\r
  border-bottom: none;\r
}\r
.floating-ball .floating-panel .arrow {\r
  position: absolute;\r
  width: 12px;\r
  height: 12px;\r
  background: #fff;\r
  right:24px;\r
  bottom:-5px;\r
  transform: rotate(45deg);\r
}\r
.floating-ball-mask {\r
  position: fixed;\r
  inset: 0px;\r
  z-index: 9990;\r
  background: rgba(0, 0, 0, 0.39);\r
  transition: opacity 0.2s ease-in-out;\r
}\r
.hidden {\r
  opacity: 0;\r
  visibility: hidden;\r
  pointer-events: none;\r
} \r
.blue{\r
  background-color: #197DFF !important;\r
}\r
.attr-panel{\r
  background: linear-gradient( 135deg, #31AFFF 0%, #1677FF 100%);\r
  border-radius: 0px 0px 8px 8px;\r
  padding: 10px 0;\r
  position: relative;\r
  white-space: nowrap;\r
}\r
.attr-panel::after{\r
  content: '';\r
  display: block;\r
  width: 83px;\r
  height: 54px;\r
  background-image: var(--bg-image-url);\r
  background-size: 100% 100%;\r
  position: absolute;\r
  top: 0;\r
  right: 0;\r
}\r
.attr-item{\r
  font-size: 12px;\r
  padding: 4px 12px 4px 10px;\r
  color: #333;\r
  display:flex;\r
  align-items: center;\r
  cursor: auto;\r
  position: relative;\r
  z-index: 1;\r
}\r
.attr-item img{\r
  width: 14px;\r
  height: 14px;\r
  margin-right: 6px;\r
}\r
.attr-item .attr-item__text{\r
  font-size: 12px;\r
  color: #fff;\r
  flex-shrink: 0;\r
  overflow: hidden;\r
  text-overflow: ellipsis;\r
}\r
.attr-item .attr-item__text_right{\r
  font-size: 12px;\r
  color: #fff;\r
  margin-left: auto;\r
  overflow: hidden;\r
  text-overflow: ellipsis;\r
}\r
.text3{\r
  flex-shrink: 1 !important;\r
}\r
.value3{\r
  flex-shrink: 0;\r
}`;function tt(r,n){return function(){return r.apply(n,arguments)}}const{toString:jr}=Object.prototype,{getPrototypeOf:re}=Object,{iterator:M0,toStringTag:rt}=Symbol,O0=(r=>n=>{const t=jr.call(n);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),s0=r=>(r=r.toLowerCase(),n=>O0(n)===r),Z0=r=>n=>typeof n===r,{isArray:B0}=Array,w0=Z0("undefined");function Ur(r){return r!==null&&!w0(r)&&r.constructor!==null&&!w0(r.constructor)&&$(r.constructor.isBuffer)&&r.constructor.isBuffer(r)}const nt=s0("ArrayBuffer");function Wr(r){let n;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?n=ArrayBuffer.isView(r):n=r&&r.buffer&&nt(r.buffer),n}const qr=Z0("string"),$=Z0("function"),it=Z0("number"),G0=r=>r!==null&&typeof r=="object",Xr=r=>r===!0||r===!1,j0=r=>{if(O0(r)!=="object")return!1;const n=re(r);return(n===null||n===Object.prototype||Object.getPrototypeOf(n)===null)&&!(rt in r)&&!(M0 in r)},Jr=s0("Date"),Vr=s0("File"),Yr=s0("Blob"),Qr=s0("FileList"),Kr=r=>G0(r)&&$(r.pipe),$r=r=>{let n;return r&&(typeof FormData=="function"&&r instanceof FormData||$(r.append)&&((n=O0(r))==="formdata"||n==="object"&&$(r.toString)&&r.toString()==="[object FormData]"))},en=s0("URLSearchParams"),[tn,rn,nn,an]=["ReadableStream","Request","Response","Headers"].map(s0),sn=r=>r.trim?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function _0(r,n,{allOwnKeys:t=!1}={}){if(r===null||typeof r>"u")return;let e,i;if(typeof r!="object"&&(r=[r]),B0(r))for(e=0,i=r.length;e<i;e++)n.call(null,r[e],e,r);else{const s=t?Object.getOwnPropertyNames(r):Object.keys(r),c=s.length;let x;for(e=0;e<c;e++)x=s[e],n.call(null,r[x],x,r)}}function at(r,n){n=n.toLowerCase();const t=Object.keys(r);let e=t.length,i;for(;e-- >0;)if(i=t[e],n===i.toLowerCase())return i;return null}const p0=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,st=r=>!w0(r)&&r!==p0;function ne(){const{caseless:r}=st(this)&&this||{},n={},t=(e,i)=>{const s=r&&at(n,i)||i;j0(n[s])&&j0(e)?n[s]=ne(n[s],e):j0(e)?n[s]=ne({},e):B0(e)?n[s]=e.slice():n[s]=e};for(let e=0,i=arguments.length;e<i;e++)arguments[e]&&_0(arguments[e],t);return n}const on=(r,n,t,{allOwnKeys:e}={})=>(_0(n,(i,s)=>{t&&$(i)?r[s]=tt(i,t):r[s]=i},{allOwnKeys:e}),r),cn=r=>(r.charCodeAt(0)===65279&&(r=r.slice(1)),r),xn=(r,n,t,e)=>{r.prototype=Object.create(n.prototype,e),r.prototype.constructor=r,Object.defineProperty(r,"super",{value:n.prototype}),t&&Object.assign(r.prototype,t)},ln=(r,n,t,e)=>{let i,s,c;const x={};if(n=n||{},r==null)return n;do{for(i=Object.getOwnPropertyNames(r),s=i.length;s-- >0;)c=i[s],(!e||e(c,r,n))&&!x[c]&&(n[c]=r[c],x[c]=!0);r=t!==!1&&re(r)}while(r&&(!t||t(r,n))&&r!==Object.prototype);return n},fn=(r,n,t)=>{r=String(r),(t===void 0||t>r.length)&&(t=r.length),t-=n.length;const e=r.indexOf(n,t);return e!==-1&&e===t},dn=r=>{if(!r)return null;if(B0(r))return r;let n=r.length;if(!it(n))return null;const t=new Array(n);for(;n-- >0;)t[n]=r[n];return t},un=(r=>n=>r&&n instanceof r)(typeof Uint8Array<"u"&&re(Uint8Array)),hn=(r,n)=>{const e=(r&&r[M0]).call(r);let i;for(;(i=e.next())&&!i.done;){const s=i.value;n.call(r,s[0],s[1])}},pn=(r,n)=>{let t;const e=[];for(;(t=r.exec(n))!==null;)e.push(t);return e},Cn=s0("HTMLFormElement"),gn=r=>r.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,e,i){return e.toUpperCase()+i}),ot=(({hasOwnProperty:r})=>(n,t)=>r.call(n,t))(Object.prototype),vn=s0("RegExp"),ct=(r,n)=>{const t=Object.getOwnPropertyDescriptors(r),e={};_0(t,(i,s)=>{let c;(c=n(i,s,r))!==!1&&(e[s]=c||i)}),Object.defineProperties(r,e)},An=r=>{ct(r,(n,t)=>{if($(r)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const e=r[t];if($(e)){if(n.enumerable=!1,"writable"in n){n.writable=!1;return}n.set||(n.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},En=(r,n)=>{const t={},e=i=>{i.forEach(s=>{t[s]=!0})};return B0(r)?e(r):e(String(r).split(n)),t},mn=()=>{},Bn=(r,n)=>r!=null&&Number.isFinite(r=+r)?r:n;function bn(r){return!!(r&&$(r.append)&&r[rt]==="FormData"&&r[M0])}const Dn=r=>{const n=new Array(10),t=(e,i)=>{if(G0(e)){if(n.indexOf(e)>=0)return;if(!("toJSON"in e)){n[i]=e;const s=B0(e)?[]:{};return _0(e,(c,x)=>{const p=t(c,i+1);!w0(p)&&(s[x]=p)}),n[i]=void 0,s}}return e};return t(r,0)},In=s0("AsyncFunction"),yn=r=>r&&(G0(r)||$(r))&&$(r.then)&&$(r.catch),xt=((r,n)=>r?setImmediate:n?((t,e)=>(p0.addEventListener("message",({source:i,data:s})=>{i===p0&&s===t&&e.length&&e.shift()()},!1),i=>{e.push(i),p0.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",$(p0.postMessage)),Fn=typeof queueMicrotask<"u"?queueMicrotask.bind(p0):typeof process<"u"&&process.nextTick||xt,y={isArray:B0,isArrayBuffer:nt,isBuffer:Ur,isFormData:$r,isArrayBufferView:Wr,isString:qr,isNumber:it,isBoolean:Xr,isObject:G0,isPlainObject:j0,isReadableStream:tn,isRequest:rn,isResponse:nn,isHeaders:an,isUndefined:w0,isDate:Jr,isFile:Vr,isBlob:Yr,isRegExp:vn,isFunction:$,isStream:Kr,isURLSearchParams:en,isTypedArray:un,isFileList:Qr,forEach:_0,merge:ne,extend:on,trim:sn,stripBOM:cn,inherits:xn,toFlatObject:ln,kindOf:O0,kindOfTest:s0,endsWith:fn,toArray:dn,forEachEntry:hn,matchAll:pn,isHTMLForm:Cn,hasOwnProperty:ot,hasOwnProp:ot,reduceDescriptors:ct,freezeMethods:An,toObjectSet:En,toCamelCase:gn,noop:mn,toFiniteNumber:Bn,findKey:at,global:p0,isContextDefined:st,isSpecCompliantForm:bn,toJSONObject:Dn,isAsyncFn:In,isThenable:yn,setImmediate:xt,asap:Fn,isIterable:r=>r!=null&&$(r[M0])};function H(r,n,t,e,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=r,this.name="AxiosError",n&&(this.code=n),t&&(this.config=t),e&&(this.request=e),i&&(this.response=i,this.status=i.status?i.status:null)}y.inherits(H,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const lt=H.prototype,ft={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(r=>{ft[r]={value:r}}),Object.defineProperties(H,ft),Object.defineProperty(lt,"isAxiosError",{value:!0}),H.from=(r,n,t,e,i,s)=>{const c=Object.create(lt);return y.toFlatObject(r,c,function(p){return p!==Error.prototype},x=>x!=="isAxiosError"),H.call(c,r.message,n,t,e,i),c.cause=r,c.name=r.name,s&&Object.assign(c,s),c};const wn=null;function ie(r){return y.isPlainObject(r)||y.isArray(r)}function dt(r){return y.endsWith(r,"[]")?r.slice(0,-2):r}function ut(r,n,t){return r?r.concat(n).map(function(i,s){return i=dt(i),!t&&s?"["+i+"]":i}).join(t?".":""):n}function _n(r){return y.isArray(r)&&!r.some(ie)}const Sn=y.toFlatObject(y,{},null,function(n){return/^is[A-Z]/.test(n)});function U0(r,n,t){if(!y.isObject(r))throw new TypeError("target must be an object");n=n||new FormData,t=y.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(C,d){return!y.isUndefined(d[C])});const e=t.metaTokens,i=t.visitor||o,s=t.dots,c=t.indexes,p=(t.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(n);if(!y.isFunction(i))throw new TypeError("visitor must be a function");function a(f){if(f===null)return"";if(y.isDate(f))return f.toISOString();if(y.isBoolean(f))return f.toString();if(!p&&y.isBlob(f))throw new H("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(f)||y.isTypedArray(f)?p&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function o(f,C,d){let B=f;if(f&&!d&&typeof f=="object"){if(y.endsWith(C,"{}"))C=e?C:C.slice(0,-2),f=JSON.stringify(f);else if(y.isArray(f)&&_n(f)||(y.isFileList(f)||y.endsWith(C,"[]"))&&(B=y.toArray(f)))return C=dt(C),B.forEach(function(h,A){!(y.isUndefined(h)||h===null)&&n.append(c===!0?ut([C],A,s):c===null?C:C+"[]",a(h))}),!1}return ie(f)?!0:(n.append(ut(d,C,s),a(f)),!1)}const v=[],l=Object.assign(Sn,{defaultVisitor:o,convertValue:a,isVisitable:ie});function g(f,C){if(!y.isUndefined(f)){if(v.indexOf(f)!==-1)throw Error("Circular reference detected in "+C.join("."));v.push(f),y.forEach(f,function(B,u){(!(y.isUndefined(B)||B===null)&&i.call(n,B,y.isString(u)?u.trim():u,C,l))===!0&&g(B,C?C.concat(u):[u])}),v.pop()}}if(!y.isObject(r))throw new TypeError("data must be an object");return g(r),n}function ht(r){const n={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g,function(e){return n[e]})}function ae(r,n){this._pairs=[],r&&U0(r,this,n)}const pt=ae.prototype;pt.append=function(n,t){this._pairs.push([n,t])},pt.toString=function(n){const t=n?function(e){return n.call(this,e,ht)}:ht;return this._pairs.map(function(i){return t(i[0])+"="+t(i[1])},"").join("&")};function kn(r){return encodeURIComponent(r).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ct(r,n,t){if(!n)return r;const e=t&&t.encode||kn;y.isFunction(t)&&(t={serialize:t});const i=t&&t.serialize;let s;if(i?s=i(n,t):s=y.isURLSearchParams(n)?n.toString():new ae(n,t).toString(e),s){const c=r.indexOf("#");c!==-1&&(r=r.slice(0,c)),r+=(r.indexOf("?")===-1?"?":"&")+s}return r}class gt{constructor(){this.handlers=[]}use(n,t,e){return this.handlers.push({fulfilled:n,rejected:t,synchronous:e?e.synchronous:!1,runWhen:e?e.runWhen:null}),this.handlers.length-1}eject(n){this.handlers[n]&&(this.handlers[n]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(n){y.forEach(this.handlers,function(e){e!==null&&n(e)})}}const vt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Rn={isBrowser:!0,classes:{URLSearchParams:typeof URLSearchParams<"u"?URLSearchParams:ae,FormData:typeof FormData<"u"?FormData:null,Blob:typeof Blob<"u"?Blob:null},protocols:["http","https","file","blob","url","data"]},se=typeof window<"u"&&typeof document<"u",oe=typeof navigator=="object"&&navigator||void 0,Ln=se&&(!oe||["ReactNative","NativeScript","NS"].indexOf(oe.product)<0),Pn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Nn=se&&window.location.href||"http://localhost",Q={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:se,hasStandardBrowserEnv:Ln,hasStandardBrowserWebWorkerEnv:Pn,navigator:oe,origin:Nn},Symbol.toStringTag,{value:"Module"})),...Rn};function Tn(r,n){return U0(r,new Q.classes.URLSearchParams,Object.assign({visitor:function(t,e,i,s){return Q.isNode&&y.isBuffer(t)?(this.append(e,t.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},n))}function Hn(r){return y.matchAll(/\w+|\[(\w*)]/g,r).map(n=>n[0]==="[]"?"":n[1]||n[0])}function zn(r){const n={},t=Object.keys(r);let e;const i=t.length;let s;for(e=0;e<i;e++)s=t[e],n[s]=r[s];return n}function At(r){function n(t,e,i,s){let c=t[s++];if(c==="__proto__")return!0;const x=Number.isFinite(+c),p=s>=t.length;return c=!c&&y.isArray(i)?i.length:c,p?(y.hasOwnProp(i,c)?i[c]=[i[c],e]:i[c]=e,!x):((!i[c]||!y.isObject(i[c]))&&(i[c]=[]),n(t,e,i[c],s)&&y.isArray(i[c])&&(i[c]=zn(i[c])),!x)}if(y.isFormData(r)&&y.isFunction(r.entries)){const t={};return y.forEachEntry(r,(e,i)=>{n(Hn(e),i,t,0)}),t}return null}function Mn(r,n,t){if(y.isString(r))try{return(n||JSON.parse)(r),y.trim(r)}catch(e){if(e.name!=="SyntaxError")throw e}return(t||JSON.stringify)(r)}const S0={transitional:vt,adapter:["xhr","http","fetch"],transformRequest:[function(n,t){const e=t.getContentType()||"",i=e.indexOf("application/json")>-1,s=y.isObject(n);if(s&&y.isHTMLForm(n)&&(n=new FormData(n)),y.isFormData(n))return i?JSON.stringify(At(n)):n;if(y.isArrayBuffer(n)||y.isBuffer(n)||y.isStream(n)||y.isFile(n)||y.isBlob(n)||y.isReadableStream(n))return n;if(y.isArrayBufferView(n))return n.buffer;if(y.isURLSearchParams(n))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),n.toString();let x;if(s){if(e.indexOf("application/x-www-form-urlencoded")>-1)return Tn(n,this.formSerializer).toString();if((x=y.isFileList(n))||e.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return U0(x?{"files[]":n}:n,p&&new p,this.formSerializer)}}return s||i?(t.setContentType("application/json",!1),Mn(n)):n}],transformResponse:[function(n){const t=this.transitional||S0.transitional,e=t&&t.forcedJSONParsing,i=this.responseType==="json";if(y.isResponse(n)||y.isReadableStream(n))return n;if(n&&y.isString(n)&&(e&&!this.responseType||i)){const c=!(t&&t.silentJSONParsing)&&i;try{return JSON.parse(n)}catch(x){if(c)throw x.name==="SyntaxError"?H.from(x,H.ERR_BAD_RESPONSE,this,null,this.response):x}}return n}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Q.classes.FormData,Blob:Q.classes.Blob},validateStatus:function(n){return n>=200&&n<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],r=>{S0.headers[r]={}});const On=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Zn=r=>{const n={};let t,e,i;return r&&r.split(`
`).forEach(function(c){i=c.indexOf(":"),t=c.substring(0,i).trim().toLowerCase(),e=c.substring(i+1).trim(),!(!t||n[t]&&On[t])&&(t==="set-cookie"?n[t]?n[t].push(e):n[t]=[e]:n[t]=n[t]?n[t]+", "+e:e)}),n},Et=Symbol("internals");function k0(r){return r&&String(r).trim().toLowerCase()}function W0(r){return r===!1||r==null?r:y.isArray(r)?r.map(W0):String(r)}function Gn(r){const n=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let e;for(;e=t.exec(r);)n[e[1]]=e[2];return n}const jn=r=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());function ce(r,n,t,e,i){if(y.isFunction(e))return e.call(this,n,t);if(i&&(n=t),!!y.isString(n)){if(y.isString(e))return n.indexOf(e)!==-1;if(y.isRegExp(e))return e.test(n)}}function Un(r){return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(n,t,e)=>t.toUpperCase()+e)}function Wn(r,n){const t=y.toCamelCase(" "+n);["get","set","has"].forEach(e=>{Object.defineProperty(r,e+t,{value:function(i,s,c){return this[e].call(this,n,i,s,c)},configurable:!0})})}let e0=class{constructor(n){n&&this.set(n)}set(n,t,e){const i=this;function s(x,p,a){const o=k0(p);if(!o)throw new Error("header name must be a non-empty string");const v=y.findKey(i,o);(!v||i[v]===void 0||a===!0||a===void 0&&i[v]!==!1)&&(i[v||p]=W0(x))}const c=(x,p)=>y.forEach(x,(a,o)=>s(a,o,p));if(y.isPlainObject(n)||n instanceof this.constructor)c(n,t);else if(y.isString(n)&&(n=n.trim())&&!jn(n))c(Zn(n),t);else if(y.isObject(n)&&y.isIterable(n)){let x={},p,a;for(const o of n){if(!y.isArray(o))throw TypeError("Object iterator must return a key-value pair");x[a=o[0]]=(p=x[a])?y.isArray(p)?[...p,o[1]]:[p,o[1]]:o[1]}c(x,t)}else n!=null&&s(t,n,e);return this}get(n,t){if(n=k0(n),n){const e=y.findKey(this,n);if(e){const i=this[e];if(!t)return i;if(t===!0)return Gn(i);if(y.isFunction(t))return t.call(this,i,e);if(y.isRegExp(t))return t.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(n,t){if(n=k0(n),n){const e=y.findKey(this,n);return!!(e&&this[e]!==void 0&&(!t||ce(this,this[e],e,t)))}return!1}delete(n,t){const e=this;let i=!1;function s(c){if(c=k0(c),c){const x=y.findKey(e,c);x&&(!t||ce(e,e[x],x,t))&&(delete e[x],i=!0)}}return y.isArray(n)?n.forEach(s):s(n),i}clear(n){const t=Object.keys(this);let e=t.length,i=!1;for(;e--;){const s=t[e];(!n||ce(this,this[s],s,n,!0))&&(delete this[s],i=!0)}return i}normalize(n){const t=this,e={};return y.forEach(this,(i,s)=>{const c=y.findKey(e,s);if(c){t[c]=W0(i),delete t[s];return}const x=n?Un(s):String(s).trim();x!==s&&delete t[s],t[x]=W0(i),e[x]=!0}),this}concat(...n){return this.constructor.concat(this,...n)}toJSON(n){const t=Object.create(null);return y.forEach(this,(e,i)=>{e!=null&&e!==!1&&(t[i]=n&&y.isArray(e)?e.join(", "):e)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([n,t])=>n+": "+t).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(n){return n instanceof this?n:new this(n)}static concat(n,...t){const e=new this(n);return t.forEach(i=>e.set(i)),e}static accessor(n){const e=(this[Et]=this[Et]={accessors:{}}).accessors,i=this.prototype;function s(c){const x=k0(c);e[x]||(Wn(i,c),e[x]=!0)}return y.isArray(n)?n.forEach(s):s(n),this}};e0.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),y.reduceDescriptors(e0.prototype,({value:r},n)=>{let t=n[0].toUpperCase()+n.slice(1);return{get:()=>r,set(e){this[t]=e}}}),y.freezeMethods(e0);function xe(r,n){const t=this||S0,e=n||t,i=e0.from(e.headers);let s=e.data;return y.forEach(r,function(x){s=x.call(t,s,i.normalize(),n?n.status:void 0)}),i.normalize(),s}function mt(r){return!!(r&&r.__CANCEL__)}function b0(r,n,t){H.call(this,r??"canceled",H.ERR_CANCELED,n,t),this.name="CanceledError"}y.inherits(b0,H,{__CANCEL__:!0});function Bt(r,n,t){const e=t.config.validateStatus;!t.status||!e||e(t.status)?r(t):n(new H("Request failed with status code "+t.status,[H.ERR_BAD_REQUEST,H.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function qn(r){const n=/^([-+\w]{1,25})(:?\/\/|:)/.exec(r);return n&&n[1]||""}function Xn(r,n){r=r||10;const t=new Array(r),e=new Array(r);let i=0,s=0,c;return n=n!==void 0?n:1e3,function(p){const a=Date.now(),o=e[s];c||(c=a),t[i]=p,e[i]=a;let v=s,l=0;for(;v!==i;)l+=t[v++],v=v%r;if(i=(i+1)%r,i===s&&(s=(s+1)%r),a-c<n)return;const g=o&&a-o;return g?Math.round(l*1e3/g):void 0}}function Jn(r,n){let t=0,e=1e3/n,i,s;const c=(a,o=Date.now())=>{t=o,i=null,s&&(clearTimeout(s),s=null),r.apply(null,a)};return[(...a)=>{const o=Date.now(),v=o-t;v>=e?c(a,o):(i=a,s||(s=setTimeout(()=>{s=null,c(i)},e-v)))},()=>i&&c(i)]}const q0=(r,n,t=3)=>{let e=0;const i=Xn(50,250);return Jn(s=>{const c=s.loaded,x=s.lengthComputable?s.total:void 0,p=c-e,a=i(p),o=c<=x;e=c;const v={loaded:c,total:x,progress:x?c/x:void 0,bytes:p,rate:a||void 0,estimated:a&&x&&o?(x-c)/a:void 0,event:s,lengthComputable:x!=null,[n?"download":"upload"]:!0};r(v)},t)},bt=(r,n)=>{const t=r!=null;return[e=>n[0]({lengthComputable:t,total:r,loaded:e}),n[1]]},Dt=r=>(...n)=>y.asap(()=>r(...n)),Vn=Q.hasStandardBrowserEnv?((r,n)=>t=>(t=new URL(t,Q.origin),r.protocol===t.protocol&&r.host===t.host&&(n||r.port===t.port)))(new URL(Q.origin),Q.navigator&&/(msie|trident)/i.test(Q.navigator.userAgent)):()=>!0,Yn=Q.hasStandardBrowserEnv?{write(r,n,t,e,i,s){const c=[r+"="+encodeURIComponent(n)];y.isNumber(t)&&c.push("expires="+new Date(t).toGMTString()),y.isString(e)&&c.push("path="+e),y.isString(i)&&c.push("domain="+i),s===!0&&c.push("secure"),document.cookie=c.join("; ")},read(r){const n=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return n?decodeURIComponent(n[3]):null},remove(r){this.write(r,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Qn(r){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)}function Kn(r,n){return n?r.replace(/\/?\/$/,"")+"/"+n.replace(/^\/+/,""):r}function It(r,n,t){let e=!Qn(n);return r&&(e||t==!1)?Kn(r,n):n}const yt=r=>r instanceof e0?{...r}:r;function C0(r,n){n=n||{};const t={};function e(a,o,v,l){return y.isPlainObject(a)&&y.isPlainObject(o)?y.merge.call({caseless:l},a,o):y.isPlainObject(o)?y.merge({},o):y.isArray(o)?o.slice():o}function i(a,o,v,l){if(y.isUndefined(o)){if(!y.isUndefined(a))return e(void 0,a,v,l)}else return e(a,o,v,l)}function s(a,o){if(!y.isUndefined(o))return e(void 0,o)}function c(a,o){if(y.isUndefined(o)){if(!y.isUndefined(a))return e(void 0,a)}else return e(void 0,o)}function x(a,o,v){if(v in n)return e(a,o);if(v in r)return e(void 0,a)}const p={url:s,method:s,data:s,baseURL:c,transformRequest:c,transformResponse:c,paramsSerializer:c,timeout:c,timeoutMessage:c,withCredentials:c,withXSRFToken:c,adapter:c,responseType:c,xsrfCookieName:c,xsrfHeaderName:c,onUploadProgress:c,onDownloadProgress:c,decompress:c,maxContentLength:c,maxBodyLength:c,beforeRedirect:c,transport:c,httpAgent:c,httpsAgent:c,cancelToken:c,socketPath:c,responseEncoding:c,validateStatus:x,headers:(a,o,v)=>i(yt(a),yt(o),v,!0)};return y.forEach(Object.keys(Object.assign({},r,n)),function(o){const v=p[o]||i,l=v(r[o],n[o],o);y.isUndefined(l)&&v!==x||(t[o]=l)}),t}const Ft=r=>{const n=C0({},r);let{data:t,withXSRFToken:e,xsrfHeaderName:i,xsrfCookieName:s,headers:c,auth:x}=n;n.headers=c=e0.from(c),n.url=Ct(It(n.baseURL,n.url,n.allowAbsoluteUrls),r.params,r.paramsSerializer),x&&c.set("Authorization","Basic "+btoa((x.username||"")+":"+(x.password?unescape(encodeURIComponent(x.password)):"")));let p;if(y.isFormData(t)){if(Q.hasStandardBrowserEnv||Q.hasStandardBrowserWebWorkerEnv)c.setContentType(void 0);else if((p=c.getContentType())!==!1){const[a,...o]=p?p.split(";").map(v=>v.trim()).filter(Boolean):[];c.setContentType([a||"multipart/form-data",...o].join("; "))}}if(Q.hasStandardBrowserEnv&&(e&&y.isFunction(e)&&(e=e(n)),e||e!==!1&&Vn(n.url))){const a=i&&s&&Yn.read(s);a&&c.set(i,a)}return n},$n=typeof XMLHttpRequest<"u"&&function(r){return new Promise(function(t,e){const i=Ft(r);let s=i.data;const c=e0.from(i.headers).normalize();let{responseType:x,onUploadProgress:p,onDownloadProgress:a}=i,o,v,l,g,f;function C(){g&&g(),f&&f(),i.cancelToken&&i.cancelToken.unsubscribe(o),i.signal&&i.signal.removeEventListener("abort",o)}let d=new XMLHttpRequest;d.open(i.method.toUpperCase(),i.url,!0),d.timeout=i.timeout;function B(){if(!d)return;const h=e0.from("getAllResponseHeaders"in d&&d.getAllResponseHeaders()),m={data:!x||x==="text"||x==="json"?d.responseText:d.response,status:d.status,statusText:d.statusText,headers:h,config:r,request:d};Bt(function(D){t(D),C()},function(D){e(D),C()},m),d=null}"onloadend"in d?d.onloadend=B:d.onreadystatechange=function(){!d||d.readyState!==4||d.status===0&&!(d.responseURL&&d.responseURL.indexOf("file:")===0)||setTimeout(B)},d.onabort=function(){d&&(e(new H("Request aborted",H.ECONNABORTED,r,d)),d=null)},d.onerror=function(){e(new H("Network Error",H.ERR_NETWORK,r,d)),d=null},d.ontimeout=function(){let A=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const m=i.transitional||vt;i.timeoutErrorMessage&&(A=i.timeoutErrorMessage),e(new H(A,m.clarifyTimeoutError?H.ETIMEDOUT:H.ECONNABORTED,r,d)),d=null},s===void 0&&c.setContentType(null),"setRequestHeader"in d&&y.forEach(c.toJSON(),function(A,m){d.setRequestHeader(m,A)}),y.isUndefined(i.withCredentials)||(d.withCredentials=!!i.withCredentials),x&&x!=="json"&&(d.responseType=i.responseType),a&&([l,f]=q0(a,!0),d.addEventListener("progress",l)),p&&d.upload&&([v,g]=q0(p),d.upload.addEventListener("progress",v),d.upload.addEventListener("loadend",g)),(i.cancelToken||i.signal)&&(o=h=>{d&&(e(!h||h.type?new b0(null,r,d):h),d.abort(),d=null)},i.cancelToken&&i.cancelToken.subscribe(o),i.signal&&(i.signal.aborted?o():i.signal.addEventListener("abort",o)));const u=qn(i.url);if(u&&Q.protocols.indexOf(u)===-1){e(new H("Unsupported protocol "+u+":",H.ERR_BAD_REQUEST,r));return}d.send(s||null)})},ei=(r,n)=>{const{length:t}=r=r?r.filter(Boolean):[];if(n||t){let e=new AbortController,i;const s=function(a){if(!i){i=!0,x();const o=a instanceof Error?a:this.reason;e.abort(o instanceof H?o:new b0(o instanceof Error?o.message:o))}};let c=n&&setTimeout(()=>{c=null,s(new H(`timeout ${n} of ms exceeded`,H.ETIMEDOUT))},n);const x=()=>{r&&(c&&clearTimeout(c),c=null,r.forEach(a=>{a.unsubscribe?a.unsubscribe(s):a.removeEventListener("abort",s)}),r=null)};r.forEach(a=>a.addEventListener("abort",s));const{signal:p}=e;return p.unsubscribe=()=>y.asap(x),p}},ti=function*(r,n){let t=r.byteLength;if(t<n){yield r;return}let e=0,i;for(;e<t;)i=e+n,yield r.slice(e,i),e=i},ri=async function*(r,n){for await(const t of ni(r))yield*ti(t,n)},ni=async function*(r){if(r[Symbol.asyncIterator]){yield*r;return}const n=r.getReader();try{for(;;){const{done:t,value:e}=await n.read();if(t)break;yield e}}finally{await n.cancel()}},wt=(r,n,t,e)=>{const i=ri(r,n);let s=0,c,x=p=>{c||(c=!0,e&&e(p))};return new ReadableStream({async pull(p){try{const{done:a,value:o}=await i.next();if(a){x(),p.close();return}let v=o.byteLength;if(t){let l=s+=v;t(l)}p.enqueue(new Uint8Array(o))}catch(a){throw x(a),a}},cancel(p){return x(p),i.return()}},{highWaterMark:2})},X0=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",_t=X0&&typeof ReadableStream=="function",ii=X0&&(typeof TextEncoder=="function"?(r=>n=>r.encode(n))(new TextEncoder):async r=>new Uint8Array(await new Response(r).arrayBuffer())),St=(r,...n)=>{try{return!!r(...n)}catch{return!1}},ai=_t&&St(()=>{let r=!1;const n=new Request(Q.origin,{body:new ReadableStream,method:"POST",get duplex(){return r=!0,"half"}}).headers.has("Content-Type");return r&&!n}),kt=64*1024,le=_t&&St(()=>y.isReadableStream(new Response("").body)),J0={stream:le&&(r=>r.body)};X0&&(r=>{["text","arrayBuffer","blob","formData","stream"].forEach(n=>{!J0[n]&&(J0[n]=y.isFunction(r[n])?t=>t[n]():(t,e)=>{throw new H(`Response type '${n}' is not supported`,H.ERR_NOT_SUPPORT,e)})})})(new Response);const si=async r=>{if(r==null)return 0;if(y.isBlob(r))return r.size;if(y.isSpecCompliantForm(r))return(await new Request(Q.origin,{method:"POST",body:r}).arrayBuffer()).byteLength;if(y.isArrayBufferView(r)||y.isArrayBuffer(r))return r.byteLength;if(y.isURLSearchParams(r)&&(r=r+""),y.isString(r))return(await ii(r)).byteLength},oi=async(r,n)=>{const t=y.toFiniteNumber(r.getContentLength());return t??si(n)},fe={http:wn,xhr:$n,fetch:X0&&(async r=>{let{url:n,method:t,data:e,signal:i,cancelToken:s,timeout:c,onDownloadProgress:x,onUploadProgress:p,responseType:a,headers:o,withCredentials:v="same-origin",fetchOptions:l}=Ft(r);a=a?(a+"").toLowerCase():"text";let g=ei([i,s&&s.toAbortSignal()],c),f;const C=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let d;try{if(p&&ai&&t!=="get"&&t!=="head"&&(d=await oi(o,e))!==0){let m=new Request(n,{method:"POST",body:e,duplex:"half"}),E;if(y.isFormData(e)&&(E=m.headers.get("content-type"))&&o.setContentType(E),m.body){const[D,F]=bt(d,q0(Dt(p)));e=wt(m.body,kt,D,F)}}y.isString(v)||(v=v?"include":"omit");const B="credentials"in Request.prototype;f=new Request(n,{...l,signal:g,method:t.toUpperCase(),headers:o.normalize().toJSON(),body:e,duplex:"half",credentials:B?v:void 0});let u=await fetch(f,l);const h=le&&(a==="stream"||a==="response");if(le&&(x||h&&C)){const m={};["status","statusText","headers"].forEach(P=>{m[P]=u[P]});const E=y.toFiniteNumber(u.headers.get("content-length")),[D,F]=x&&bt(E,q0(Dt(x),!0))||[];u=new Response(wt(u.body,kt,D,()=>{F&&F(),C&&C()}),m)}a=a||"text";let A=await J0[y.findKey(J0,a)||"text"](u,r);return!h&&C&&C(),await new Promise((m,E)=>{Bt(m,E,{data:A,headers:e0.from(u.headers),status:u.status,statusText:u.statusText,config:r,request:f})})}catch(B){throw C&&C(),B&&B.name==="TypeError"&&/Load failed|fetch/i.test(B.message)?Object.assign(new H("Network Error",H.ERR_NETWORK,r,f),{cause:B.cause||B}):H.from(B,B&&B.code,r,f)}})};y.forEach(fe,(r,n)=>{if(r){try{Object.defineProperty(r,"name",{value:n})}catch{}Object.defineProperty(r,"adapterName",{value:n})}});const Rt=r=>`- ${r}`,ci=r=>y.isFunction(r)||r===null||r===!1,Lt={getAdapter:r=>{r=y.isArray(r)?r:[r];const{length:n}=r;let t,e;const i={};for(let s=0;s<n;s++){t=r[s];let c;if(e=t,!ci(t)&&(e=fe[(c=String(t)).toLowerCase()],e===void 0))throw new H(`Unknown adapter '${c}'`);if(e)break;i[c||"#"+s]=e}if(!e){const s=Object.entries(i).map(([x,p])=>`adapter ${x} `+(p===!1?"is not supported by the environment":"is not available in the build"));let c=n?s.length>1?`since :
`+s.map(Rt).join(`
`):" "+Rt(s[0]):"as no adapter specified";throw new H("There is no suitable adapter to dispatch the request "+c,"ERR_NOT_SUPPORT")}return e},adapters:fe};function de(r){if(r.cancelToken&&r.cancelToken.throwIfRequested(),r.signal&&r.signal.aborted)throw new b0(null,r)}function Pt(r){return de(r),r.headers=e0.from(r.headers),r.data=xe.call(r,r.transformRequest),["post","put","patch"].indexOf(r.method)!==-1&&r.headers.setContentType("application/x-www-form-urlencoded",!1),Lt.getAdapter(r.adapter||S0.adapter)(r).then(function(e){return de(r),e.data=xe.call(r,r.transformResponse,e),e.headers=e0.from(e.headers),e},function(e){return mt(e)||(de(r),e&&e.response&&(e.response.data=xe.call(r,r.transformResponse,e.response),e.response.headers=e0.from(e.response.headers))),Promise.reject(e)})}const Nt="1.10.0",V0={};["object","boolean","number","function","string","symbol"].forEach((r,n)=>{V0[r]=function(e){return typeof e===r||"a"+(n<1?"n ":" ")+r}});const Tt={};V0.transitional=function(n,t,e){function i(s,c){return"[Axios v"+Nt+"] Transitional option '"+s+"'"+c+(e?". "+e:"")}return(s,c,x)=>{if(n===!1)throw new H(i(c," has been removed"+(t?" in "+t:"")),H.ERR_DEPRECATED);return t&&!Tt[c]&&(Tt[c]=!0,console.warn(i(c," has been deprecated since v"+t+" and will be removed in the near future"))),n?n(s,c,x):!0}},V0.spelling=function(n){return(t,e)=>(console.warn(`${e} is likely a misspelling of ${n}`),!0)};function xi(r,n,t){if(typeof r!="object")throw new H("options must be an object",H.ERR_BAD_OPTION_VALUE);const e=Object.keys(r);let i=e.length;for(;i-- >0;){const s=e[i],c=n[s];if(c){const x=r[s],p=x===void 0||c(x,s,r);if(p!==!0)throw new H("option "+s+" must be "+p,H.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new H("Unknown option "+s,H.ERR_BAD_OPTION)}}const Y0={assertOptions:xi,validators:V0},x0=Y0.validators;let g0=class{constructor(n){this.defaults=n||{},this.interceptors={request:new gt,response:new gt}}async request(n,t){try{return await this._request(n,t)}catch(e){if(e instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{e.stack?s&&!String(e.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(e.stack+=`
`+s):e.stack=s}catch{}}throw e}}_request(n,t){typeof n=="string"?(t=t||{},t.url=n):t=n||{},t=C0(this.defaults,t);const{transitional:e,paramsSerializer:i,headers:s}=t;e!==void 0&&Y0.assertOptions(e,{silentJSONParsing:x0.transitional(x0.boolean),forcedJSONParsing:x0.transitional(x0.boolean),clarifyTimeoutError:x0.transitional(x0.boolean)},!1),i!=null&&(y.isFunction(i)?t.paramsSerializer={serialize:i}:Y0.assertOptions(i,{encode:x0.function,serialize:x0.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Y0.assertOptions(t,{baseUrl:x0.spelling("baseURL"),withXsrfToken:x0.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let c=s&&y.merge(s.common,s[t.method]);s&&y.forEach(["delete","get","head","post","put","patch","common"],f=>{delete s[f]}),t.headers=e0.concat(c,s);const x=[];let p=!0;this.interceptors.request.forEach(function(C){typeof C.runWhen=="function"&&C.runWhen(t)===!1||(p=p&&C.synchronous,x.unshift(C.fulfilled,C.rejected))});const a=[];this.interceptors.response.forEach(function(C){a.push(C.fulfilled,C.rejected)});let o,v=0,l;if(!p){const f=[Pt.bind(this),void 0];for(f.unshift.apply(f,x),f.push.apply(f,a),l=f.length,o=Promise.resolve(t);v<l;)o=o.then(f[v++],f[v++]);return o}l=x.length;let g=t;for(v=0;v<l;){const f=x[v++],C=x[v++];try{g=f(g)}catch(d){C.call(this,d);break}}try{o=Pt.call(this,g)}catch(f){return Promise.reject(f)}for(v=0,l=a.length;v<l;)o=o.then(a[v++],a[v++]);return o}getUri(n){n=C0(this.defaults,n);const t=It(n.baseURL,n.url,n.allowAbsoluteUrls);return Ct(t,n.params,n.paramsSerializer)}};y.forEach(["delete","get","head","options"],function(n){g0.prototype[n]=function(t,e){return this.request(C0(e||{},{method:n,url:t,data:(e||{}).data}))}}),y.forEach(["post","put","patch"],function(n){function t(e){return function(s,c,x){return this.request(C0(x||{},{method:n,headers:e?{"Content-Type":"multipart/form-data"}:{},url:s,data:c}))}}g0.prototype[n]=t(),g0.prototype[n+"Form"]=t(!0)});let li=class Pr{constructor(n){if(typeof n!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(s){t=s});const e=this;this.promise.then(i=>{if(!e._listeners)return;let s=e._listeners.length;for(;s-- >0;)e._listeners[s](i);e._listeners=null}),this.promise.then=i=>{let s;const c=new Promise(x=>{e.subscribe(x),s=x}).then(i);return c.cancel=function(){e.unsubscribe(s)},c},n(function(s,c,x){e.reason||(e.reason=new b0(s,c,x),t(e.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(n){if(this.reason){n(this.reason);return}this._listeners?this._listeners.push(n):this._listeners=[n]}unsubscribe(n){if(!this._listeners)return;const t=this._listeners.indexOf(n);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const n=new AbortController,t=e=>{n.abort(e)};return this.subscribe(t),n.signal.unsubscribe=()=>this.unsubscribe(t),n.signal}static source(){let n;return{token:new Pr(function(i){n=i}),cancel:n}}};function fi(r){return function(t){return r.apply(null,t)}}function di(r){return y.isObject(r)&&r.isAxiosError===!0}const ue={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ue).forEach(([r,n])=>{ue[n]=r});function Ht(r){const n=new g0(r),t=tt(g0.prototype.request,n);return y.extend(t,g0.prototype,n,{allOwnKeys:!0}),y.extend(t,n,null,{allOwnKeys:!0}),t.create=function(i){return Ht(C0(r,i))},t}const V=Ht(S0);V.Axios=g0,V.CanceledError=b0,V.CancelToken=li,V.isCancel=mt,V.VERSION=Nt,V.toFormData=U0,V.AxiosError=H,V.Cancel=V.CanceledError,V.all=function(n){return Promise.all(n)},V.spread=fi,V.isAxiosError=di,V.mergeConfig=C0,V.AxiosHeaders=e0,V.formToJSON=r=>At(y.isHTMLForm(r)?new FormData(r):r),V.getAdapter=Lt.getAdapter,V.HttpStatusCode=ue,V.default=V;const{Axios:Ba,AxiosError:ba,CanceledError:Da,isCancel:Ia,CancelToken:ya,VERSION:Fa,all:wa,Cancel:_a,isAxiosError:Sa,spread:ka,toFormData:Ra,AxiosHeaders:La,HttpStatusCode:Pa,formToJSON:Na,getAdapter:Ta,mergeConfig:Ha}=V;let ui=class{constructor(n){G(this,"service");G(this,"baseURL","");this.baseURL=n||"",this.service=V.create({baseURL:this.baseURL}),this.service.interceptors.request.use(t=>{const e=localStorage.getItem("float-token");return e&&(t.headers.Authorization=`Bearer ${e}`),t}),this.service.interceptors.response.use(t=>{const{data:e}=t;return e})}updateBaseURL(n){this.baseURL=n,this.service.defaults.baseURL=n}updateToken(n){localStorage.setItem("float-token",n)}clearToken(){localStorage.removeItem("float-token")}};const l0=new ui,hi=(r,n)=>l0.service({method:"post",url:`/common-service/app-sdk-config/set/integrated?appId=${r}&userId=${n}`}),pi=r=>l0.service({method:"get",url:`/common-service/app-sdk-config/get-by-appid?appId=${r}`}),Ci=r=>l0.service({method:"post",url:"/common-service/app-sdk-config/getConditonProperty",data:r}),gi=r=>l0.service({method:"get",url:`/common-service/system/config/getSysConfigVoByKey?configKey=${r}`}),vi=r=>l0.service({method:"post",url:"/common-service/wbAppSdkComment/save",data:r}),Ai=r=>l0.service({method:"get",url:`/common-service/wbAppSdkComment/getUserLastComment?appId=${r}`}),Ei=r=>l0.service({method:"get",url:"/base-service/base-api/base/authentication/getTicket",params:r});class mi{constructor(n){G(this,"appId");G(this,"onSubmitSuccess");G(this,"onClose");G(this,"modalContainer",null);G(this,"currentRating",0);this.appId=n.appId,this.onSubmitSuccess=n.onSubmitSuccess,this.onClose=n.onClose}async show(){if(!this.appId){this.showToast("缺少应用ID，无法显示评分");return}let n=null,t=!1;try{const e=await Ai(this.appId);e&&e.data&&e.data.score&&(n=e.data,t=!0)}catch(e){console.warn("获取评分数据失败，继续显示评分界面:",e)}this.currentRating=n?n.score:0,this.createModal(t,n)}createModal(n,t){const e=`
      <div class="score-modal-overlay">
        <div class="score-modal">
          <div class="score-modal-header">
            <h3 class="score-modal-title">${n?"我的评分":"应用评分"}</h3>
          </div>
          <div class="score-modal-body">
            <div class="score-rating-section">
              <label class="score-label">评分：</label>
              <div class="score-stars">
                <span class="star" data-rating="1">★</span>
                <span class="star" data-rating="2">★</span>
                <span class="star" data-rating="3">★</span>
                <span class="star" data-rating="4">★</span>
                <span class="star" data-rating="5">★</span>
              </div>
            </div>
            <div class="score-comment-section">
              <label class="score-label">评价：</label>
              <textarea class="score-textarea" placeholder="${n?"":"请输入您的评价..."}" ${n?"readonly":""} maxlength="300"></textarea>
            </div>
          </div>
          <div class="score-modal-footer">
            ${n?'<button class="score-cancel-btn">关闭</button>':'<button class="score-cancel-btn">取消</button><button class="score-submit-btn">确认</button>'}
          </div>
        </div>
      </div>
    `;this.modalContainer=document.createElement("div"),this.modalContainer.innerHTML=this.getModalStyles()+e,document.body.appendChild(this.modalContainer),this.initializeModalElements(n,t)}initializeModalElements(n,t){if(!this.modalContainer)return;const e=this.modalContainer.querySelector(".score-modal-overlay"),i=this.modalContainer.querySelector(".score-cancel-btn"),s=this.modalContainer.querySelectorAll(".star"),c=this.modalContainer.querySelector(".score-textarea"),x=this.modalContainer.querySelector(".score-submit-btn");n&&t&&(c.value=t.content||"",s.forEach(p=>{p.classList.add("readonly")})),this.updateStars(s,this.currentRating),this.bindEvents(e,i,s,c,x,n)}bindEvents(n,t,e,i,s,c){t.addEventListener("click",()=>this.closeModal()),n.addEventListener("click",p=>{p.target===n&&this.closeModal()});const x=p=>{p.key==="Escape"&&(this.closeModal(),document.removeEventListener("keydown",x))};document.addEventListener("keydown",x),c||this.bindStarEvents(e),!c&&s&&this.bindSubmitEvent(s,i)}bindStarEvents(n){n.forEach((t,e)=>{t.addEventListener("click",i=>{const s=e+1,c=t.getBoundingClientRect(),x=i.clientX-c.left,p=c.width;let a;x<p/2?a=s-.5:a=s,this.currentRating===a?this.currentRating=0:this.currentRating=a,this.updateStars(n,this.currentRating)}),t.addEventListener("mouseenter",i=>{const s=e+1,c=t.getBoundingClientRect(),x=i.clientX-c.left,p=c.width;let a;x<p/2?a=s-.5:a=s,this.updateStars(n,a)}),t.addEventListener("mousemove",i=>{const s=e+1,c=t.getBoundingClientRect(),x=i.clientX-c.left,p=c.width;let a;x<p/2?a=s-.5:a=s,this.updateStars(n,a)})}),this.modalContainer&&this.modalContainer.querySelector(".score-stars").addEventListener("mouseleave",()=>{this.updateStars(n,this.currentRating)})}bindSubmitEvent(n,t){n.addEventListener("click",async()=>{var i;const e=t.value.trim();if(!this.appId){this.showToast("缺少应用ID，无法提交评分");return}try{n.disabled=!0,n.textContent="确认中...",await vi({appId:this.appId,content:e,score:this.currentRating}),(i=this.onSubmitSuccess)==null||i.call(this,{score:this.currentRating,content:e}),this.closeModal(),this.showToast("提交成功，感谢评价！")}catch(s){console.error("评分提交失败:",s);let c="评分提交失败，请重试";s instanceof Error&&(c=s.message),this.showToast(c)}finally{n.disabled=!1,n.textContent="确认"}})}updateStars(n,t){n.forEach((e,i)=>{const s=i+1;e.classList.remove("star-full","star-half","star-empty"),t>=s?e.classList.add("star-full"):t>=s-.5?e.classList.add("star-half"):e.classList.add("star-empty")})}closeModal(){var n;this.modalContainer&&document.body.contains(this.modalContainer)&&(document.body.removeChild(this.modalContainer),this.modalContainer=null),(n=this.onClose)==null||n.call(this)}showToast(n){const t=document.createElement("div");t.style.cssText=`
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 10001;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      min-width: 120px;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      animation: toast-fade-in 0.3s ease-out;
    `,t.textContent=n;const e=document.createElement("style");e.textContent=`
      @keyframes toast-fade-in {
        0% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.8);
        }
        100% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
      
      @keyframes toast-fade-out {
        0% {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
        100% {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.8);
        }
      }
    `,document.head.appendChild(e),document.body.appendChild(t),setTimeout(()=>{document.body.contains(t)&&(t.style.animation="toast-fade-out 0.3s ease-in",setTimeout(()=>{document.body.contains(t)&&document.body.removeChild(t),document.head.contains(e)&&document.head.removeChild(e)},300))},2e3)}getModalStyles(){return`
      <style>
        .score-modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
        }
        
        .score-modal {
          background: white;
          border-radius: 12px;
          width: 350px;
          max-width: 90vw;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          overflow: hidden;
        }
        
        .score-modal-header {
          padding: 24px 24px 0 24px;
          text-align: center;
        }
        
        .score-modal-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #000000;
          line-height: 22px;
        }
        
        .score-modal-body {
          padding: 12px 24px;
        }

        .score-rating-section{
          display: flex;
          align-items: center;
        }
        
        .score-comment-section {
          margin-bottom: 0;
        }
        
        .score-label {
          display: block;
          font-size: 14px;
          color: #000000;
          font-weight: 500;
        }
        
        .score-stars {
          display: flex;
          justify-content: center;
          gap: 8px;
          margin-bottom: 4px;
        }
        
        .star {
          font-size: 28px;
          color: #ddd;
          cursor: pointer;
          transition: all 0.2s;
          user-select: none;
          position: relative;
          display: inline-block;
        }
        
        .star:hover {
          color: #ffc107;
          transform: scale(1.1);
        }
        
        .star.star-full {
          color: #ffc107;
        }
        
        .star.star-half {
          color: #ddd;
          position: relative;
        }
        
        .star.star-half::before {
          content: '★';
          position: absolute;
          left: 0;
          color: #ffc107;
          width: 50%;
          overflow: hidden;
        }
        
        .star.star-empty {
          color: #ddd;
        }
        
        .score-textarea {
          width: 100%;
          min-height: 150px;
          padding: 12px 16px;
          border: 1px solid #e5e5e5;
          border-radius: 8px;
          font-size: 14px;
          font-family: inherit;
          resize: vertical;
          box-sizing: border-box;
          background: #fafafa;
          transition: all 0.2s;
          margin-top: 12px;
        }
        
        .score-textarea:focus {
          outline: none;
          border-color: #1677ff;
          background: white;
          box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        }
        
        .score-modal-footer {
          display: flex;
          border-top: 1px solid #f0f0f0;
        }
        
        .score-cancel-btn,
        .score-submit-btn {
          flex: 1;
          padding: 14px 16px;
          border: none;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.2s;
          background: transparent;
        }
        
        .score-cancel-btn {
          color: #666;
          border-right: 1px solid #f0f0f0;
        }
        
        .score-cancel-btn:hover {
          background: #f5f5f5;
          color: #333;
        }
        
        .score-submit-btn {
          color: #1677ff;
          font-weight: 500;
        }
        
        .score-submit-btn:hover {
          background: #f0f8ff;
          color: #0958d9;
        }
        
        .score-submit-btn:disabled {
          color: #ccc;
          cursor: not-allowed;
          background: transparent;
        }
        
        .score-textarea:read-only {
          background: #f9f9f9;
          color: #666;
          cursor: default;
        }
        
        .star.readonly {
          cursor: default;
        }
        
        .star.readonly:hover {
          transform: none;
        }
        
        /* 当只有一个按钮时的样式 */
        .score-modal-footer .score-cancel-btn:only-child {
          border-right: none;
        }
      </style>
    `}}var z=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Bi(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}function bi(r){if(r.__esModule)return r;var n=r.default;if(typeof n=="function"){var t=function e(){return this instanceof e?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};t.prototype=n.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(r).forEach(function(e){var i=Object.getOwnPropertyDescriptor(r,e);Object.defineProperty(t,e,i.get?i:{enumerable:!0,get:function(){return r[e]}})}),t}var zt={exports:{}};function Di(r){throw new Error('Could not dynamically require "'+r+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var he={exports:{}};const Ii=bi(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var Mt;function Z(){return Mt||(Mt=1,function(r,n){(function(t,e){r.exports=e()})(z,function(){var t=t||function(e,i){var s;if(typeof window<"u"&&window.crypto&&(s=window.crypto),typeof self<"u"&&self.crypto&&(s=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(s=globalThis.crypto),!s&&typeof window<"u"&&window.msCrypto&&(s=window.msCrypto),!s&&typeof z<"u"&&z.crypto&&(s=z.crypto),!s&&typeof Di=="function")try{s=Ii}catch{}var c=function(){if(s){if(typeof s.getRandomValues=="function")try{return s.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof s.randomBytes=="function")try{return s.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},x=Object.create||function(){function u(){}return function(h){var A;return u.prototype=h,A=new u,u.prototype=null,A}}(),p={},a=p.lib={},o=a.Base=function(){return{extend:function(u){var h=x(this);return u&&h.mixIn(u),(!h.hasOwnProperty("init")||this.init===h.init)&&(h.init=function(){h.$super.init.apply(this,arguments)}),h.init.prototype=h,h.$super=this,h},create:function(){var u=this.extend();return u.init.apply(u,arguments),u},init:function(){},mixIn:function(u){for(var h in u)u.hasOwnProperty(h)&&(this[h]=u[h]);u.hasOwnProperty("toString")&&(this.toString=u.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),v=a.WordArray=o.extend({init:function(u,h){u=this.words=u||[],h!=i?this.sigBytes=h:this.sigBytes=u.length*4},toString:function(u){return(u||g).stringify(this)},concat:function(u){var h=this.words,A=u.words,m=this.sigBytes,E=u.sigBytes;if(this.clamp(),m%4)for(var D=0;D<E;D++){var F=A[D>>>2]>>>24-D%4*8&255;h[m+D>>>2]|=F<<24-(m+D)%4*8}else for(var P=0;P<E;P+=4)h[m+P>>>2]=A[P>>>2];return this.sigBytes+=E,this},clamp:function(){var u=this.words,h=this.sigBytes;u[h>>>2]&=4294967295<<32-h%4*8,u.length=e.ceil(h/4)},clone:function(){var u=o.clone.call(this);return u.words=this.words.slice(0),u},random:function(u){for(var h=[],A=0;A<u;A+=4)h.push(c());return new v.init(h,u)}}),l=p.enc={},g=l.Hex={stringify:function(u){for(var h=u.words,A=u.sigBytes,m=[],E=0;E<A;E++){var D=h[E>>>2]>>>24-E%4*8&255;m.push((D>>>4).toString(16)),m.push((D&15).toString(16))}return m.join("")},parse:function(u){for(var h=u.length,A=[],m=0;m<h;m+=2)A[m>>>3]|=parseInt(u.substr(m,2),16)<<24-m%8*4;return new v.init(A,h/2)}},f=l.Latin1={stringify:function(u){for(var h=u.words,A=u.sigBytes,m=[],E=0;E<A;E++){var D=h[E>>>2]>>>24-E%4*8&255;m.push(String.fromCharCode(D))}return m.join("")},parse:function(u){for(var h=u.length,A=[],m=0;m<h;m++)A[m>>>2]|=(u.charCodeAt(m)&255)<<24-m%4*8;return new v.init(A,h)}},C=l.Utf8={stringify:function(u){try{return decodeURIComponent(escape(f.stringify(u)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(u){return f.parse(unescape(encodeURIComponent(u)))}},d=a.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new v.init,this._nDataBytes=0},_append:function(u){typeof u=="string"&&(u=C.parse(u)),this._data.concat(u),this._nDataBytes+=u.sigBytes},_process:function(u){var h,A=this._data,m=A.words,E=A.sigBytes,D=this.blockSize,F=D*4,P=E/F;u?P=e.ceil(P):P=e.max((P|0)-this._minBufferSize,0);var b=P*D,I=e.min(b*4,E);if(b){for(var _=0;_<b;_+=D)this._doProcessBlock(m,_);h=m.splice(0,b),A.sigBytes-=I}return new v.init(h,I)},clone:function(){var u=o.clone.call(this);return u._data=this._data.clone(),u},_minBufferSize:0});a.Hasher=d.extend({cfg:o.extend(),init:function(u){this.cfg=this.cfg.extend(u),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(u){return this._append(u),this._process(),this},finalize:function(u){u&&this._append(u);var h=this._doFinalize();return h},blockSize:16,_createHelper:function(u){return function(h,A){return new u.init(A).finalize(h)}},_createHmacHelper:function(u){return function(h,A){return new B.HMAC.init(u,A).finalize(h)}}});var B=p.algo={};return p}(Math);return t})}(he)),he.exports}var pe={exports:{}},Ot;function Q0(){return Ot||(Ot=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(e){var i=t,s=i.lib,c=s.Base,x=s.WordArray,p=i.x64={};p.Word=c.extend({init:function(a,o){this.high=a,this.low=o}}),p.WordArray=c.extend({init:function(a,o){a=this.words=a||[],o!=e?this.sigBytes=o:this.sigBytes=a.length*8},toX32:function(){for(var a=this.words,o=a.length,v=[],l=0;l<o;l++){var g=a[l];v.push(g.high),v.push(g.low)}return x.create(v,this.sigBytes)},clone:function(){for(var a=c.clone.call(this),o=a.words=this.words.slice(0),v=o.length,l=0;l<v;l++)o[l]=o[l].clone();return a}})}(),t})}(pe)),pe.exports}var Ce={exports:{}},Zt;function yi(){return Zt||(Zt=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(){if(typeof ArrayBuffer=="function"){var e=t,i=e.lib,s=i.WordArray,c=s.init,x=s.init=function(p){if(p instanceof ArrayBuffer&&(p=new Uint8Array(p)),(p instanceof Int8Array||typeof Uint8ClampedArray<"u"&&p instanceof Uint8ClampedArray||p instanceof Int16Array||p instanceof Uint16Array||p instanceof Int32Array||p instanceof Uint32Array||p instanceof Float32Array||p instanceof Float64Array)&&(p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength)),p instanceof Uint8Array){for(var a=p.byteLength,o=[],v=0;v<a;v++)o[v>>>2]|=p[v]<<24-v%4*8;c.call(this,o,a)}else c.apply(this,arguments)};x.prototype=s}}(),t.lib.WordArray})}(Ce)),Ce.exports}var ge={exports:{}},Gt;function Fi(){return Gt||(Gt=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(){var e=t,i=e.lib,s=i.WordArray,c=e.enc;c.Utf16=c.Utf16BE={stringify:function(p){for(var a=p.words,o=p.sigBytes,v=[],l=0;l<o;l+=2){var g=a[l>>>2]>>>16-l%4*8&65535;v.push(String.fromCharCode(g))}return v.join("")},parse:function(p){for(var a=p.length,o=[],v=0;v<a;v++)o[v>>>1]|=p.charCodeAt(v)<<16-v%2*16;return s.create(o,a*2)}},c.Utf16LE={stringify:function(p){for(var a=p.words,o=p.sigBytes,v=[],l=0;l<o;l+=2){var g=x(a[l>>>2]>>>16-l%4*8&65535);v.push(String.fromCharCode(g))}return v.join("")},parse:function(p){for(var a=p.length,o=[],v=0;v<a;v++)o[v>>>1]|=x(p.charCodeAt(v)<<16-v%2*16);return s.create(o,a*2)}};function x(p){return p<<8&4278255360|p>>>8&16711935}}(),t.enc.Utf16})}(ge)),ge.exports}var ve={exports:{}},jt;function v0(){return jt||(jt=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(){var e=t,i=e.lib,s=i.WordArray,c=e.enc;c.Base64={stringify:function(p){var a=p.words,o=p.sigBytes,v=this._map;p.clamp();for(var l=[],g=0;g<o;g+=3)for(var f=a[g>>>2]>>>24-g%4*8&255,C=a[g+1>>>2]>>>24-(g+1)%4*8&255,d=a[g+2>>>2]>>>24-(g+2)%4*8&255,B=f<<16|C<<8|d,u=0;u<4&&g+u*.75<o;u++)l.push(v.charAt(B>>>6*(3-u)&63));var h=v.charAt(64);if(h)for(;l.length%4;)l.push(h);return l.join("")},parse:function(p){var a=p.length,o=this._map,v=this._reverseMap;if(!v){v=this._reverseMap=[];for(var l=0;l<o.length;l++)v[o.charCodeAt(l)]=l}var g=o.charAt(64);if(g){var f=p.indexOf(g);f!==-1&&(a=f)}return x(p,a,v)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function x(p,a,o){for(var v=[],l=0,g=0;g<a;g++)if(g%4){var f=o[p.charCodeAt(g-1)]<<g%4*2,C=o[p.charCodeAt(g)]>>>6-g%4*2,d=f|C;v[l>>>2]|=d<<24-l%4*8,l++}return s.create(v,l)}}(),t.enc.Base64})}(ve)),ve.exports}var Ae={exports:{}},Ut;function wi(){return Ut||(Ut=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(){var e=t,i=e.lib,s=i.WordArray,c=e.enc;c.Base64url={stringify:function(p,a){a===void 0&&(a=!0);var o=p.words,v=p.sigBytes,l=a?this._safe_map:this._map;p.clamp();for(var g=[],f=0;f<v;f+=3)for(var C=o[f>>>2]>>>24-f%4*8&255,d=o[f+1>>>2]>>>24-(f+1)%4*8&255,B=o[f+2>>>2]>>>24-(f+2)%4*8&255,u=C<<16|d<<8|B,h=0;h<4&&f+h*.75<v;h++)g.push(l.charAt(u>>>6*(3-h)&63));var A=l.charAt(64);if(A)for(;g.length%4;)g.push(A);return g.join("")},parse:function(p,a){a===void 0&&(a=!0);var o=p.length,v=a?this._safe_map:this._map,l=this._reverseMap;if(!l){l=this._reverseMap=[];for(var g=0;g<v.length;g++)l[v.charCodeAt(g)]=g}var f=v.charAt(64);if(f){var C=p.indexOf(f);C!==-1&&(o=C)}return x(p,o,l)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function x(p,a,o){for(var v=[],l=0,g=0;g<a;g++)if(g%4){var f=o[p.charCodeAt(g-1)]<<g%4*2,C=o[p.charCodeAt(g)]>>>6-g%4*2,d=f|C;v[l>>>2]|=d<<24-l%4*8,l++}return s.create(v,l)}}(),t.enc.Base64url})}(Ae)),Ae.exports}var Ee={exports:{}},Wt;function A0(){return Wt||(Wt=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(e){var i=t,s=i.lib,c=s.WordArray,x=s.Hasher,p=i.algo,a=[];(function(){for(var C=0;C<64;C++)a[C]=e.abs(e.sin(C+1))*4294967296|0})();var o=p.MD5=x.extend({_doReset:function(){this._hash=new c.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(C,d){for(var B=0;B<16;B++){var u=d+B,h=C[u];C[u]=(h<<8|h>>>24)&16711935|(h<<24|h>>>8)&4278255360}var A=this._hash.words,m=C[d+0],E=C[d+1],D=C[d+2],F=C[d+3],P=C[d+4],b=C[d+5],I=C[d+6],_=C[d+7],S=C[d+8],N=C[d+9],T=C[d+10],M=C[d+11],X=C[d+12],j=C[d+13],W=C[d+14],U=C[d+15],w=A[0],R=A[1],L=A[2],k=A[3];w=v(w,R,L,k,m,7,a[0]),k=v(k,w,R,L,E,12,a[1]),L=v(L,k,w,R,D,17,a[2]),R=v(R,L,k,w,F,22,a[3]),w=v(w,R,L,k,P,7,a[4]),k=v(k,w,R,L,b,12,a[5]),L=v(L,k,w,R,I,17,a[6]),R=v(R,L,k,w,_,22,a[7]),w=v(w,R,L,k,S,7,a[8]),k=v(k,w,R,L,N,12,a[9]),L=v(L,k,w,R,T,17,a[10]),R=v(R,L,k,w,M,22,a[11]),w=v(w,R,L,k,X,7,a[12]),k=v(k,w,R,L,j,12,a[13]),L=v(L,k,w,R,W,17,a[14]),R=v(R,L,k,w,U,22,a[15]),w=l(w,R,L,k,E,5,a[16]),k=l(k,w,R,L,I,9,a[17]),L=l(L,k,w,R,M,14,a[18]),R=l(R,L,k,w,m,20,a[19]),w=l(w,R,L,k,b,5,a[20]),k=l(k,w,R,L,T,9,a[21]),L=l(L,k,w,R,U,14,a[22]),R=l(R,L,k,w,P,20,a[23]),w=l(w,R,L,k,N,5,a[24]),k=l(k,w,R,L,W,9,a[25]),L=l(L,k,w,R,F,14,a[26]),R=l(R,L,k,w,S,20,a[27]),w=l(w,R,L,k,j,5,a[28]),k=l(k,w,R,L,D,9,a[29]),L=l(L,k,w,R,_,14,a[30]),R=l(R,L,k,w,X,20,a[31]),w=g(w,R,L,k,b,4,a[32]),k=g(k,w,R,L,S,11,a[33]),L=g(L,k,w,R,M,16,a[34]),R=g(R,L,k,w,W,23,a[35]),w=g(w,R,L,k,E,4,a[36]),k=g(k,w,R,L,P,11,a[37]),L=g(L,k,w,R,_,16,a[38]),R=g(R,L,k,w,T,23,a[39]),w=g(w,R,L,k,j,4,a[40]),k=g(k,w,R,L,m,11,a[41]),L=g(L,k,w,R,F,16,a[42]),R=g(R,L,k,w,I,23,a[43]),w=g(w,R,L,k,N,4,a[44]),k=g(k,w,R,L,X,11,a[45]),L=g(L,k,w,R,U,16,a[46]),R=g(R,L,k,w,D,23,a[47]),w=f(w,R,L,k,m,6,a[48]),k=f(k,w,R,L,_,10,a[49]),L=f(L,k,w,R,W,15,a[50]),R=f(R,L,k,w,b,21,a[51]),w=f(w,R,L,k,X,6,a[52]),k=f(k,w,R,L,F,10,a[53]),L=f(L,k,w,R,T,15,a[54]),R=f(R,L,k,w,E,21,a[55]),w=f(w,R,L,k,S,6,a[56]),k=f(k,w,R,L,U,10,a[57]),L=f(L,k,w,R,I,15,a[58]),R=f(R,L,k,w,j,21,a[59]),w=f(w,R,L,k,P,6,a[60]),k=f(k,w,R,L,M,10,a[61]),L=f(L,k,w,R,D,15,a[62]),R=f(R,L,k,w,N,21,a[63]),A[0]=A[0]+w|0,A[1]=A[1]+R|0,A[2]=A[2]+L|0,A[3]=A[3]+k|0},_doFinalize:function(){var C=this._data,d=C.words,B=this._nDataBytes*8,u=C.sigBytes*8;d[u>>>5]|=128<<24-u%32;var h=e.floor(B/4294967296),A=B;d[(u+64>>>9<<4)+15]=(h<<8|h>>>24)&16711935|(h<<24|h>>>8)&4278255360,d[(u+64>>>9<<4)+14]=(A<<8|A>>>24)&16711935|(A<<24|A>>>8)&4278255360,C.sigBytes=(d.length+1)*4,this._process();for(var m=this._hash,E=m.words,D=0;D<4;D++){var F=E[D];E[D]=(F<<8|F>>>24)&16711935|(F<<24|F>>>8)&4278255360}return m},clone:function(){var C=x.clone.call(this);return C._hash=this._hash.clone(),C}});function v(C,d,B,u,h,A,m){var E=C+(d&B|~d&u)+h+m;return(E<<A|E>>>32-A)+d}function l(C,d,B,u,h,A,m){var E=C+(d&u|B&~u)+h+m;return(E<<A|E>>>32-A)+d}function g(C,d,B,u,h,A,m){var E=C+(d^B^u)+h+m;return(E<<A|E>>>32-A)+d}function f(C,d,B,u,h,A,m){var E=C+(B^(d|~u))+h+m;return(E<<A|E>>>32-A)+d}i.MD5=x._createHelper(o),i.HmacMD5=x._createHmacHelper(o)}(Math),t.MD5})}(Ee)),Ee.exports}var me={exports:{}},qt;function Xt(){return qt||(qt=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(){var e=t,i=e.lib,s=i.WordArray,c=i.Hasher,x=e.algo,p=[],a=x.SHA1=c.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(o,v){for(var l=this._hash.words,g=l[0],f=l[1],C=l[2],d=l[3],B=l[4],u=0;u<80;u++){if(u<16)p[u]=o[v+u]|0;else{var h=p[u-3]^p[u-8]^p[u-14]^p[u-16];p[u]=h<<1|h>>>31}var A=(g<<5|g>>>27)+B+p[u];u<20?A+=(f&C|~f&d)+1518500249:u<40?A+=(f^C^d)+1859775393:u<60?A+=(f&C|f&d|C&d)-1894007588:A+=(f^C^d)-899497514,B=d,d=C,C=f<<30|f>>>2,f=g,g=A}l[0]=l[0]+g|0,l[1]=l[1]+f|0,l[2]=l[2]+C|0,l[3]=l[3]+d|0,l[4]=l[4]+B|0},_doFinalize:function(){var o=this._data,v=o.words,l=this._nDataBytes*8,g=o.sigBytes*8;return v[g>>>5]|=128<<24-g%32,v[(g+64>>>9<<4)+14]=Math.floor(l/4294967296),v[(g+64>>>9<<4)+15]=l,o.sigBytes=v.length*4,this._process(),this._hash},clone:function(){var o=c.clone.call(this);return o._hash=this._hash.clone(),o}});e.SHA1=c._createHelper(a),e.HmacSHA1=c._createHmacHelper(a)}(),t.SHA1})}(me)),me.exports}var Be={exports:{}},Jt;function be(){return Jt||(Jt=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){return function(e){var i=t,s=i.lib,c=s.WordArray,x=s.Hasher,p=i.algo,a=[],o=[];(function(){function g(B){for(var u=e.sqrt(B),h=2;h<=u;h++)if(!(B%h))return!1;return!0}function f(B){return(B-(B|0))*4294967296|0}for(var C=2,d=0;d<64;)g(C)&&(d<8&&(a[d]=f(e.pow(C,1/2))),o[d]=f(e.pow(C,1/3)),d++),C++})();var v=[],l=p.SHA256=x.extend({_doReset:function(){this._hash=new c.init(a.slice(0))},_doProcessBlock:function(g,f){for(var C=this._hash.words,d=C[0],B=C[1],u=C[2],h=C[3],A=C[4],m=C[5],E=C[6],D=C[7],F=0;F<64;F++){if(F<16)v[F]=g[f+F]|0;else{var P=v[F-15],b=(P<<25|P>>>7)^(P<<14|P>>>18)^P>>>3,I=v[F-2],_=(I<<15|I>>>17)^(I<<13|I>>>19)^I>>>10;v[F]=b+v[F-7]+_+v[F-16]}var S=A&m^~A&E,N=d&B^d&u^B&u,T=(d<<30|d>>>2)^(d<<19|d>>>13)^(d<<10|d>>>22),M=(A<<26|A>>>6)^(A<<21|A>>>11)^(A<<7|A>>>25),X=D+M+S+o[F]+v[F],j=T+N;D=E,E=m,m=A,A=h+X|0,h=u,u=B,B=d,d=X+j|0}C[0]=C[0]+d|0,C[1]=C[1]+B|0,C[2]=C[2]+u|0,C[3]=C[3]+h|0,C[4]=C[4]+A|0,C[5]=C[5]+m|0,C[6]=C[6]+E|0,C[7]=C[7]+D|0},_doFinalize:function(){var g=this._data,f=g.words,C=this._nDataBytes*8,d=g.sigBytes*8;return f[d>>>5]|=128<<24-d%32,f[(d+64>>>9<<4)+14]=e.floor(C/4294967296),f[(d+64>>>9<<4)+15]=C,g.sigBytes=f.length*4,this._process(),this._hash},clone:function(){var g=x.clone.call(this);return g._hash=this._hash.clone(),g}});i.SHA256=x._createHelper(l),i.HmacSHA256=x._createHmacHelper(l)}(Math),t.SHA256})}(Be)),Be.exports}var De={exports:{}},Vt;function _i(){return Vt||(Vt=1,function(r,n){(function(t,e,i){r.exports=e(Z(),be())})(z,function(t){return function(){var e=t,i=e.lib,s=i.WordArray,c=e.algo,x=c.SHA256,p=c.SHA224=x.extend({_doReset:function(){this._hash=new s.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var a=x._doFinalize.call(this);return a.sigBytes-=4,a}});e.SHA224=x._createHelper(p),e.HmacSHA224=x._createHmacHelper(p)}(),t.SHA224})}(De)),De.exports}var Ie={exports:{}},Yt;function Qt(){return Yt||(Yt=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Q0())})(z,function(t){return function(){var e=t,i=e.lib,s=i.Hasher,c=e.x64,x=c.Word,p=c.WordArray,a=e.algo;function o(){return x.create.apply(x,arguments)}var v=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],l=[];(function(){for(var f=0;f<80;f++)l[f]=o()})();var g=a.SHA512=s.extend({_doReset:function(){this._hash=new p.init([new x.init(1779033703,4089235720),new x.init(3144134277,2227873595),new x.init(1013904242,4271175723),new x.init(2773480762,1595750129),new x.init(1359893119,2917565137),new x.init(2600822924,725511199),new x.init(528734635,4215389547),new x.init(1541459225,327033209)])},_doProcessBlock:function(f,C){for(var d=this._hash.words,B=d[0],u=d[1],h=d[2],A=d[3],m=d[4],E=d[5],D=d[6],F=d[7],P=B.high,b=B.low,I=u.high,_=u.low,S=h.high,N=h.low,T=A.high,M=A.low,X=m.high,j=m.low,W=E.high,U=E.low,w=D.high,R=D.low,L=F.high,k=F.low,J=P,q=b,t0=I,O=_,R0=S,I0=N,$e=T,L0=M,o0=X,n0=j,$0=W,P0=U,ee=w,N0=R,et=L,T0=k,c0=0;c0<80;c0++){var a0,u0,te=l[c0];if(c0<16)u0=te.high=f[C+c0*2]|0,a0=te.low=f[C+c0*2+1]|0;else{var Br=l[c0-15],y0=Br.high,H0=Br.low,ea=(y0>>>1|H0<<31)^(y0>>>8|H0<<24)^y0>>>7,br=(H0>>>1|y0<<31)^(H0>>>8|y0<<24)^(H0>>>7|y0<<25),Dr=l[c0-2],F0=Dr.high,z0=Dr.low,ta=(F0>>>19|z0<<13)^(F0<<3|z0>>>29)^F0>>>6,Ir=(z0>>>19|F0<<13)^(z0<<3|F0>>>29)^(z0>>>6|F0<<26),yr=l[c0-7],ra=yr.high,na=yr.low,Fr=l[c0-16],ia=Fr.high,wr=Fr.low;a0=br+na,u0=ea+ra+(a0>>>0<br>>>0?1:0),a0=a0+Ir,u0=u0+ta+(a0>>>0<Ir>>>0?1:0),a0=a0+wr,u0=u0+ia+(a0>>>0<wr>>>0?1:0),te.high=u0,te.low=a0}var aa=o0&$0^~o0&ee,_r=n0&P0^~n0&N0,sa=J&t0^J&R0^t0&R0,oa=q&O^q&I0^O&I0,ca=(J>>>28|q<<4)^(J<<30|q>>>2)^(J<<25|q>>>7),Sr=(q>>>28|J<<4)^(q<<30|J>>>2)^(q<<25|J>>>7),xa=(o0>>>14|n0<<18)^(o0>>>18|n0<<14)^(o0<<23|n0>>>9),la=(n0>>>14|o0<<18)^(n0>>>18|o0<<14)^(n0<<23|o0>>>9),kr=v[c0],fa=kr.high,Rr=kr.low,i0=T0+la,h0=et+xa+(i0>>>0<T0>>>0?1:0),i0=i0+_r,h0=h0+aa+(i0>>>0<_r>>>0?1:0),i0=i0+Rr,h0=h0+fa+(i0>>>0<Rr>>>0?1:0),i0=i0+a0,h0=h0+u0+(i0>>>0<a0>>>0?1:0),Lr=Sr+oa,da=ca+sa+(Lr>>>0<Sr>>>0?1:0);et=ee,T0=N0,ee=$0,N0=P0,$0=o0,P0=n0,n0=L0+i0|0,o0=$e+h0+(n0>>>0<L0>>>0?1:0)|0,$e=R0,L0=I0,R0=t0,I0=O,t0=J,O=q,q=i0+Lr|0,J=h0+da+(q>>>0<i0>>>0?1:0)|0}b=B.low=b+q,B.high=P+J+(b>>>0<q>>>0?1:0),_=u.low=_+O,u.high=I+t0+(_>>>0<O>>>0?1:0),N=h.low=N+I0,h.high=S+R0+(N>>>0<I0>>>0?1:0),M=A.low=M+L0,A.high=T+$e+(M>>>0<L0>>>0?1:0),j=m.low=j+n0,m.high=X+o0+(j>>>0<n0>>>0?1:0),U=E.low=U+P0,E.high=W+$0+(U>>>0<P0>>>0?1:0),R=D.low=R+N0,D.high=w+ee+(R>>>0<N0>>>0?1:0),k=F.low=k+T0,F.high=L+et+(k>>>0<T0>>>0?1:0)},_doFinalize:function(){var f=this._data,C=f.words,d=this._nDataBytes*8,B=f.sigBytes*8;C[B>>>5]|=128<<24-B%32,C[(B+128>>>10<<5)+30]=Math.floor(d/4294967296),C[(B+128>>>10<<5)+31]=d,f.sigBytes=C.length*4,this._process();var u=this._hash.toX32();return u},clone:function(){var f=s.clone.call(this);return f._hash=this._hash.clone(),f},blockSize:1024/32});e.SHA512=s._createHelper(g),e.HmacSHA512=s._createHmacHelper(g)}(),t.SHA512})}(Ie)),Ie.exports}var ye={exports:{}},Kt;function Si(){return Kt||(Kt=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Q0(),Qt())})(z,function(t){return function(){var e=t,i=e.x64,s=i.Word,c=i.WordArray,x=e.algo,p=x.SHA512,a=x.SHA384=p.extend({_doReset:function(){this._hash=new c.init([new s.init(3418070365,3238371032),new s.init(1654270250,914150663),new s.init(2438529370,812702999),new s.init(355462360,4144912697),new s.init(1731405415,4290775857),new s.init(2394180231,1750603025),new s.init(3675008525,1694076839),new s.init(1203062813,3204075428)])},_doFinalize:function(){var o=p._doFinalize.call(this);return o.sigBytes-=16,o}});e.SHA384=p._createHelper(a),e.HmacSHA384=p._createHmacHelper(a)}(),t.SHA384})}(ye)),ye.exports}var Fe={exports:{}},$t;function ki(){return $t||($t=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Q0())})(z,function(t){return function(e){var i=t,s=i.lib,c=s.WordArray,x=s.Hasher,p=i.x64,a=p.Word,o=i.algo,v=[],l=[],g=[];(function(){for(var d=1,B=0,u=0;u<24;u++){v[d+5*B]=(u+1)*(u+2)/2%64;var h=B%5,A=(2*d+3*B)%5;d=h,B=A}for(var d=0;d<5;d++)for(var B=0;B<5;B++)l[d+5*B]=B+(2*d+3*B)%5*5;for(var m=1,E=0;E<24;E++){for(var D=0,F=0,P=0;P<7;P++){if(m&1){var b=(1<<P)-1;b<32?F^=1<<b:D^=1<<b-32}m&128?m=m<<1^113:m<<=1}g[E]=a.create(D,F)}})();var f=[];(function(){for(var d=0;d<25;d++)f[d]=a.create()})();var C=o.SHA3=x.extend({cfg:x.cfg.extend({outputLength:512}),_doReset:function(){for(var d=this._state=[],B=0;B<25;B++)d[B]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(d,B){for(var u=this._state,h=this.blockSize/2,A=0;A<h;A++){var m=d[B+2*A],E=d[B+2*A+1];m=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360,E=(E<<8|E>>>24)&16711935|(E<<24|E>>>8)&4278255360;var D=u[A];D.high^=E,D.low^=m}for(var F=0;F<24;F++){for(var P=0;P<5;P++){for(var b=0,I=0,_=0;_<5;_++){var D=u[P+5*_];b^=D.high,I^=D.low}var S=f[P];S.high=b,S.low=I}for(var P=0;P<5;P++)for(var N=f[(P+4)%5],T=f[(P+1)%5],M=T.high,X=T.low,b=N.high^(M<<1|X>>>31),I=N.low^(X<<1|M>>>31),_=0;_<5;_++){var D=u[P+5*_];D.high^=b,D.low^=I}for(var j=1;j<25;j++){var b,I,D=u[j],W=D.high,U=D.low,w=v[j];w<32?(b=W<<w|U>>>32-w,I=U<<w|W>>>32-w):(b=U<<w-32|W>>>64-w,I=W<<w-32|U>>>64-w);var R=f[l[j]];R.high=b,R.low=I}var L=f[0],k=u[0];L.high=k.high,L.low=k.low;for(var P=0;P<5;P++)for(var _=0;_<5;_++){var j=P+5*_,D=u[j],J=f[j],q=f[(P+1)%5+5*_],t0=f[(P+2)%5+5*_];D.high=J.high^~q.high&t0.high,D.low=J.low^~q.low&t0.low}var D=u[0],O=g[F];D.high^=O.high,D.low^=O.low}},_doFinalize:function(){var d=this._data,B=d.words;this._nDataBytes*8;var u=d.sigBytes*8,h=this.blockSize*32;B[u>>>5]|=1<<24-u%32,B[(e.ceil((u+1)/h)*h>>>5)-1]|=128,d.sigBytes=B.length*4,this._process();for(var A=this._state,m=this.cfg.outputLength/8,E=m/8,D=[],F=0;F<E;F++){var P=A[F],b=P.high,I=P.low;b=(b<<8|b>>>24)&16711935|(b<<24|b>>>8)&4278255360,I=(I<<8|I>>>24)&16711935|(I<<24|I>>>8)&4278255360,D.push(I),D.push(b)}return new c.init(D,m)},clone:function(){for(var d=x.clone.call(this),B=d._state=this._state.slice(0),u=0;u<25;u++)B[u]=B[u].clone();return d}});i.SHA3=x._createHelper(C),i.HmacSHA3=x._createHmacHelper(C)}(Math),t.SHA3})}(Fe)),Fe.exports}var we={exports:{}},er;function Ri(){return er||(er=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){/** @preserve
  			(c) 2012 by Cédric Mesnil. All rights reserved.

  			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

  			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
  			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

  			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  			*/return function(e){var i=t,s=i.lib,c=s.WordArray,x=s.Hasher,p=i.algo,a=c.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=c.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),v=c.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=c.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),g=c.create([0,1518500249,1859775393,2400959708,2840853838]),f=c.create([1352829926,1548603684,1836072691,2053994217,0]),C=p.RIPEMD160=x.extend({_doReset:function(){this._hash=c.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(E,D){for(var F=0;F<16;F++){var P=D+F,b=E[P];E[P]=(b<<8|b>>>24)&16711935|(b<<24|b>>>8)&4278255360}var I=this._hash.words,_=g.words,S=f.words,N=a.words,T=o.words,M=v.words,X=l.words,j,W,U,w,R,L,k,J,q,t0;L=j=I[0],k=W=I[1],J=U=I[2],q=w=I[3],t0=R=I[4];for(var O,F=0;F<80;F+=1)O=j+E[D+N[F]]|0,F<16?O+=d(W,U,w)+_[0]:F<32?O+=B(W,U,w)+_[1]:F<48?O+=u(W,U,w)+_[2]:F<64?O+=h(W,U,w)+_[3]:O+=A(W,U,w)+_[4],O=O|0,O=m(O,M[F]),O=O+R|0,j=R,R=w,w=m(U,10),U=W,W=O,O=L+E[D+T[F]]|0,F<16?O+=A(k,J,q)+S[0]:F<32?O+=h(k,J,q)+S[1]:F<48?O+=u(k,J,q)+S[2]:F<64?O+=B(k,J,q)+S[3]:O+=d(k,J,q)+S[4],O=O|0,O=m(O,X[F]),O=O+t0|0,L=t0,t0=q,q=m(J,10),J=k,k=O;O=I[1]+U+q|0,I[1]=I[2]+w+t0|0,I[2]=I[3]+R+L|0,I[3]=I[4]+j+k|0,I[4]=I[0]+W+J|0,I[0]=O},_doFinalize:function(){var E=this._data,D=E.words,F=this._nDataBytes*8,P=E.sigBytes*8;D[P>>>5]|=128<<24-P%32,D[(P+64>>>9<<4)+14]=(F<<8|F>>>24)&16711935|(F<<24|F>>>8)&4278255360,E.sigBytes=(D.length+1)*4,this._process();for(var b=this._hash,I=b.words,_=0;_<5;_++){var S=I[_];I[_]=(S<<8|S>>>24)&16711935|(S<<24|S>>>8)&4278255360}return b},clone:function(){var E=x.clone.call(this);return E._hash=this._hash.clone(),E}});function d(E,D,F){return E^D^F}function B(E,D,F){return E&D|~E&F}function u(E,D,F){return(E|~D)^F}function h(E,D,F){return E&F|D&~F}function A(E,D,F){return E^(D|~F)}function m(E,D){return E<<D|E>>>32-D}i.RIPEMD160=x._createHelper(C),i.HmacRIPEMD160=x._createHmacHelper(C)}(),t.RIPEMD160})}(we)),we.exports}var _e={exports:{}},tr;function Se(){return tr||(tr=1,function(r,n){(function(t,e){r.exports=e(Z())})(z,function(t){(function(){var e=t,i=e.lib,s=i.Base,c=e.enc,x=c.Utf8,p=e.algo;p.HMAC=s.extend({init:function(a,o){a=this._hasher=new a.init,typeof o=="string"&&(o=x.parse(o));var v=a.blockSize,l=v*4;o.sigBytes>l&&(o=a.finalize(o)),o.clamp();for(var g=this._oKey=o.clone(),f=this._iKey=o.clone(),C=g.words,d=f.words,B=0;B<v;B++)C[B]^=1549556828,d[B]^=909522486;g.sigBytes=f.sigBytes=l,this.reset()},reset:function(){var a=this._hasher;a.reset(),a.update(this._iKey)},update:function(a){return this._hasher.update(a),this},finalize:function(a){var o=this._hasher,v=o.finalize(a);o.reset();var l=o.finalize(this._oKey.clone().concat(v));return l}})})()})}(_e)),_e.exports}var ke={exports:{}},rr;function Li(){return rr||(rr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),be(),Se())})(z,function(t){return function(){var e=t,i=e.lib,s=i.Base,c=i.WordArray,x=e.algo,p=x.SHA256,a=x.HMAC,o=x.PBKDF2=s.extend({cfg:s.extend({keySize:128/32,hasher:p,iterations:25e4}),init:function(v){this.cfg=this.cfg.extend(v)},compute:function(v,l){for(var g=this.cfg,f=a.create(g.hasher,v),C=c.create(),d=c.create([1]),B=C.words,u=d.words,h=g.keySize,A=g.iterations;B.length<h;){var m=f.update(l).finalize(d);f.reset();for(var E=m.words,D=E.length,F=m,P=1;P<A;P++){F=f.finalize(F),f.reset();for(var b=F.words,I=0;I<D;I++)E[I]^=b[I]}C.concat(m),u[0]++}return C.sigBytes=h*4,C}});e.PBKDF2=function(v,l,g){return o.create(g).compute(v,l)}}(),t.PBKDF2})}(ke)),ke.exports}var Re={exports:{}},nr;function d0(){return nr||(nr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Xt(),Se())})(z,function(t){return function(){var e=t,i=e.lib,s=i.Base,c=i.WordArray,x=e.algo,p=x.MD5,a=x.EvpKDF=s.extend({cfg:s.extend({keySize:128/32,hasher:p,iterations:1}),init:function(o){this.cfg=this.cfg.extend(o)},compute:function(o,v){for(var l,g=this.cfg,f=g.hasher.create(),C=c.create(),d=C.words,B=g.keySize,u=g.iterations;d.length<B;){l&&f.update(l),l=f.update(o).finalize(v),f.reset();for(var h=1;h<u;h++)l=f.finalize(l),f.reset();C.concat(l)}return C.sigBytes=B*4,C}});e.EvpKDF=function(o,v,l){return a.create(l).compute(o,v)}}(),t.EvpKDF})}(Re)),Re.exports}var Le={exports:{}},ir;function Y(){return ir||(ir=1,function(r,n){(function(t,e,i){r.exports=e(Z(),d0())})(z,function(t){t.lib.Cipher||function(e){var i=t,s=i.lib,c=s.Base,x=s.WordArray,p=s.BufferedBlockAlgorithm,a=i.enc;a.Utf8;var o=a.Base64,v=i.algo,l=v.EvpKDF,g=s.Cipher=p.extend({cfg:c.extend(),createEncryptor:function(b,I){return this.create(this._ENC_XFORM_MODE,b,I)},createDecryptor:function(b,I){return this.create(this._DEC_XFORM_MODE,b,I)},init:function(b,I,_){this.cfg=this.cfg.extend(_),this._xformMode=b,this._key=I,this.reset()},reset:function(){p.reset.call(this),this._doReset()},process:function(b){return this._append(b),this._process()},finalize:function(b){b&&this._append(b);var I=this._doFinalize();return I},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function b(I){return typeof I=="string"?P:E}return function(I){return{encrypt:function(_,S,N){return b(S).encrypt(I,_,S,N)},decrypt:function(_,S,N){return b(S).decrypt(I,_,S,N)}}}}()});s.StreamCipher=g.extend({_doFinalize:function(){var b=this._process(!0);return b},blockSize:1});var f=i.mode={},C=s.BlockCipherMode=c.extend({createEncryptor:function(b,I){return this.Encryptor.create(b,I)},createDecryptor:function(b,I){return this.Decryptor.create(b,I)},init:function(b,I){this._cipher=b,this._iv=I}}),d=f.CBC=function(){var b=C.extend();b.Encryptor=b.extend({processBlock:function(_,S){var N=this._cipher,T=N.blockSize;I.call(this,_,S,T),N.encryptBlock(_,S),this._prevBlock=_.slice(S,S+T)}}),b.Decryptor=b.extend({processBlock:function(_,S){var N=this._cipher,T=N.blockSize,M=_.slice(S,S+T);N.decryptBlock(_,S),I.call(this,_,S,T),this._prevBlock=M}});function I(_,S,N){var T,M=this._iv;M?(T=M,this._iv=e):T=this._prevBlock;for(var X=0;X<N;X++)_[S+X]^=T[X]}return b}(),B=i.pad={},u=B.Pkcs7={pad:function(b,I){for(var _=I*4,S=_-b.sigBytes%_,N=S<<24|S<<16|S<<8|S,T=[],M=0;M<S;M+=4)T.push(N);var X=x.create(T,S);b.concat(X)},unpad:function(b){var I=b.words[b.sigBytes-1>>>2]&255;b.sigBytes-=I}};s.BlockCipher=g.extend({cfg:g.cfg.extend({mode:d,padding:u}),reset:function(){var b;g.reset.call(this);var I=this.cfg,_=I.iv,S=I.mode;this._xformMode==this._ENC_XFORM_MODE?b=S.createEncryptor:(b=S.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==b?this._mode.init(this,_&&_.words):(this._mode=b.call(S,this,_&&_.words),this._mode.__creator=b)},_doProcessBlock:function(b,I){this._mode.processBlock(b,I)},_doFinalize:function(){var b,I=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(I.pad(this._data,this.blockSize),b=this._process(!0)):(b=this._process(!0),I.unpad(b)),b},blockSize:128/32});var h=s.CipherParams=c.extend({init:function(b){this.mixIn(b)},toString:function(b){return(b||this.formatter).stringify(this)}}),A=i.format={},m=A.OpenSSL={stringify:function(b){var I,_=b.ciphertext,S=b.salt;return S?I=x.create([1398893684,1701076831]).concat(S).concat(_):I=_,I.toString(o)},parse:function(b){var I,_=o.parse(b),S=_.words;return S[0]==1398893684&&S[1]==1701076831&&(I=x.create(S.slice(2,4)),S.splice(0,4),_.sigBytes-=16),h.create({ciphertext:_,salt:I})}},E=s.SerializableCipher=c.extend({cfg:c.extend({format:m}),encrypt:function(b,I,_,S){S=this.cfg.extend(S);var N=b.createEncryptor(_,S),T=N.finalize(I),M=N.cfg;return h.create({ciphertext:T,key:_,iv:M.iv,algorithm:b,mode:M.mode,padding:M.padding,blockSize:b.blockSize,formatter:S.format})},decrypt:function(b,I,_,S){S=this.cfg.extend(S),I=this._parse(I,S.format);var N=b.createDecryptor(_,S).finalize(I.ciphertext);return N},_parse:function(b,I){return typeof b=="string"?I.parse(b,this):b}}),D=i.kdf={},F=D.OpenSSL={execute:function(b,I,_,S,N){if(S||(S=x.random(64/8)),N)var T=l.create({keySize:I+_,hasher:N}).compute(b,S);else var T=l.create({keySize:I+_}).compute(b,S);var M=x.create(T.words.slice(I),_*4);return T.sigBytes=I*4,h.create({key:T,iv:M,salt:S})}},P=s.PasswordBasedCipher=E.extend({cfg:E.cfg.extend({kdf:F}),encrypt:function(b,I,_,S){S=this.cfg.extend(S);var N=S.kdf.execute(_,b.keySize,b.ivSize,S.salt,S.hasher);S.iv=N.iv;var T=E.encrypt.call(this,b,I,N.key,S);return T.mixIn(N),T},decrypt:function(b,I,_,S){S=this.cfg.extend(S),I=this._parse(I,S.format);var N=S.kdf.execute(_,b.keySize,b.ivSize,I.salt,S.hasher);S.iv=N.iv;var T=E.decrypt.call(this,b,I,N.key,S);return T}})}()})}(Le)),Le.exports}var Pe={exports:{}},ar;function Pi(){return ar||(ar=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();e.Encryptor=e.extend({processBlock:function(s,c){var x=this._cipher,p=x.blockSize;i.call(this,s,c,p,x),this._prevBlock=s.slice(c,c+p)}}),e.Decryptor=e.extend({processBlock:function(s,c){var x=this._cipher,p=x.blockSize,a=s.slice(c,c+p);i.call(this,s,c,p,x),this._prevBlock=a}});function i(s,c,x,p){var a,o=this._iv;o?(a=o.slice(0),this._iv=void 0):a=this._prevBlock,p.encryptBlock(a,0);for(var v=0;v<x;v++)s[c+v]^=a[v]}return e}(),t.mode.CFB})}(Pe)),Pe.exports}var Ne={exports:{}},sr;function Ni(){return sr||(sr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.mode.CTR=function(){var e=t.lib.BlockCipherMode.extend(),i=e.Encryptor=e.extend({processBlock:function(s,c){var x=this._cipher,p=x.blockSize,a=this._iv,o=this._counter;a&&(o=this._counter=a.slice(0),this._iv=void 0);var v=o.slice(0);x.encryptBlock(v,0),o[p-1]=o[p-1]+1|0;for(var l=0;l<p;l++)s[c+l]^=v[l]}});return e.Decryptor=i,e}(),t.mode.CTR})}(Ne)),Ne.exports}var Te={exports:{}},or;function Ti(){return or||(or=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */return t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function i(x){if((x>>24&255)===255){var p=x>>16&255,a=x>>8&255,o=x&255;p===255?(p=0,a===255?(a=0,o===255?o=0:++o):++a):++p,x=0,x+=p<<16,x+=a<<8,x+=o}else x+=1<<24;return x}function s(x){return(x[0]=i(x[0]))===0&&(x[1]=i(x[1])),x}var c=e.Encryptor=e.extend({processBlock:function(x,p){var a=this._cipher,o=a.blockSize,v=this._iv,l=this._counter;v&&(l=this._counter=v.slice(0),this._iv=void 0),s(l);var g=l.slice(0);a.encryptBlock(g,0);for(var f=0;f<o;f++)x[p+f]^=g[f]}});return e.Decryptor=c,e}(),t.mode.CTRGladman})}(Te)),Te.exports}var He={exports:{}},cr;function Hi(){return cr||(cr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.mode.OFB=function(){var e=t.lib.BlockCipherMode.extend(),i=e.Encryptor=e.extend({processBlock:function(s,c){var x=this._cipher,p=x.blockSize,a=this._iv,o=this._keystream;a&&(o=this._keystream=a.slice(0),this._iv=void 0),x.encryptBlock(o,0);for(var v=0;v<p;v++)s[c+v]^=o[v]}});return e.Decryptor=i,e}(),t.mode.OFB})}(He)),He.exports}var ze={exports:{}},xr;function zi(){return xr||(xr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.mode.ECB=function(){var e=t.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(i,s){this._cipher.encryptBlock(i,s)}}),e.Decryptor=e.extend({processBlock:function(i,s){this._cipher.decryptBlock(i,s)}}),e}(),t.mode.ECB})}(ze)),ze.exports}var Me={exports:{}},lr;function Mi(){return lr||(lr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.pad.AnsiX923={pad:function(e,i){var s=e.sigBytes,c=i*4,x=c-s%c,p=s+x-1;e.clamp(),e.words[p>>>2]|=x<<24-p%4*8,e.sigBytes+=x},unpad:function(e){var i=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=i}},t.pad.Ansix923})}(Me)),Me.exports}var Oe={exports:{}},fr;function Oi(){return fr||(fr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.pad.Iso10126={pad:function(e,i){var s=i*4,c=s-e.sigBytes%s;e.concat(t.lib.WordArray.random(c-1)).concat(t.lib.WordArray.create([c<<24],1))},unpad:function(e){var i=e.words[e.sigBytes-1>>>2]&255;e.sigBytes-=i}},t.pad.Iso10126})}(Oe)),Oe.exports}var Ze={exports:{}},dr;function Zi(){return dr||(dr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.pad.Iso97971={pad:function(e,i){e.concat(t.lib.WordArray.create([2147483648],1)),t.pad.ZeroPadding.pad(e,i)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971})}(Ze)),Ze.exports}var Ge={exports:{}},ur;function Gi(){return ur||(ur=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.pad.ZeroPadding={pad:function(e,i){var s=i*4;e.clamp(),e.sigBytes+=s-(e.sigBytes%s||s)},unpad:function(e){for(var i=e.words,s=e.sigBytes-1,s=e.sigBytes-1;s>=0;s--)if(i[s>>>2]>>>24-s%4*8&255){e.sigBytes=s+1;break}}},t.pad.ZeroPadding})}(Ge)),Ge.exports}var je={exports:{}},hr;function ji(){return hr||(hr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding})}(je)),je.exports}var Ue={exports:{}},pr;function Ui(){return pr||(pr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),Y())})(z,function(t){return function(e){var i=t,s=i.lib,c=s.CipherParams,x=i.enc,p=x.Hex,a=i.format;a.Hex={stringify:function(o){return o.ciphertext.toString(p)},parse:function(o){var v=p.parse(o);return c.create({ciphertext:v})}}}(),t.format.Hex})}(Ue)),Ue.exports}var We={exports:{}},Cr;function Wi(){return Cr||(Cr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),v0(),A0(),d0(),Y())})(z,function(t){return function(){var e=t,i=e.lib,s=i.BlockCipher,c=e.algo,x=[],p=[],a=[],o=[],v=[],l=[],g=[],f=[],C=[],d=[];(function(){for(var h=[],A=0;A<256;A++)A<128?h[A]=A<<1:h[A]=A<<1^283;for(var m=0,E=0,A=0;A<256;A++){var D=E^E<<1^E<<2^E<<3^E<<4;D=D>>>8^D&255^99,x[m]=D,p[D]=m;var F=h[m],P=h[F],b=h[P],I=h[D]*257^D*16843008;a[m]=I<<24|I>>>8,o[m]=I<<16|I>>>16,v[m]=I<<8|I>>>24,l[m]=I;var I=b*16843009^P*65537^F*257^m*16843008;g[D]=I<<24|I>>>8,f[D]=I<<16|I>>>16,C[D]=I<<8|I>>>24,d[D]=I,m?(m=F^h[h[h[b^F]]],E^=h[h[E]]):m=E=1}})();var B=[0,1,2,4,8,16,32,64,128,27,54],u=c.AES=s.extend({_doReset:function(){var h;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var A=this._keyPriorReset=this._key,m=A.words,E=A.sigBytes/4,D=this._nRounds=E+6,F=(D+1)*4,P=this._keySchedule=[],b=0;b<F;b++)b<E?P[b]=m[b]:(h=P[b-1],b%E?E>6&&b%E==4&&(h=x[h>>>24]<<24|x[h>>>16&255]<<16|x[h>>>8&255]<<8|x[h&255]):(h=h<<8|h>>>24,h=x[h>>>24]<<24|x[h>>>16&255]<<16|x[h>>>8&255]<<8|x[h&255],h^=B[b/E|0]<<24),P[b]=P[b-E]^h);for(var I=this._invKeySchedule=[],_=0;_<F;_++){var b=F-_;if(_%4)var h=P[b];else var h=P[b-4];_<4||b<=4?I[_]=h:I[_]=g[x[h>>>24]]^f[x[h>>>16&255]]^C[x[h>>>8&255]]^d[x[h&255]]}}},encryptBlock:function(h,A){this._doCryptBlock(h,A,this._keySchedule,a,o,v,l,x)},decryptBlock:function(h,A){var m=h[A+1];h[A+1]=h[A+3],h[A+3]=m,this._doCryptBlock(h,A,this._invKeySchedule,g,f,C,d,p);var m=h[A+1];h[A+1]=h[A+3],h[A+3]=m},_doCryptBlock:function(h,A,m,E,D,F,P,b){for(var I=this._nRounds,_=h[A]^m[0],S=h[A+1]^m[1],N=h[A+2]^m[2],T=h[A+3]^m[3],M=4,X=1;X<I;X++){var j=E[_>>>24]^D[S>>>16&255]^F[N>>>8&255]^P[T&255]^m[M++],W=E[S>>>24]^D[N>>>16&255]^F[T>>>8&255]^P[_&255]^m[M++],U=E[N>>>24]^D[T>>>16&255]^F[_>>>8&255]^P[S&255]^m[M++],w=E[T>>>24]^D[_>>>16&255]^F[S>>>8&255]^P[N&255]^m[M++];_=j,S=W,N=U,T=w}var j=(b[_>>>24]<<24|b[S>>>16&255]<<16|b[N>>>8&255]<<8|b[T&255])^m[M++],W=(b[S>>>24]<<24|b[N>>>16&255]<<16|b[T>>>8&255]<<8|b[_&255])^m[M++],U=(b[N>>>24]<<24|b[T>>>16&255]<<16|b[_>>>8&255]<<8|b[S&255])^m[M++],w=(b[T>>>24]<<24|b[_>>>16&255]<<16|b[S>>>8&255]<<8|b[N&255])^m[M++];h[A]=j,h[A+1]=W,h[A+2]=U,h[A+3]=w},keySize:256/32});e.AES=s._createHelper(u)}(),t.AES})}(We)),We.exports}var qe={exports:{}},gr;function qi(){return gr||(gr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),v0(),A0(),d0(),Y())})(z,function(t){return function(){var e=t,i=e.lib,s=i.WordArray,c=i.BlockCipher,x=e.algo,p=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],o=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],v=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],g=x.DES=c.extend({_doReset:function(){for(var B=this._key,u=B.words,h=[],A=0;A<56;A++){var m=p[A]-1;h[A]=u[m>>>5]>>>31-m%32&1}for(var E=this._subKeys=[],D=0;D<16;D++){for(var F=E[D]=[],P=o[D],A=0;A<24;A++)F[A/6|0]|=h[(a[A]-1+P)%28]<<31-A%6,F[4+(A/6|0)]|=h[28+(a[A+24]-1+P)%28]<<31-A%6;F[0]=F[0]<<1|F[0]>>>31;for(var A=1;A<7;A++)F[A]=F[A]>>>(A-1)*4+3;F[7]=F[7]<<5|F[7]>>>27}for(var b=this._invSubKeys=[],A=0;A<16;A++)b[A]=E[15-A]},encryptBlock:function(B,u){this._doCryptBlock(B,u,this._subKeys)},decryptBlock:function(B,u){this._doCryptBlock(B,u,this._invSubKeys)},_doCryptBlock:function(B,u,h){this._lBlock=B[u],this._rBlock=B[u+1],f.call(this,4,252645135),f.call(this,16,65535),C.call(this,2,858993459),C.call(this,8,16711935),f.call(this,1,1431655765);for(var A=0;A<16;A++){for(var m=h[A],E=this._lBlock,D=this._rBlock,F=0,P=0;P<8;P++)F|=v[P][((D^m[P])&l[P])>>>0];this._lBlock=D,this._rBlock=E^F}var b=this._lBlock;this._lBlock=this._rBlock,this._rBlock=b,f.call(this,1,1431655765),C.call(this,8,16711935),C.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),B[u]=this._lBlock,B[u+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function f(B,u){var h=(this._lBlock>>>B^this._rBlock)&u;this._rBlock^=h,this._lBlock^=h<<B}function C(B,u){var h=(this._rBlock>>>B^this._lBlock)&u;this._lBlock^=h,this._rBlock^=h<<B}e.DES=c._createHelper(g);var d=x.TripleDES=c.extend({_doReset:function(){var B=this._key,u=B.words;if(u.length!==2&&u.length!==4&&u.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var h=u.slice(0,2),A=u.length<4?u.slice(0,2):u.slice(2,4),m=u.length<6?u.slice(0,2):u.slice(4,6);this._des1=g.createEncryptor(s.create(h)),this._des2=g.createEncryptor(s.create(A)),this._des3=g.createEncryptor(s.create(m))},encryptBlock:function(B,u){this._des1.encryptBlock(B,u),this._des2.decryptBlock(B,u),this._des3.encryptBlock(B,u)},decryptBlock:function(B,u){this._des3.decryptBlock(B,u),this._des2.encryptBlock(B,u),this._des1.decryptBlock(B,u)},keySize:192/32,ivSize:64/32,blockSize:64/32});e.TripleDES=c._createHelper(d)}(),t.TripleDES})}(qe)),qe.exports}var Xe={exports:{}},vr;function Xi(){return vr||(vr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),v0(),A0(),d0(),Y())})(z,function(t){return function(){var e=t,i=e.lib,s=i.StreamCipher,c=e.algo,x=c.RC4=s.extend({_doReset:function(){for(var o=this._key,v=o.words,l=o.sigBytes,g=this._S=[],f=0;f<256;f++)g[f]=f;for(var f=0,C=0;f<256;f++){var d=f%l,B=v[d>>>2]>>>24-d%4*8&255;C=(C+g[f]+B)%256;var u=g[f];g[f]=g[C],g[C]=u}this._i=this._j=0},_doProcessBlock:function(o,v){o[v]^=p.call(this)},keySize:256/32,ivSize:0});function p(){for(var o=this._S,v=this._i,l=this._j,g=0,f=0;f<4;f++){v=(v+1)%256,l=(l+o[v])%256;var C=o[v];o[v]=o[l],o[l]=C,g|=o[(o[v]+o[l])%256]<<24-f*8}return this._i=v,this._j=l,g}e.RC4=s._createHelper(x);var a=c.RC4Drop=x.extend({cfg:x.cfg.extend({drop:192}),_doReset:function(){x._doReset.call(this);for(var o=this.cfg.drop;o>0;o--)p.call(this)}});e.RC4Drop=s._createHelper(a)}(),t.RC4})}(Xe)),Xe.exports}var Je={exports:{}},Ar;function Ji(){return Ar||(Ar=1,function(r,n){(function(t,e,i){r.exports=e(Z(),v0(),A0(),d0(),Y())})(z,function(t){return function(){var e=t,i=e.lib,s=i.StreamCipher,c=e.algo,x=[],p=[],a=[],o=c.Rabbit=s.extend({_doReset:function(){for(var l=this._key.words,g=this.cfg.iv,f=0;f<4;f++)l[f]=(l[f]<<8|l[f]>>>24)&16711935|(l[f]<<24|l[f]>>>8)&4278255360;var C=this._X=[l[0],l[3]<<16|l[2]>>>16,l[1],l[0]<<16|l[3]>>>16,l[2],l[1]<<16|l[0]>>>16,l[3],l[2]<<16|l[1]>>>16],d=this._C=[l[2]<<16|l[2]>>>16,l[0]&4294901760|l[1]&65535,l[3]<<16|l[3]>>>16,l[1]&4294901760|l[2]&65535,l[0]<<16|l[0]>>>16,l[2]&4294901760|l[3]&65535,l[1]<<16|l[1]>>>16,l[3]&4294901760|l[0]&65535];this._b=0;for(var f=0;f<4;f++)v.call(this);for(var f=0;f<8;f++)d[f]^=C[f+4&7];if(g){var B=g.words,u=B[0],h=B[1],A=(u<<8|u>>>24)&16711935|(u<<24|u>>>8)&4278255360,m=(h<<8|h>>>24)&16711935|(h<<24|h>>>8)&4278255360,E=A>>>16|m&4294901760,D=m<<16|A&65535;d[0]^=A,d[1]^=E,d[2]^=m,d[3]^=D,d[4]^=A,d[5]^=E,d[6]^=m,d[7]^=D;for(var f=0;f<4;f++)v.call(this)}},_doProcessBlock:function(l,g){var f=this._X;v.call(this),x[0]=f[0]^f[5]>>>16^f[3]<<16,x[1]=f[2]^f[7]>>>16^f[5]<<16,x[2]=f[4]^f[1]>>>16^f[7]<<16,x[3]=f[6]^f[3]>>>16^f[1]<<16;for(var C=0;C<4;C++)x[C]=(x[C]<<8|x[C]>>>24)&16711935|(x[C]<<24|x[C]>>>8)&4278255360,l[g+C]^=x[C]},blockSize:128/32,ivSize:64/32});function v(){for(var l=this._X,g=this._C,f=0;f<8;f++)p[f]=g[f];g[0]=g[0]+1295307597+this._b|0,g[1]=g[1]+3545052371+(g[0]>>>0<p[0]>>>0?1:0)|0,g[2]=g[2]+886263092+(g[1]>>>0<p[1]>>>0?1:0)|0,g[3]=g[3]+1295307597+(g[2]>>>0<p[2]>>>0?1:0)|0,g[4]=g[4]+3545052371+(g[3]>>>0<p[3]>>>0?1:0)|0,g[5]=g[5]+886263092+(g[4]>>>0<p[4]>>>0?1:0)|0,g[6]=g[6]+1295307597+(g[5]>>>0<p[5]>>>0?1:0)|0,g[7]=g[7]+3545052371+(g[6]>>>0<p[6]>>>0?1:0)|0,this._b=g[7]>>>0<p[7]>>>0?1:0;for(var f=0;f<8;f++){var C=l[f]+g[f],d=C&65535,B=C>>>16,u=((d*d>>>17)+d*B>>>15)+B*B,h=((C&4294901760)*C|0)+((C&65535)*C|0);a[f]=u^h}l[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,l[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,l[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,l[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,l[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,l[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,l[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,l[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=s._createHelper(o)}(),t.Rabbit})}(Je)),Je.exports}var Ve={exports:{}},Er;function Vi(){return Er||(Er=1,function(r,n){(function(t,e,i){r.exports=e(Z(),v0(),A0(),d0(),Y())})(z,function(t){return function(){var e=t,i=e.lib,s=i.StreamCipher,c=e.algo,x=[],p=[],a=[],o=c.RabbitLegacy=s.extend({_doReset:function(){var l=this._key.words,g=this.cfg.iv,f=this._X=[l[0],l[3]<<16|l[2]>>>16,l[1],l[0]<<16|l[3]>>>16,l[2],l[1]<<16|l[0]>>>16,l[3],l[2]<<16|l[1]>>>16],C=this._C=[l[2]<<16|l[2]>>>16,l[0]&4294901760|l[1]&65535,l[3]<<16|l[3]>>>16,l[1]&4294901760|l[2]&65535,l[0]<<16|l[0]>>>16,l[2]&4294901760|l[3]&65535,l[1]<<16|l[1]>>>16,l[3]&4294901760|l[0]&65535];this._b=0;for(var d=0;d<4;d++)v.call(this);for(var d=0;d<8;d++)C[d]^=f[d+4&7];if(g){var B=g.words,u=B[0],h=B[1],A=(u<<8|u>>>24)&16711935|(u<<24|u>>>8)&4278255360,m=(h<<8|h>>>24)&16711935|(h<<24|h>>>8)&4278255360,E=A>>>16|m&4294901760,D=m<<16|A&65535;C[0]^=A,C[1]^=E,C[2]^=m,C[3]^=D,C[4]^=A,C[5]^=E,C[6]^=m,C[7]^=D;for(var d=0;d<4;d++)v.call(this)}},_doProcessBlock:function(l,g){var f=this._X;v.call(this),x[0]=f[0]^f[5]>>>16^f[3]<<16,x[1]=f[2]^f[7]>>>16^f[5]<<16,x[2]=f[4]^f[1]>>>16^f[7]<<16,x[3]=f[6]^f[3]>>>16^f[1]<<16;for(var C=0;C<4;C++)x[C]=(x[C]<<8|x[C]>>>24)&16711935|(x[C]<<24|x[C]>>>8)&4278255360,l[g+C]^=x[C]},blockSize:128/32,ivSize:64/32});function v(){for(var l=this._X,g=this._C,f=0;f<8;f++)p[f]=g[f];g[0]=g[0]+1295307597+this._b|0,g[1]=g[1]+3545052371+(g[0]>>>0<p[0]>>>0?1:0)|0,g[2]=g[2]+886263092+(g[1]>>>0<p[1]>>>0?1:0)|0,g[3]=g[3]+1295307597+(g[2]>>>0<p[2]>>>0?1:0)|0,g[4]=g[4]+3545052371+(g[3]>>>0<p[3]>>>0?1:0)|0,g[5]=g[5]+886263092+(g[4]>>>0<p[4]>>>0?1:0)|0,g[6]=g[6]+1295307597+(g[5]>>>0<p[5]>>>0?1:0)|0,g[7]=g[7]+3545052371+(g[6]>>>0<p[6]>>>0?1:0)|0,this._b=g[7]>>>0<p[7]>>>0?1:0;for(var f=0;f<8;f++){var C=l[f]+g[f],d=C&65535,B=C>>>16,u=((d*d>>>17)+d*B>>>15)+B*B,h=((C&4294901760)*C|0)+((C&65535)*C|0);a[f]=u^h}l[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,l[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,l[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,l[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,l[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,l[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,l[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,l[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=s._createHelper(o)}(),t.RabbitLegacy})}(Ve)),Ve.exports}var Ye={exports:{}},mr;function Yi(){return mr||(mr=1,function(r,n){(function(t,e,i){r.exports=e(Z(),v0(),A0(),d0(),Y())})(z,function(t){return function(){var e=t,i=e.lib,s=i.BlockCipher,c=e.algo;const x=16,p=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],a=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var o={pbox:[],sbox:[]};function v(d,B){let u=B>>24&255,h=B>>16&255,A=B>>8&255,m=B&255,E=d.sbox[0][u]+d.sbox[1][h];return E=E^d.sbox[2][A],E=E+d.sbox[3][m],E}function l(d,B,u){let h=B,A=u,m;for(let E=0;E<x;++E)h=h^d.pbox[E],A=v(d,h)^A,m=h,h=A,A=m;return m=h,h=A,A=m,A=A^d.pbox[x],h=h^d.pbox[x+1],{left:h,right:A}}function g(d,B,u){let h=B,A=u,m;for(let E=x+1;E>1;--E)h=h^d.pbox[E],A=v(d,h)^A,m=h,h=A,A=m;return m=h,h=A,A=m,A=A^d.pbox[1],h=h^d.pbox[0],{left:h,right:A}}function f(d,B,u){for(let D=0;D<4;D++){d.sbox[D]=[];for(let F=0;F<256;F++)d.sbox[D][F]=a[D][F]}let h=0;for(let D=0;D<x+2;D++)d.pbox[D]=p[D]^B[h],h++,h>=u&&(h=0);let A=0,m=0,E=0;for(let D=0;D<x+2;D+=2)E=l(d,A,m),A=E.left,m=E.right,d.pbox[D]=A,d.pbox[D+1]=m;for(let D=0;D<4;D++)for(let F=0;F<256;F+=2)E=l(d,A,m),A=E.left,m=E.right,d.sbox[D][F]=A,d.sbox[D][F+1]=m;return!0}var C=c.Blowfish=s.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var d=this._keyPriorReset=this._key,B=d.words,u=d.sigBytes/4;f(o,B,u)}},encryptBlock:function(d,B){var u=l(o,d[B],d[B+1]);d[B]=u.left,d[B+1]=u.right},decryptBlock:function(d,B){var u=g(o,d[B],d[B+1]);d[B]=u.left,d[B+1]=u.right},blockSize:64/32,keySize:128/32,ivSize:64/32});e.Blowfish=s._createHelper(C)}(),t.Blowfish})}(Ye)),Ye.exports}(function(r,n){(function(t,e,i){r.exports=e(Z(),Q0(),yi(),Fi(),v0(),wi(),A0(),Xt(),be(),_i(),Qt(),Si(),ki(),Ri(),Se(),Li(),d0(),Y(),Pi(),Ni(),Ti(),Hi(),zi(),Mi(),Oi(),Zi(),Gi(),ji(),Ui(),Wi(),qi(),Xi(),Ji(),Vi(),Yi())})(z,function(t){return t})})(zt);var Qi=zt.exports;const Ki=Bi(Qi),E0=class E0{constructor(){G(this,"status","not_loaded");G(this,"loadPromise",null)}static getInstance(){return E0.instance||(E0.instance=new E0),E0.instance}async loadSDK(){var n;if(this.status==="loaded"&&((n=window.ww)!=null&&n.checkJsApi))return!0;if(this.status==="loading"&&this.loadPromise)return this.loadPromise;this.status="loading",this.loadPromise=this.loadSDKScript();try{const t=await this.loadPromise;return this.status=t?"loaded":"error",t}catch(t){return this.status="error",console.error("企业微信SDK加载失败:",t),!1}}loadSDKScript(){return new Promise(n=>{var e;if((e=window.ww)!=null&&e.checkJsApi){n(!0);return}const t=document.createElement("script");t.src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js",t.async=!0,t.onload=()=>{var i;(i=window.ww)!=null&&i.checkJsApi?(console.log("企业微信SDK加载成功"),n(!0)):(console.warn("企业微信SDK加载后未找到预期的API"),n(!1))},t.onerror=()=>{console.error("企业微信SDK加载失败"),n(!1)},document.head.appendChild(t),setTimeout(()=>{this.status==="loading"&&(console.warn("企业微信SDK加载超时"),n(!1))},1e4)})}isLoaded(){var n;return this.status==="loaded"&&!!((n=window.ww)!=null&&n.checkJsApi)}getStatus(){return this.status}};G(E0,"instance");let Qe=E0;const D0=class D0 extends HTMLElement{constructor(){super();G(this,"_shadowRoot");G(this,"_ballEl",null);G(this,"_iconEl",null);G(this,"_panelEl",null);G(this,"_maskEl",null);G(this,"_closeBtn",null);G(this,"_scrollTimeout",null);G(this,"_wxSDKManager");G(this,"_panelEvents",{});G(this,"_isLoading",!1);G(this,"_appId",null);G(this,"_userId",null);G(this,"_baseURL",null);G(this,"_lastLoadedParams",null);G(this,"_isHidden",!1);G(this,"_corpId",null);G(this,"_agentId",null);G(this,"_handleScroll",()=>{let t=0;return e=>{const i=e.target,s=i instanceof Document?i.documentElement.scrollTop:i.scrollTop;if(s!==t){if(this._ballEl){const c=this._ballEl.clientWidth??0;this._ballEl.style.right=`${-c/2-10}px`}this._togglePanel(!0),t=s,this._scrollTimeout&&window.clearTimeout(this._scrollTimeout),this._scrollTimeout=window.setTimeout(()=>{this._ballEl&&this._updateBallPosition()},200)}}});G(this,"_scrollHandler",this._handleScroll());this._shadowRoot=this.attachShadow({mode:"open"}),this._wxSDKManager=Qe.getInstance()}static get observedAttributes(){return["right","bottom","panel-events","app-id","user-id","base-url"]}connectedCallback(){if(this._checkIfHidden()){this._isHidden=!0;return}this._render(),this._attachEventListeners()}disconnectedCallback(){this._removeEventListeners()}attributeChangedCallback(t,e,i){if(e!==i)switch(t){case"panel-events":this._updatePanelEvents(i);break;case"app-id":this._appId=i,this._checkAndLoadData();break;case"user-id":this._userId=i,this._checkAndLoadData();break;case"base-url":this._updateBaseURL(i);break;case"right":case"bottom":this._updateBallPosition();break}}get panelEvents(){return this._panelEvents}set panelEvents(t){if(typeof t=="string")try{const e=JSON.parse(t);this.setAttribute("panel-events",t),this._panelEvents=e}catch(e){console.error("Invalid JSON for panel-events attribute.",e),this._panelEvents={}}else this._panelEvents=t,this.setAttribute("panel-events",JSON.stringify(t));this._initPanelItems()}get appId(){return this._appId}set appId(t){this._appId=t,t?this.setAttribute("app-id",t):this.removeAttribute("app-id"),this._checkAndLoadData()}get userId(){return this._userId}set userId(t){this._userId=t,t?this.setAttribute("user-id",t):this.removeAttribute("user-id"),this._checkAndLoadData()}get baseURL(){return this._baseURL}set baseURL(t){this._baseURL=t,t?this.setAttribute("base-url",t):this.removeAttribute("base-url"),this._updateBaseURL(t)}get isLoading(){return this._isLoading}_render(){this._shadowRoot.innerHTML=`
      <style>
        :host {
          --bg-image-url: url('${Or}');
        }
        ${Gr}
        
        /* 关闭按钮样式 */
        .floating-ball-close {
          position: absolute;
          top: -8px;
          right: 0px;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 1000;
        }
        
        
        .floating-ball-close img {
          width: 18px;
          height: 18px;
        }
        
        /* 隐藏状态 */
        :host(.hidden) {
          display: none !important;
        }
      </style>
      <div class="floating-ball">
        <img class="icon" src=${K} />
        <div class="floating-ball-close">
          <img src="${Zr}" alt="关闭" />
        </div>
        <div class="floating-panel hidden"></div>
      </div>
      <div class="floating-ball-mask hidden"></div>
    `,this._ballEl=this._shadowRoot.querySelector(".floating-ball"),this._iconEl=this._shadowRoot.querySelector(".icon"),this._panelEl=this._shadowRoot.querySelector(".floating-panel"),this._maskEl=this._shadowRoot.querySelector(".floating-ball-mask"),this._closeBtn=this._shadowRoot.querySelector(".floating-ball-close"),this._updateBallPosition(),this._initializeAttributes()}_initializeAttributes(){const t=this.getAttribute("app-id"),e=this.getAttribute("user-id"),i=this.getAttribute("base-url");this._appId=t,this._userId=e,this._baseURL=i,i&&this._updateBaseURL(i),this._checkAndLoadData(),t||this._updatePanelEvents(this.getAttribute("panel-events"))}_updateBaseURL(t){this._baseURL=t,t&&l0.updateBaseURL(t)}_checkAndLoadData(){if(this._appId&&this._userId&&this._baseURL&&!this._isLoading){const t=`${this._appId}|${this._userId}|${this._baseURL}`;if(this._lastLoadedParams===t)return;this._lastLoadedParams=t,this._loadDataByAppId(this._appId)}else this._appId||(this._panelEvents={},this._lastLoadedParams=null,this._initPanelItems())}async _loadDataByAppId(t){var e,i,s,c,x,p,a,o,v,l,g,f,C,d,B;if(!this._isLoading)try{if(this._isLoading=!0,this._showLoadingState(),this._userId){const E=await hi(t,this._userId);(e=E==null?void 0:E.data)!=null&&e.token&&l0.updateToken(E.data.token),(i=E==null?void 0:E.data)!=null&&i.corpId&&(this._corpId=E.data.corpId),(s=E==null?void 0:E.data)!=null&&s.agentId&&(this._agentId=E.data.agentId)}const u=await pi(t),h=await gi("app.score.on");((c=h==null?void 0:h.data)==null?void 0:c.configValue)==="true"&&!((x=u.data)!=null&&x.config.items.find(E=>E.type==="score"))&&((p=u.data)==null||p.config.items.push({type:"score",name:"应用评分"})),((a=h==null?void 0:h.data)==null?void 0:a.configValue)==="false"&&((o=u.data)!=null&&o.config.items.find(E=>E.type==="score"))&&(u.data.config.items=u.data.config.items.filter(E=>E.type!=="score"));const A=(g=(l=(v=u.data)==null?void 0:v.config)==null?void 0:l.menus)==null?void 0:g.map(E=>E.type);let m={};A!=null&&A.length&&(m=await Ci({appId:t,menus:A})),u&&u.data?(m&&(m!=null&&m.data)&&((d=(C=(f=u.data)==null?void 0:f.config)==null?void 0:C.menus)==null||d.forEach(E=>{var D,F,P,b,I,_;E.icon=E.type==="1"?zr:E.type==="2"?Hr:Mr,E.label=E.type==="1"?"应用负责人：":E.type==="2"?"相关部门：":((D=m==null?void 0:m.data)==null?void 0:D.deptName)+"：",E.value=E.type==="1"?(F=m==null?void 0:m.data)==null?void 0:F.managerName:E.type==="2"?(P=m==null?void 0:m.data)==null?void 0:P.deptName:(b=m==null?void 0:m.data)==null?void 0:b.managerPhone,E.managerId=(I=m==null?void 0:m.data)==null?void 0:I.managerId,E.managerPhone=(_=m==null?void 0:m.data)==null?void 0:_.managerPhone})),this._panelEvents=(B=u.data)==null?void 0:B.config):(console.warn("No panelEvents found in API response",u),this._panelEvents={}),this._initPanelItems()}catch(u){console.error("Failed to load data for appId:",t,u),this._panelEvents={},this._showErrorState()}finally{this._isLoading=!1}}_showLoadingState(){this._panelEl&&(this._panelEl.innerHTML=`
      <div class="loading-state" style="padding: 16px; text-align: center; color: #666;">
        加载中...
      </div>
      <div class="arrow"></div>
    `)}_showErrorState(){this._panelEl&&(this._panelEl.innerHTML=`
      <div class="error-state" style="padding: 16px; text-align: center; color: #f56565;">
        数据加载失败
      </div>
      <div class="arrow"></div>
    `)}_updatePanelEvents(t){if(!this._appId){if(t)try{this._panelEvents=JSON.parse(t)}catch(e){console.error("Invalid JSON for panel-events attribute.",e),this._panelEvents={}}else this._panelEvents={};this._initPanelItems()}}_updateBallPosition(){if(!this._ballEl)return;const t=this.getAttribute("right")??"0",e=this.getAttribute("bottom")??"45";this._ballEl.style.right=`${t}px`,this._ballEl.style.bottom=`${e}px`}_initPanelItems(){var i,s,c,x,p,a,o,v;if(!this._panelEl)return;const t=document.createDocumentFragment();if((s=(i=this._panelEvents)==null?void 0:i.items)==null||s.forEach((l,g)=>{var d,B;const f=g===(((B=(d=this._panelEvents)==null?void 0:d.items)==null?void 0:B.length)??0)-1,C=this._createPanelItem(l,f);t.appendChild(C)}),(x=Object.keys(((c=this._panelEvents)==null?void 0:c.menus)||{}))!=null&&x.length){const l=document.createElement("div");l.className="attr-panel",(a=(p=this._panelEvents)==null?void 0:p.menus)==null||a.forEach(g=>{const f=this._createAttrPanelItem(g);l.appendChild(f)}),t.appendChild(l)}this._panelEl.innerHTML="",this._panelEl.appendChild(t);const e=document.createElement("div");(v=Object.keys(((o=this._panelEvents)==null?void 0:o.menus)||{}))!=null&&v.length?e.className="arrow blue":e.className="arrow",this._panelEl.appendChild(e)}_createPanelItem(t,e){const i=document.createElement("div");i.className="event-item",e&&i.classList.add("no_border");const s=["link","app","score"].includes(t.type);return i.innerHTML=`
      <img class="icon_radio" src="${Nr}"/>
      <div class="event-item__text">${t.name}</div>
      ${s?`<img src="${Tr}" class="icon_arrow"/>`:""}
    `,s&&(i.style.cursor="pointer",i.addEventListener("click",c=>{c.stopPropagation(),this._togglePanel(),t.type==="score"?this._showScoreModal():window.location.href=t.url??""})),i}_createAttrPanelItem(t){const e=document.createElement("div");return e.className="attr-item",e.innerHTML=`
      <img class="icon_radio" src="${t.icon}"/>
      <div class="attr-item__text ${"text"+t.type}">${t.label}</div>
      <div class="attr-item__text_right ${"value"+t.type}">${t.value}</div>
    `,(t.type==="1"||t.type==="3")&&(e.style.cursor="pointer",e.addEventListener("click",async i=>{if(i.stopPropagation(),this._togglePanel(),t.type==="1"){if(t.managerId){await this._openEnterpriseChat(t.managerId);return}}else if(t.type==="3"){const s=`tel:${t.managerPhone}`;window.location.href=s}})),e}_togglePanel(t){if(!this._panelEl||!this._maskEl||!this._iconEl)return;const e=this._panelEl.classList.contains("hidden");if(t===!0){if(e)return;this._panelEl.classList.add("hidden"),this._maskEl.classList.add("hidden"),this._iconEl.setAttribute("src",K)}else{this._panelEl.classList.toggle("hidden"),this._maskEl.classList.toggle("hidden");const i=this._panelEl.classList.contains("hidden");this._iconEl.setAttribute("src",i?K:m0)}}_attachEventListeners(){var t,e,i;(t=this._iconEl)==null||t.addEventListener("click",()=>this._togglePanel()),(e=this._maskEl)==null||e.addEventListener("click",()=>this._togglePanel()),(i=this._closeBtn)==null||i.addEventListener("click",s=>{s.stopPropagation(),this._hideFloatingBall()}),document.addEventListener("scroll",this._scrollHandler,!0)}_removeEventListeners(){document.removeEventListener("scroll",this._scrollHandler,!0)}_showScoreModal(){if(!this._appId){alert("缺少应用ID，无法显示评分");return}new mi({appId:this._appId,onSubmitSuccess:e=>{console.log("评分提交成功:",e)},onClose:()=>{console.log("评分弹框已关闭")}}).show()}_checkIfHidden(){try{return sessionStorage.getItem(D0.HIDDEN_KEY)==="true"}catch(t){return console.warn("无法访问sessionStorage:",t),!1}}_hideFloatingBall(){this._isHidden=!0,this.classList.add("hidden");try{sessionStorage.setItem(D0.HIDDEN_KEY,"true")}catch(t){console.warn("无法设置sessionStorage:",t)}this.dispatchEvent(new CustomEvent("floating-ball-hidden",{detail:{timestamp:Date.now()}}))}_showFloatingBall(){this._isHidden=!1,this.classList.remove("hidden");try{sessionStorage.removeItem(D0.HIDDEN_KEY)}catch(t){console.warn("无法清除sessionStorage:",t)}this.dispatchEvent(new CustomEvent("floating-ball-shown",{detail:{timestamp:Date.now()}}))}show(){this._isHidden&&this._showFloatingBall()}hide(){this._isHidden||this._hideFloatingBall()}get isHidden(){return this._isHidden}async _openEnterpriseChat(t){try{if(!await this._wxSDKManager.loadSDK()||!this._wxSDKManager.isLoaded()){console.warn("企业微信SDK未能正确加载，无法使用联系功能");return}window.ww.checkJsApi({jsApiList:["openEnterpriseChat"],success:i=>{console.log("权限检查结果:",i),i.checkResult&&i.checkResult.openEnterpriseChat?this._callOpenEnterpriseChat(t):this._registerAndUseEnterpriseChat(t)},fail:i=>{console.error("权限检查失败:",i),this._registerAndUseEnterpriseChat(t)}})}catch(e){console.error("打开企业微信联系时发生错误:",e)}}async _registerAndUseEnterpriseChat(t){try{if(!this._corpId||!this._agentId){console.warn("缺少企业微信配置信息（corpId或agentId），无法注册SDK");return}const e={corpId:this._corpId,agentId:this._agentId,timestamp:Date.now(),nonceStr:Math.random().toString(36).substr(2,15),jsApiList:["openEnterpriseChat"]},{data:i}=await Ei({agentId:this._agentId,corpId:this._corpId});await window.ww.register({...e,getConfigSignature:c=>this.shaTheSignature({nonceStr:e.nonceStr,timestamp:e.timestamp,url:c}),getAgentConfigSignature:c=>this.shaTheSignature({nonceStr:e.nonceStr,timestamp:e.timestamp,url:c,jsapiTicket:i})});const s=this;setTimeout(()=>{s._callOpenEnterpriseChat(t)},500)}catch(e){console.error("注册企业微信时发生错误:",e)}}_callOpenEnterpriseChat(t){console.log("开始调用企业微信联系功能"),window.ww.openEnterpriseChat({userIds:t,success:e=>{console.log("企业微信联系成功:",e)},fail:e=>{console.error("企业微信联系失败:",e)}})}shaTheSignature(t){const{jsapiTicket:e,nonceStr:i,timestamp:s,url:c}=t,x=[{key:"noncestr",value:i},{key:"timestamp",value:s},{key:"url",value:c}];e&&x.unshift({key:"jsapi_ticket",value:e});const p=x.sort((o,v)=>o.key.localeCompare(v.key)).map(o=>`${o.key}=${o.value}`).join("&"),a=Ki.SHA1(p).toString();return{timestamp:s,nonceStr:i,signature:a}}};G(D0,"HIDDEN_KEY","floating-ball-hidden");let Ke=D0;customElements.get("floating-ball")||customElements.define("floating-ball",Ke);const f0=class f0{static create(n){if(!n.appId||!n.userId||!n.baseUrl)throw new Error("FloatingBall: appId, userId, baseUrl 是必需的参数");const t=`floating-ball-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,e=document.createElement("floating-ball");e.setAttribute("app-id",n.appId),e.setAttribute("user-id",n.userId),e.setAttribute("base-url",n.baseUrl),n.right!==void 0&&e.setAttribute("right",n.right.toString()),n.bottom!==void 0&&e.setAttribute("bottom",n.bottom.toString()),n.panelEvents&&(e.panelEvents=n.panelEvents),e.setAttribute("data-instance-id",t),(n.container||document.body).appendChild(e);const s={element:e,show(){e.show()},hide(){e.hide()},destroy(){e.parentNode&&e.parentNode.removeChild(e),f0.instances.delete(t)},get isHidden(){return e.isHidden},updateOptions(c){c.appId&&e.setAttribute("app-id",c.appId),c.userId&&e.setAttribute("user-id",c.userId),c.baseUrl&&e.setAttribute("base-url",c.baseUrl),c.right!==void 0&&e.setAttribute("right",c.right.toString()),c.bottom!==void 0&&e.setAttribute("bottom",c.bottom.toString()),c.panelEvents&&(e.panelEvents=c.panelEvents)},on(c,x){e.addEventListener(c,x)},off(c,x){e.removeEventListener(c,x)}};return f0.instances.set(t,s),s}static getAllInstances(){return Array.from(f0.instances.values())}static destroyAll(){f0.instances.forEach(n=>{n.destroy()}),f0.instances.clear()}static findInstanceByElement(n){const t=n.getAttribute("data-instance-id");return t&&f0.instances.get(t)||null}};G(f0,"instances",new Map);let K0=f0;function $i(r){return K0.create(r)}r0.FloatingBallFactory=K0,r0.createFloatingBall=$i,Object.defineProperty(r0,Symbol.toStringTag,{value:"Module"})});
